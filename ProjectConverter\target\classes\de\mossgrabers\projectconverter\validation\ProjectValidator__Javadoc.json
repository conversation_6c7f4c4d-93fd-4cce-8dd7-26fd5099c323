{"doc": " Validator for DAW project structure and content.\n Validates project integrity, structure, and potential conversion issues.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "createDefault", "paramTypes": [], "doc": " Create a validator with default settings.\n \n @return Default project validator\n"}, {"name": "createStrict", "paramTypes": [], "doc": " Create a strict validator.\n \n @return Strict project validator\n"}], "constructors": [{"name": "<init>", "paramTypes": ["boolean", "java.util.Set"], "doc": " Constructor.\n \n @param strictMode Whether to apply strict validation rules\n @param supportedPluginFormats Set of supported plugin formats (VST2, VST3, CLAP, etc.)\n"}]}