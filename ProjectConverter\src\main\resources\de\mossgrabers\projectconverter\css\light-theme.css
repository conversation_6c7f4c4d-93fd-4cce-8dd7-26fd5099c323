/* Light Theme for ProjectConverter */
/* Modern Material Design inspired styling */

:root {
    /* Color palette */
    --primary-color: #1976d2;
    --primary-variant: #1565c0;
    --secondary-color: #03dac6;
    --secondary-variant: #018786;
    
    /* Surface colors */
    --background: #ffffff;
    --surface: #f5f5f5;
    --surface-variant: #e0e0e0;
    --outline: #cccccc;
    
    /* Text colors */
    --on-background: #000000;
    --on-surface: #212121;
    --on-surface-variant: #666666;
    --on-primary: #ffffff;
    --on-secondary: #000000;
    
    /* State colors */
    --error: #d32f2f;
    --warning: #f57c00;
    --success: #388e3c;
    --info: #1976d2;
    
    /* Interactive states */
    --hover: rgba(0, 0, 0, 0.04);
    --pressed: rgba(0, 0, 0, 0.08);
    --focus: rgba(25, 118, 210, 0.12);
    --selected: rgba(25, 118, 210, 0.08);
    --disabled: rgba(0, 0, 0, 0.38);
    
    /* Shadows */
    --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    
    /* Border radius */
    --border-radius-small: 4px;
    --border-radius-medium: 8px;
    --border-radius-large: 12px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Typography */
    --font-family: -fx-font-family;
    --font-size-small: 0.875em;
    --font-size-medium: 1em;
    --font-size-large: 1.125em;
    --font-size-xlarge: 1.25em;
}

/* Root styling */
.root {
    -fx-base: var(--background);
    -fx-background: var(--background);
    -fx-control-inner-background: var(--surface);
    -fx-accent: var(--primary-color);
    -fx-default-button: var(--primary-color);
    -fx-focus-color: var(--primary-color);
    -fx-faint-focus-color: var(--focus);
    -fx-text-fill: var(--on-background);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-font-size: 13px;
}

/* Buttons */
.button {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
    -fx-border-radius: var(--border-radius-medium);
    -fx-background-radius: var(--border-radius-medium);
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-sm) var(--spacing-md);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: var(--hover);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 4, 0, 0, 2);
}

.button:pressed {
    -fx-background-color: var(--pressed);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 1, 0, 0, 0);
}

.button:focused {
    -fx-border-color: var(--primary-color);
    -fx-border-width: 2px;
}

.button:disabled {
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

/* Primary button */
.button.primary, .button:default {
    -fx-background-color: var(--primary-color);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--primary-color);
}

.button.primary:hover, .button:default:hover {
    -fx-background-color: var(--primary-variant);
    -fx-border-color: var(--primary-variant);
}

/* Text fields and combo boxes */
.text-field, .combo-box {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-sm);
}

.text-field:focused, .combo-box:focused {
    -fx-border-color: var(--primary-color);
    -fx-border-width: 2px;
}

/* Tab pane */
.tab-pane {
    -fx-background-color: var(--background);
}

.tab-pane .tab-header-area {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 0 0 1px 0;
}

.tab-pane .tab {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-text-fill: var(--on-surface-variant);
    -fx-padding: var(--spacing-sm) var(--spacing-md);
}

.tab-pane .tab:selected {
    -fx-background-color: var(--selected);
    -fx-text-fill: var(--primary-color);
    -fx-border-color: var(--primary-color);
    -fx-border-width: 0 0 2px 0;
}

.tab-pane .tab:hover {
    -fx-background-color: var(--hover);
}

/* Progress bar */
.progress-bar {
    -fx-background-color: var(--surface-variant);
    -fx-border-radius: var(--border-radius-large);
    -fx-background-radius: var(--border-radius-large);
}

.progress-bar .bar {
    -fx-background-color: var(--primary-color);
    -fx-background-radius: var(--border-radius-large);
    -fx-padding: 2px;
}

/* Check box */
.check-box {
    -fx-text-fill: var(--on-surface);
}

.check-box .box {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.check-box:selected .box {
    -fx-background-color: var(--primary-color);
    -fx-border-color: var(--primary-color);
}

.check-box:selected .mark {
    -fx-background-color: var(--on-primary);
}

/* Scroll pane */
.scroll-pane {
    -fx-background-color: var(--background);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.scroll-bar {
    -fx-background-color: var(--surface);
}

.scroll-bar .thumb {
    -fx-background-color: var(--outline);
    -fx-background-radius: var(--border-radius-small);
}

.scroll-bar .thumb:hover {
    -fx-background-color: var(--on-surface-variant);
}

/* Text area */
.text-area {
    -fx-background-color: var(--surface);
    -fx-text-fill: var(--on-surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.text-area:focused {
    -fx-border-color: var(--primary-color);
    -fx-border-width: 2px;
}

/* Tooltips */
.tooltip {
    -fx-background-color: var(--on-surface);
    -fx-text-fill: var(--surface);
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
    -fx-padding: var(--spacing-xs) var(--spacing-sm);
    -fx-font-size: var(--font-size-small);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 4, 0, 0, 2);
}

/* Menu and context menu */
.menu-bar {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 0 0 1px 0;
}

.menu-item {
    -fx-background-color: transparent;
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-xs) var(--spacing-md);
}

.menu-item:hover {
    -fx-background-color: var(--hover);
}

.menu-item:focused {
    -fx-background-color: var(--selected);
}

/* Separator */
.separator {
    -fx-background-color: var(--outline);
}

/* Labels */
.label {
    -fx-text-fill: var(--on-surface);
}

.label.title {
    -fx-font-size: var(--font-size-large);
    -fx-font-weight: bold;
}

.label.subtitle {
    -fx-text-fill: var(--on-surface-variant);
    -fx-font-size: var(--font-size-small);
}

/* Error states */
.error {
    -fx-text-fill: var(--error);
    -fx-border-color: var(--error);
}

.warning {
    -fx-text-fill: var(--warning);
    -fx-border-color: var(--warning);
}

.success {
    -fx-text-fill: var(--success);
    -fx-border-color: var(--success);
}

.info {
    -fx-text-fill: var(--info);
    -fx-border-color: var(--info);
}

/* Custom classes */
.card {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
    -fx-border-radius: var(--border-radius-medium);
    -fx-background-radius: var(--border-radius-medium);
    -fx-padding: var(--spacing-md);
    -fx-effect: var(--shadow-1);
}

.elevated {
    -fx-effect: var(--shadow-2);
}

.floating {
    -fx-effect: var(--shadow-3);
}

/* Focus indicators for accessibility */
.button:focused,
.text-field:focused,
.combo-box:focused,
.check-box:focused {
    -fx-effect: dropshadow(gaussian, var(--primary-color), 3, 0.5, 0, 0);
}

/* High contrast support */
@media (prefers-contrast: high) {
    :root {
        --outline: #000000;
        --on-surface-variant: #000000;
    }
}
