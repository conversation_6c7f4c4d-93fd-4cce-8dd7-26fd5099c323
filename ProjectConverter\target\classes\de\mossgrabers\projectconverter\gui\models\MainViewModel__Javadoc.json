{"doc": " Main view model for the ProjectConverter application.\n Manages the overall application state, file selection, and conversion settings.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "initializeServices", "paramTypes": ["de.mossgrabers.projectconverter.services.ErrorHandler"], "doc": " Initialize the view model with required services.\n \n @param errorHandler The error handler service\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "getSelectedSourceFormat", "paramTypes": [], "doc": " Get the currently selected source format.\n \n @return The selected source format, or null if none selected\n"}, {"name": "getSelectedDestinationFormat", "paramTypes": [], "doc": " Get the currently selected destination format.\n \n @return The selected destination format, or null if none selected\n"}, {"name": "updateSourcePathHistory", "paramTypes": ["java.lang.String"], "doc": " Update the source path history.\n \n @param path The path to add to history\n"}, {"name": "updateDestinationPathHistory", "paramTypes": ["java.lang.String"], "doc": " Update the destination path history.\n \n @param path The path to add to history\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}]}