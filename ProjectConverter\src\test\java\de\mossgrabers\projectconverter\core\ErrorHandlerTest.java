package de.mossgrabers.projectconverter.core;

import de.mossgrabers.projectconverter.INotifier;
import de.mossgrabers.projectconverter.services.ErrorHandler;
import de.mossgrabers.projectconverter.services.ErrorRecord;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the ErrorHandler class.
 * 
 * <AUTHOR> ProjectConverter
 */
class ErrorHandlerTest
{
    @Mock
    private INotifier mockNotifier;
    
    private ErrorHandler errorHandler;
    
    @BeforeEach
    void setUp()
    {
        MockitoAnnotations.openMocks(this);
        this.errorHandler = new ErrorHandler(this.mockNotifier);
    }
    
    @Test
    void testHandleRecoverableException()
    {
        // Given
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.INVALID_CONFIGURATION,
            "Test recoverable error",
            "Technical details",
            true
        );
        
        // When
        final boolean recovered = this.errorHandler.handleException(exception);
        
        // Then
        assertTrue(recovered, "Should attempt recovery for recoverable exceptions");
        
        // Verify error was recorded
        final List<ErrorRecord> history = this.errorHandler.getErrorHistory();
        assertEquals(1, history.size());
        assertEquals(ErrorCode.INVALID_CONFIGURATION, history.get(0).getErrorCode());
    }
    
    @Test
    void testHandleNonRecoverableException()
    {
        // Given
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.FILE_NOT_FOUND,
            "Test non-recoverable error",
            "Technical details",
            false
        );
        
        // When
        final boolean recovered = this.errorHandler.handleException(exception);
        
        // Then
        assertFalse(recovered, "Should not recover from non-recoverable exceptions");
        
        // Verify user was notified
        verify(this.mockNotifier).logError("Test non-recoverable error");
    }
    
    @Test
    void testHandleThrowableWithFileNotFoundException()
    {
        // Given
        final FileNotFoundException exception = new FileNotFoundException("File not found");
        final String context = "reading project file";
        
        // When
        final boolean recovered = this.errorHandler.handleThrowable(exception, context);
        
        // Then
        assertFalse(recovered, "FileNotFoundException should not be recoverable");
        
        // Verify error was recorded with correct error code
        final List<ErrorRecord> history = this.errorHandler.getErrorHistory();
        assertEquals(1, history.size());
        assertEquals(ErrorCode.FILE_NOT_FOUND, history.get(0).getErrorCode());
    }
    
    @Test
    void testHandleThrowableWithIOException()
    {
        // Given
        final IOException exception = new IOException("Access denied");
        final String context = "writing output file";
        
        // When
        final boolean recovered = this.errorHandler.handleThrowable(exception, context);
        
        // Then
        assertFalse(recovered, "IOException should not be recoverable");
        
        // Verify error was recorded with correct error code
        final List<ErrorRecord> history = this.errorHandler.getErrorHistory();
        assertEquals(1, history.size());
        assertEquals(ErrorCode.FILE_ACCESS_DENIED, history.get(0).getErrorCode());
    }
    
    @Test
    void testHandleThrowableWithOutOfMemoryError()
    {
        // Given
        final OutOfMemoryError error = new OutOfMemoryError("Java heap space");
        final String context = "processing large file";
        
        // When
        final boolean recovered = this.errorHandler.handleThrowable(error, context);
        
        // Then
        assertFalse(recovered, "OutOfMemoryError should not be recoverable");
        
        // Verify error was recorded with correct error code
        final List<ErrorRecord> history = this.errorHandler.getErrorHistory();
        assertEquals(1, history.size());
        assertEquals(ErrorCode.OUT_OF_MEMORY, history.get(0).getErrorCode());
    }
    
    @Test
    void testErrorListener()
    {
        // Given
        final AtomicReference<ProjectConverterException> capturedError = new AtomicReference<>();
        this.errorHandler.addErrorListener(capturedError::set);
        
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.CONVERSION_FAILED,
            "Test error for listener",
            "Technical details",
            false
        );
        
        // When
        this.errorHandler.handleException(exception);
        
        // Then
        assertNotNull(capturedError.get(), "Error listener should be notified");
        assertEquals(exception, capturedError.get());
    }
    
    @Test
    void testRemoveErrorListener()
    {
        // Given
        final AtomicReference<ProjectConverterException> capturedError = new AtomicReference<>();
        this.errorHandler.addErrorListener(capturedError::set);
        this.errorHandler.removeErrorListener(capturedError::set);
        
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.CONVERSION_FAILED,
            "Test error",
            "Technical details",
            false
        );
        
        // When
        this.errorHandler.handleException(exception);
        
        // Then
        assertNull(capturedError.get(), "Removed listener should not be notified");
    }
    
    @Test
    void testErrorHistoryLimit()
    {
        // Given - Create more than 1000 errors to test history limit
        for (int i = 0; i < 1005; i++)
        {
            final ProjectConverterException exception = new ProjectConverterException(
                ErrorCode.INTERNAL_ERROR,
                "Test error " + i,
                "Technical details " + i,
                false
            );
            this.errorHandler.handleException(exception);
        }
        
        // Then
        final List<ErrorRecord> history = this.errorHandler.getErrorHistory();
        assertEquals(1000, history.size(), "Error history should be limited to 1000 entries");
        
        // Verify oldest entries were removed (should start from error 5)
        assertTrue(history.get(0).getUserMessage().contains("Test error 5"));
    }
    
    @Test
    void testClearErrorHistory()
    {
        // Given
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.INTERNAL_ERROR,
            "Test error",
            "Technical details",
            false
        );
        this.errorHandler.handleException(exception);
        
        // Verify error was recorded
        assertEquals(1, this.errorHandler.getErrorHistory().size());
        
        // When
        this.errorHandler.clearErrorHistory();
        
        // Then
        assertEquals(0, this.errorHandler.getErrorHistory().size());
    }
    
    @Test
    void testErrorListenerException()
    {
        // Given - Add a listener that throws an exception
        this.errorHandler.addErrorListener(error -> {
            throw new RuntimeException("Listener error");
        });
        
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.INTERNAL_ERROR,
            "Test error",
            "Technical details",
            false
        );
        
        // When/Then - Should not throw exception even if listener fails
        assertDoesNotThrow(() -> this.errorHandler.handleException(exception));
        
        // Verify error was still recorded
        assertEquals(1, this.errorHandler.getErrorHistory().size());
    }
    
    @Test
    void testFormattedErrorMessage()
    {
        // Given
        final ProjectConverterException exception = new ProjectConverterException(
            ErrorCode.CONVERSION_FAILED,
            "User message",
            "Technical details",
            true
        );
        
        // When
        final String formatted = exception.getFormattedMessage();
        
        // Then
        assertTrue(formatted.contains("CONVERSION_FAILED"));
        assertTrue(formatted.contains("User message"));
        assertTrue(formatted.contains("Technical details"));
        assertTrue(formatted.contains("Recoverable: true"));
    }
    
    @Test
    void testErrorCodeProperties()
    {
        // Test error code properties
        assertEquals(1001, ErrorCode.FILE_NOT_FOUND.getCode());
        assertEquals(ErrorSeverity.ERROR, ErrorCode.FILE_NOT_FOUND.getSeverity());
        assertEquals("File not found", ErrorCode.FILE_NOT_FOUND.getDescription());
        assertEquals("File I/O", ErrorCode.FILE_NOT_FOUND.getCategory());
        
        // Test different categories
        assertEquals("Validation", ErrorCode.INVALID_PROJECT_FORMAT.getCategory());
        assertEquals("Conversion", ErrorCode.CONVERSION_FAILED.getCategory());
        assertEquals("Memory/Performance", ErrorCode.OUT_OF_MEMORY.getCategory());
    }
    
    @Test
    void testErrorCodeToString()
    {
        // Given
        final ErrorCode errorCode = ErrorCode.FILE_NOT_FOUND;
        
        // When
        final String toString = errorCode.toString();
        
        // Then
        assertTrue(toString.contains("File I/O-1001"));
        assertTrue(toString.contains("File not found"));
        assertTrue(toString.contains("ERROR"));
    }
}
