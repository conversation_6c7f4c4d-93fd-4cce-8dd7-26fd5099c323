{"doc": "\n Interface to notify the user about notification messages.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "log", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "\n Log the message to the notifier.\r\n\r\n @param messageID The ID of the message to get\r\n @param replaceStrings Replaces the %1..%n in the message with the strings\r\n"}, {"name": "logError", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "\n Log the message to the notifier.\r\n\r\n @param messageID The ID of the message to get\r\n @param replaceStrings Replaces the %1..%n in the message with the strings\r\n"}, {"name": "logError", "paramTypes": ["java.lang.String", "java.lang.Throwable"], "doc": "\n Log the message to the notifier.\r\n\r\n @param messageID The ID of the message to get\r\n @param throwable A throwable\r\n"}, {"name": "logError", "paramTypes": ["java.lang.Throwable"], "doc": "\n Log the message to the notifier.\r\n\r\n @param throwable A throwable\r\n"}, {"name": "updateButtonStates", "paramTypes": ["boolean"], "doc": "\n Update the button execution states.\r\n\r\n @param canClose Execution can be closed\r\n"}, {"name": "isCancelled", "paramTypes": [], "doc": "\n Check if the process should be cancelled.\r\n\r\n @return True if the process should be cancelled.\r\n"}], "constructors": []}