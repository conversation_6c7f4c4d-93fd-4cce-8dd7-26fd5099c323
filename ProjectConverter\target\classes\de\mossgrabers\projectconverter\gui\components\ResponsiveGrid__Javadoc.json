{"doc": " Responsive grid component that automatically adjusts column count and layout\n based on screen size and breakpoints.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "setupDefaultColumnConfiguration", "paramTypes": [], "doc": " Set up default column configuration for different breakpoints.\n"}, {"name": "setupResponsiveBehavior", "paramTypes": [], "doc": " Set up responsive behavior.\n"}, {"name": "setupStyleClasses", "paramTypes": [], "doc": " Set up style classes.\n"}, {"name": "columnsProperty", "paramTypes": [], "doc": " Get the columns property.\n \n @return The columns property\n"}, {"name": "getColumns", "paramTypes": [], "doc": " Get the number of columns.\n \n @return The number of columns\n"}, {"name": "setColumns", "paramTypes": ["int"], "doc": " Set the number of columns.\n \n @param columns The number of columns\n"}, {"name": "setColumnsForBreakpoint", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.Breakpoint", "int"], "doc": " Set the column configuration for a specific breakpoint.\n \n @param breakpoint The breakpoint\n @param columns The number of columns for this breakpoint\n"}, {"name": "getColumnsForBreakpoint", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.Breakpoint"], "doc": " Get the column configuration for a breakpoint.\n \n @param breakpoint The breakpoint\n @return The number of columns for this breakpoint\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["javafx.scene.Node"], "doc": " Add a child node to the grid.\n The node will be automatically positioned based on the current layout.\n \n @param child The child node to add\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["javafx.scene.Node", "int", "int"], "doc": " Add a child node at a specific grid position.\n \n @param child The child node to add\n @param columnIndex The column index\n @param rowIndex The row index\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["javafx.scene.Node", "int", "int", "int", "int"], "doc": " Add a child node with column and row span.\n \n @param child The child node to add\n @param columnIndex The column index\n @param rowIndex The row index\n @param columnSpan The column span\n @param rowSpan The row span\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["javafx.scene.Node"], "doc": " Remove a child node from the grid.\n \n @param child The child node to remove\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " Clear all children from the grid.\n"}, {"name": "getLayoutManager", "paramTypes": [], "doc": " Get the responsive layout manager.\n \n @return The layout manager\n"}, {"name": "onBreakpointChanged", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.Breakpoint"], "doc": " Handle breakpoint changes.\n \n @param newBreakpoint The new breakpoint\n"}, {"name": "updateBreakpointStyleClasses", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.Breakpoint"], "doc": " Update style classes based on current breakpoint.\n \n @param breakpoint The current breakpoint\n"}, {"name": "updateColumnConstraints", "paramTypes": [], "doc": " Update column constraints based on current column count.\n"}, {"name": "relayout<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " Re-layout all children in the grid.\n"}, {"name": "setEqualColumnWidths", "paramTypes": ["boolean"], "doc": " Set equal column widths.\n \n @param equal True to set equal column widths\n"}, {"name": "setGap", "paramTypes": ["double"], "doc": " Set the gap between grid cells.\n \n @param gap The gap size\n"}, {"name": "setResponsive", "paramTypes": ["boolean"], "doc": " Enable or disable responsive behavior.\n \n @param responsive True to enable responsive behavior\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager"], "doc": " Constructor with layout manager.\n \n @param layoutManager The responsive layout manager\n"}]}