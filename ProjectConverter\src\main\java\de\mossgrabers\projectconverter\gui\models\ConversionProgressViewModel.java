package de.mossgrabers.projectconverter.gui.models;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ListProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleListProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * View model for tracking conversion progress and logging.
 * Manages progress indicators, status messages, and log entries.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ConversionProgressViewModel extends BaseViewModel
{
    private static final int MAX_LOG_ENTRIES = 1000;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    // Progress properties
    private final DoubleProperty progressProperty = new SimpleDoubleProperty(0.0);
    private final StringProperty currentStepProperty = new SimpleStringProperty("");
    private final BooleanProperty indeterminateProperty = new SimpleBooleanProperty(true);
    
    // Status properties
    private final StringProperty statusTextProperty = new SimpleStringProperty("Ready");
    private final BooleanProperty hasErrorsProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty hasWarningsProperty = new SimpleBooleanProperty(false);
    
    // Log properties
    private final ListProperty<LogEntry> logEntriesProperty = new SimpleListProperty<>(FXCollections.observableArrayList());
    private final StringProperty logTextProperty = new SimpleStringProperty("");
    
    // Timing properties
    private final StringProperty startTimeProperty = new SimpleStringProperty("");
    private final StringProperty elapsedTimeProperty = new SimpleStringProperty("");
    private LocalDateTime conversionStartTime;
    
    /**
     * Constructor.
     */
    public ConversionProgressViewModel()
    {
        super();
        setupPropertyBindings();
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        // Update log text when log entries change
        this.logEntriesProperty.addListener((obs, oldList, newList) -> updateLogText());
    }
    
    // Progress properties
    public DoubleProperty progressProperty() { return this.progressProperty; }
    public double getProgress() { return this.progressProperty.get(); }
    public void setProgress(final double progress) { this.progressProperty.set(Math.max(0.0, Math.min(1.0, progress))); }
    
    public StringProperty currentStepProperty() { return this.currentStepProperty; }
    public String getCurrentStep() { return this.currentStepProperty.get(); }
    public void setCurrentStep(final String step) { this.currentStepProperty.set(step); }
    
    public BooleanProperty indeterminateProperty() { return this.indeterminateProperty; }
    public boolean isIndeterminate() { return this.indeterminateProperty.get(); }
    public void setIndeterminate(final boolean indeterminate) { this.indeterminateProperty.set(indeterminate); }
    
    // Status properties
    public StringProperty statusTextProperty() { return this.statusTextProperty; }
    public String getStatusText() { return this.statusTextProperty.get(); }
    public void setStatusText(final String status) { this.statusTextProperty.set(status); }
    
    public BooleanProperty hasErrorsProperty() { return this.hasErrorsProperty; }
    public boolean hasErrors() { return this.hasErrorsProperty.get(); }
    
    public BooleanProperty hasWarningsProperty() { return this.hasWarningsProperty; }
    public boolean hasWarnings() { return this.hasWarningsProperty.get(); }
    
    // Log properties
    public ListProperty<LogEntry> logEntriesProperty() { return this.logEntriesProperty; }
    public ObservableList<LogEntry> getLogEntries() { return this.logEntriesProperty.get(); }
    
    public StringProperty logTextProperty() { return this.logTextProperty; }
    public String getLogText() { return this.logTextProperty.get(); }
    
    // Timing properties
    public StringProperty startTimeProperty() { return this.startTimeProperty; }
    public String getStartTime() { return this.startTimeProperty.get(); }
    
    public StringProperty elapsedTimeProperty() { return this.elapsedTimeProperty; }
    public String getElapsedTime() { return this.elapsedTimeProperty.get(); }
    
    /**
     * Start a new conversion process.
     */
    public void startConversion()
    {
        this.conversionStartTime = LocalDateTime.now();
        setStartTime(this.conversionStartTime.format(TIME_FORMATTER));
        setProgress(0.0);
        setIndeterminate(true);
        setCurrentStep("Initializing...");
        setStatusText("Conversion in progress");
        clearLog();
        this.hasErrorsProperty.set(false);
        this.hasWarningsProperty.set(false);
        setBusy(true);
        
        addLogEntry(LogLevel.INFO, "Conversion started at " + getStartTime());
    }
    
    /**
     * Complete the conversion process.
     * 
     * @param success True if conversion was successful
     */
    public void completeConversion(final boolean success)
    {
        setProgress(1.0);
        setIndeterminate(false);
        setCurrentStep(success ? "Completed successfully" : "Completed with errors");
        setStatusText(success ? "Conversion completed" : "Conversion failed");
        setBusy(false);
        
        updateElapsedTime();
        
        final String message = success ? "Conversion completed successfully" : "Conversion completed with errors";
        addLogEntry(success ? LogLevel.INFO : LogLevel.ERROR, message);
    }
    
    /**
     * Cancel the conversion process.
     */
    public void cancelConversion()
    {
        setIndeterminate(false);
        setCurrentStep("Cancelled");
        setStatusText("Conversion cancelled");
        setBusy(false);
        
        updateElapsedTime();
        addLogEntry(LogLevel.WARNING, "Conversion cancelled by user");
    }
    
    /**
     * Update the conversion progress.
     * 
     * @param progress Progress value between 0.0 and 1.0
     * @param step Description of current step
     */
    public void updateProgress(final double progress, final String step)
    {
        setProgress(progress);
        setCurrentStep(step);
        setIndeterminate(false);
        updateElapsedTime();
    }
    
    /**
     * Add a log entry.
     * 
     * @param level The log level
     * @param message The log message
     */
    public void addLogEntry(final LogLevel level, final String message)
    {
        final LogEntry entry = new LogEntry(LocalDateTime.now(), level, message);
        final ObservableList<LogEntry> entries = getLogEntries();
        
        entries.add(entry);
        
        // Update error/warning flags
        if (level == LogLevel.ERROR)
        {
            this.hasErrorsProperty.set(true);
        }
        else if (level == LogLevel.WARNING)
        {
            this.hasWarningsProperty.set(true);
        }
        
        // Limit log size
        while (entries.size() > MAX_LOG_ENTRIES)
        {
            entries.remove(0);
        }
    }
    
    /**
     * Add an info log entry.
     * 
     * @param message The log message
     */
    public void addInfo(final String message)
    {
        addLogEntry(LogLevel.INFO, message);
    }
    
    /**
     * Add a warning log entry.
     * 
     * @param message The log message
     */
    public void addWarning(final String message)
    {
        addLogEntry(LogLevel.WARNING, message);
    }
    
    /**
     * Add an error log entry.
     * 
     * @param message The log message
     */
    public void addError(final String message)
    {
        addLogEntry(LogLevel.ERROR, message);
    }
    
    /**
     * Clear all log entries.
     */
    public void clearLog()
    {
        getLogEntries().clear();
        this.hasErrorsProperty.set(false);
        this.hasWarningsProperty.set(false);
    }
    
    /**
     * Update the elapsed time display.
     */
    private void updateElapsedTime()
    {
        if (this.conversionStartTime != null)
        {
            final LocalDateTime now = LocalDateTime.now();
            final long seconds = java.time.Duration.between(this.conversionStartTime, now).getSeconds();
            final String elapsed = String.format("%02d:%02d", seconds / 60, seconds % 60);
            this.elapsedTimeProperty.set(elapsed);
        }
    }
    
    /**
     * Update the log text property from log entries.
     */
    private void updateLogText()
    {
        final StringBuilder sb = new StringBuilder();
        for (final LogEntry entry : getLogEntries())
        {
            if (sb.length() > 0)
            {
                sb.append("\n");
            }
            sb.append(entry.toString());
        }
        this.logTextProperty.set(sb.toString());
    }
    
    /**
     * Set the start time.
     * 
     * @param startTime The start time string
     */
    private void setStartTime(final String startTime)
    {
        this.startTimeProperty.set(startTime);
    }
    
    @Override
    public void reset()
    {
        super.reset();
        setProgress(0.0);
        setIndeterminate(true);
        setCurrentStep("");
        setStatusText("Ready");
        clearLog();
        this.conversionStartTime = null;
        this.startTimeProperty.set("");
        this.elapsedTimeProperty.set("");
    }
    
    /**
     * Log level enumeration.
     */
    public enum LogLevel
    {
        INFO, WARNING, ERROR
    }
    
    /**
     * Log entry class.
     */
    public static class LogEntry
    {
        private final LocalDateTime timestamp;
        private final LogLevel level;
        private final String message;
        
        public LogEntry(final LocalDateTime timestamp, final LogLevel level, final String message)
        {
            this.timestamp = timestamp;
            this.level = level;
            this.message = message;
        }
        
        public LocalDateTime getTimestamp() { return this.timestamp; }
        public LogLevel getLevel() { return this.level; }
        public String getMessage() { return this.message; }
        
        @Override
        public String toString()
        {
            return String.format("[%s] %s: %s", 
                               this.timestamp.format(TIME_FORMATTER), 
                               this.level, 
                               this.message);
        }
    }
}
