package de.mossgrabers.projectconverter.gui.accessibility;

import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.scene.Node;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Control;
import javafx.scene.control.Labeled;
import javafx.scene.control.TextInputControl;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Accessibility manager for enhancing keyboard navigation, screen reader support,
 * and other accessibility features throughout the application.
 * 
 * <AUTHOR> ProjectConverter
 */
public class AccessibilityManager
{
    private static final Logger LOGGER = LoggerFactory.getLogger(AccessibilityManager.class);
    
    private static AccessibilityManager instance;
    
    // Accessibility properties
    private final BooleanProperty screenReaderSupportProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty enhancedKeyboardNavigationProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty highContrastModeProperty = new SimpleBooleanProperty(false);
    private final DoubleProperty focusIndicatorSizeProperty = new SimpleDoubleProperty(1.0);
    private final DoubleProperty fontSizeMultiplierProperty = new SimpleDoubleProperty(1.0);
    
    // Registered scenes and nodes
    private final List<Scene> registeredScenes = new ArrayList<>();
    private final Map<Node, AccessibilityInfo> nodeAccessibilityInfo = new HashMap<>();
    private final List<Consumer<AccessibilityEvent>> accessibilityListeners = new ArrayList<>();
    
    // Keyboard navigation
    private Node currentFocusedNode;
    private final List<Node> focusTraversalOrder = new ArrayList<>();
    
    /**
     * Private constructor for singleton pattern.
     */
    private AccessibilityManager()
    {
        setupPropertyBindings();
    }
    
    /**
     * Get the singleton instance.
     * 
     * @return The accessibility manager instance
     */
    public static synchronized AccessibilityManager getInstance()
    {
        if (instance == null)
        {
            instance = new AccessibilityManager();
        }
        return instance;
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        this.screenReaderSupportProperty.addListener((obs, oldValue, newValue) -> {
            updateScreenReaderSupport(newValue);
        });
        
        this.enhancedKeyboardNavigationProperty.addListener((obs, oldValue, newValue) -> {
            updateKeyboardNavigation(newValue);
        });
        
        this.highContrastModeProperty.addListener((obs, oldValue, newValue) -> {
            updateHighContrastMode(newValue);
        });
        
        this.focusIndicatorSizeProperty.addListener((obs, oldValue, newValue) -> {
            updateFocusIndicatorSize(newValue.doubleValue());
        });
        
        this.fontSizeMultiplierProperty.addListener((obs, oldValue, newValue) -> {
            updateFontSizes(newValue.doubleValue());
        });
    }
    
    // Property getters and setters
    public BooleanProperty screenReaderSupportProperty() { return this.screenReaderSupportProperty; }
    public boolean isScreenReaderSupport() { return this.screenReaderSupportProperty.get(); }
    public void setScreenReaderSupport(final boolean enabled) { this.screenReaderSupportProperty.set(enabled); }
    
    public BooleanProperty enhancedKeyboardNavigationProperty() { return this.enhancedKeyboardNavigationProperty; }
    public boolean isEnhancedKeyboardNavigation() { return this.enhancedKeyboardNavigationProperty.get(); }
    public void setEnhancedKeyboardNavigation(final boolean enabled) { this.enhancedKeyboardNavigationProperty.set(enabled); }
    
    public BooleanProperty highContrastModeProperty() { return this.highContrastModeProperty; }
    public boolean isHighContrastMode() { return this.highContrastModeProperty.get(); }
    public void setHighContrastMode(final boolean enabled) { this.highContrastModeProperty.set(enabled); }
    
    public DoubleProperty focusIndicatorSizeProperty() { return this.focusIndicatorSizeProperty; }
    public double getFocusIndicatorSize() { return this.focusIndicatorSizeProperty.get(); }
    public void setFocusIndicatorSize(final double size) { this.focusIndicatorSizeProperty.set(Math.max(0.5, Math.min(3.0, size))); }
    
    public DoubleProperty fontSizeMultiplierProperty() { return this.fontSizeMultiplierProperty; }
    public double getFontSizeMultiplier() { return this.fontSizeMultiplierProperty.get(); }
    public void setFontSizeMultiplier(final double multiplier) { this.fontSizeMultiplierProperty.set(Math.max(0.5, Math.min(3.0, multiplier))); }
    
    /**
     * Register a scene for accessibility management.
     * 
     * @param scene The scene to register
     */
    public void registerScene(final Scene scene)
    {
        if (scene != null && !this.registeredScenes.contains(scene))
        {
            this.registeredScenes.add(scene);
            setupSceneAccessibility(scene);
            LOGGER.debug("Scene registered for accessibility management");
        }
    }
    
    /**
     * Unregister a scene from accessibility management.
     * 
     * @param scene The scene to unregister
     */
    public void unregisterScene(final Scene scene)
    {
        this.registeredScenes.remove(scene);
        LOGGER.debug("Scene unregistered from accessibility management");
    }
    
    /**
     * Set accessibility information for a node.
     * 
     * @param node The node
     * @param accessibilityInfo The accessibility information
     */
    public void setAccessibilityInfo(final Node node, final AccessibilityInfo accessibilityInfo)
    {
        this.nodeAccessibilityInfo.put(node, accessibilityInfo);
        applyAccessibilityInfo(node, accessibilityInfo);
    }
    
    /**
     * Set accessible text for a node.
     * 
     * @param node The node
     * @param accessibleText The accessible text
     */
    public void setAccessibleText(final Node node, final String accessibleText)
    {
        if (node != null && accessibleText != null)
        {
            node.setAccessibleText(accessibleText);
            LOGGER.debug("Set accessible text for node: {}", accessibleText);
        }
    }
    
    /**
     * Set accessible help text for a node.
     * 
     * @param node The node
     * @param helpText The help text
     */
    public void setAccessibleHelp(final Node node, final String helpText)
    {
        if (node != null && helpText != null)
        {
            node.setAccessibleHelp(helpText);
            LOGGER.debug("Set accessible help for node: {}", helpText);
        }
    }
    
    /**
     * Set accessible role for a node.
     * 
     * @param node The node
     * @param role The accessible role
     */
    public void setAccessibleRole(final Node node, final javafx.scene.AccessibleRole role)
    {
        if (node != null && role != null)
        {
            node.setAccessibleRole(role);
            LOGGER.debug("Set accessible role for node: {}", role);
        }
    }
    
    /**
     * Add a node to the focus traversal order.
     * 
     * @param node The node to add
     */
    public void addToFocusTraversal(final Node node)
    {
        if (node != null && !this.focusTraversalOrder.contains(node))
        {
            this.focusTraversalOrder.add(node);
            node.setFocusTraversable(true);
        }
    }
    
    /**
     * Remove a node from the focus traversal order.
     * 
     * @param node The node to remove
     */
    public void removeFromFocusTraversal(final Node node)
    {
        this.focusTraversalOrder.remove(node);
    }
    
    /**
     * Move focus to the next node in traversal order.
     */
    public void focusNext()
    {
        if (this.focusTraversalOrder.isEmpty())
            return;
            
        final int currentIndex = this.focusTraversalOrder.indexOf(this.currentFocusedNode);
        final int nextIndex = (currentIndex + 1) % this.focusTraversalOrder.size();
        final Node nextNode = this.focusTraversalOrder.get(nextIndex);
        
        Platform.runLater(() -> {
            nextNode.requestFocus();
            this.currentFocusedNode = nextNode;
            announceToScreenReader("Focus moved to " + getNodeDescription(nextNode));
        });
    }
    
    /**
     * Move focus to the previous node in traversal order.
     */
    public void focusPrevious()
    {
        if (this.focusTraversalOrder.isEmpty())
            return;
            
        final int currentIndex = this.focusTraversalOrder.indexOf(this.currentFocusedNode);
        final int prevIndex = currentIndex <= 0 ? this.focusTraversalOrder.size() - 1 : currentIndex - 1;
        final Node prevNode = this.focusTraversalOrder.get(prevIndex);
        
        Platform.runLater(() -> {
            prevNode.requestFocus();
            this.currentFocusedNode = prevNode;
            announceToScreenReader("Focus moved to " + getNodeDescription(prevNode));
        });
    }
    
    /**
     * Announce text to screen readers.
     * 
     * @param text The text to announce
     */
    public void announceToScreenReader(final String text)
    {
        if (isScreenReaderSupport() && text != null && !text.trim().isEmpty())
        {
            // Create a temporary node for screen reader announcement
            Platform.runLater(() -> {
                final javafx.scene.control.Label announcement = new javafx.scene.control.Label();
                announcement.setAccessibleText(text);
                announcement.setVisible(false);
                announcement.setManaged(false);
                
                // Add to first registered scene temporarily
                if (!this.registeredScenes.isEmpty())
                {
                    final Scene scene = this.registeredScenes.get(0);
                    if (scene.getRoot() instanceof javafx.scene.layout.Pane)
                    {
                        final javafx.scene.layout.Pane root = (javafx.scene.layout.Pane) scene.getRoot();
                        root.getChildren().add(announcement);
                        
                        // Remove after a short delay
                        Platform.runLater(() -> root.getChildren().remove(announcement));
                    }
                }
            });
            
            LOGGER.debug("Screen reader announcement: {}", text);
        }
    }
    
    /**
     * Add an accessibility event listener.
     * 
     * @param listener The listener to add
     */
    public void addAccessibilityListener(final Consumer<AccessibilityEvent> listener)
    {
        this.accessibilityListeners.add(listener);
    }
    
    /**
     * Remove an accessibility event listener.
     * 
     * @param listener The listener to remove
     */
    public void removeAccessibilityListener(final Consumer<AccessibilityEvent> listener)
    {
        this.accessibilityListeners.remove(listener);
    }
    
    /**
     * Set up accessibility for a scene.
     * 
     * @param scene The scene to set up
     */
    private void setupSceneAccessibility(final Scene scene)
    {
        // Add keyboard event handlers for enhanced navigation
        scene.addEventFilter(KeyEvent.KEY_PRESSED, this::handleKeyboardNavigation);
        
        // Set up focus tracking
        scene.focusOwnerProperty().addListener((obs, oldFocus, newFocus) -> {
            this.currentFocusedNode = newFocus;
            if (newFocus != null)
            {
                notifyAccessibilityListeners(new AccessibilityEvent(AccessibilityEvent.Type.FOCUS_CHANGED, newFocus));
            }
        });
    }
    
    /**
     * Handle keyboard navigation events.
     * 
     * @param event The keyboard event
     */
    private void handleKeyboardNavigation(final KeyEvent event)
    {
        if (!isEnhancedKeyboardNavigation())
            return;
            
        if (event.getCode() == KeyCode.TAB)
        {
            if (event.isShiftDown())
            {
                focusPrevious();
            }
            else
            {
                focusNext();
            }
            event.consume();
        }
        else if (event.getCode() == KeyCode.F6)
        {
            // Move between major UI sections
            focusNext();
            event.consume();
        }
    }
    
    /**
     * Apply accessibility information to a node.
     * 
     * @param node The node
     * @param info The accessibility information
     */
    private void applyAccessibilityInfo(final Node node, final AccessibilityInfo info)
    {
        if (info.getAccessibleText() != null)
        {
            node.setAccessibleText(info.getAccessibleText());
        }
        
        if (info.getAccessibleHelp() != null)
        {
            node.setAccessibleHelp(info.getAccessibleHelp());
        }
        
        if (info.getAccessibleRole() != null)
        {
            node.setAccessibleRole(info.getAccessibleRole());
        }
        
        node.setFocusTraversable(info.isFocusTraversable());
    }
    
    /**
     * Update screen reader support.
     * 
     * @param enabled True if enabled
     */
    private void updateScreenReaderSupport(final boolean enabled)
    {
        LOGGER.info("Screen reader support {}", enabled ? "enabled" : "disabled");
        
        if (enabled)
        {
            // Enable additional accessibility features
            for (final Scene scene : this.registeredScenes)
            {
                enhanceSceneForScreenReaders(scene);
            }
        }
    }
    
    /**
     * Update keyboard navigation enhancement.
     * 
     * @param enabled True if enabled
     */
    private void updateKeyboardNavigation(final boolean enabled)
    {
        LOGGER.info("Enhanced keyboard navigation {}", enabled ? "enabled" : "disabled");
    }
    
    /**
     * Update high contrast mode.
     * 
     * @param enabled True if enabled
     */
    private void updateHighContrastMode(final boolean enabled)
    {
        LOGGER.info("High contrast mode {}", enabled ? "enabled" : "disabled");
        
        for (final Scene scene : this.registeredScenes)
        {
            if (enabled)
            {
                scene.getStylesheets().add(getClass().getResource("/de/mossgrabers/projectconverter/css/high-contrast.css").toExternalForm());
            }
            else
            {
                scene.getStylesheets().removeIf(stylesheet -> stylesheet.contains("high-contrast"));
            }
        }
    }
    
    /**
     * Update focus indicator size.
     * 
     * @param size The new size multiplier
     */
    private void updateFocusIndicatorSize(final double size)
    {
        LOGGER.debug("Focus indicator size updated to: {}", size);
        
        for (final Scene scene : this.registeredScenes)
        {
            final String focusStyle = String.format(
                "data:text/css,.root { -fx-focus-color: derive(-fx-accent, 0%%); " +
                "-fx-faint-focus-color: derive(-fx-focus-color, 50%%); " +
                "-fx-focus-traversable-border-width: %.1fpx; }", 
                size * 2
            );
            
            scene.getStylesheets().removeIf(stylesheet -> stylesheet.contains("focus-indicator"));
            scene.getStylesheets().add(focusStyle);
        }
    }
    
    /**
     * Update font sizes.
     * 
     * @param multiplier The font size multiplier
     */
    private void updateFontSizes(final double multiplier)
    {
        LOGGER.debug("Font size multiplier updated to: {}", multiplier);
        
        for (final Scene scene : this.registeredScenes)
        {
            final String fontStyle = String.format(
                "data:text/css,.root { -fx-font-size: %.1fem; }", 
                multiplier
            );
            
            scene.getStylesheets().removeIf(stylesheet -> stylesheet.contains("font-size"));
            scene.getStylesheets().add(fontStyle);
        }
    }
    
    /**
     * Enhance a scene for screen readers.
     * 
     * @param scene The scene to enhance
     */
    private void enhanceSceneForScreenReaders(final Scene scene)
    {
        // Add ARIA-like attributes to common controls
        scene.getRoot().lookupAll(".button").forEach(node -> {
            if (node instanceof Button)
            {
                final Button button = (Button) node;
                if (button.getAccessibleText() == null && button.getText() != null)
                {
                    button.setAccessibleText("Button: " + button.getText());
                }
            }
        });
    }
    
    /**
     * Get a description of a node for screen readers.
     * 
     * @param node The node
     * @return A description of the node
     */
    private String getNodeDescription(final Node node)
    {
        if (node instanceof Labeled)
        {
            final Labeled labeled = (Labeled) node;
            return labeled.getText() != null ? labeled.getText() : labeled.getClass().getSimpleName();
        }
        else if (node instanceof TextInputControl)
        {
            return "Text input";
        }
        else
        {
            return node.getClass().getSimpleName();
        }
    }
    
    /**
     * Notify accessibility listeners.
     * 
     * @param event The accessibility event
     */
    private void notifyAccessibilityListeners(final AccessibilityEvent event)
    {
        for (final Consumer<AccessibilityEvent> listener : this.accessibilityListeners)
        {
            try
            {
                listener.accept(event);
            }
            catch (final Exception e)
            {
                LOGGER.warn("Error in accessibility listener", e);
            }
        }
    }
}
