package de.mossgrabers.projectconverter.gui.models;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

import java.util.prefs.Preferences;

/**
 * View model for application settings and preferences.
 * Manages user preferences, theme settings, and application configuration.
 * 
 * <AUTHOR> ProjectConverter
 */
public class SettingsViewModel extends BaseViewModel
{
    // Theme settings
    private final BooleanProperty darkModeEnabledProperty = new SimpleBooleanProperty(false);
    private final StringProperty themeNameProperty = new SimpleStringProperty("Light");
    private final BooleanProperty highContrastModeProperty = new SimpleBooleanProperty(false);
    
    // UI settings
    private final DoubleProperty fontSizeProperty = new SimpleDoubleProperty(12.0);
    private final BooleanProperty animationsEnabledProperty = new SimpleBooleanProperty(true);
    private final BooleanProperty tooltipsEnabledProperty = new SimpleBooleanProperty(true);
    
    // Accessibility settings
    private final BooleanProperty screenReaderSupportProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty keyboardNavigationEnhancedProperty = new SimpleBooleanProperty(false);
    private final DoubleProperty focusIndicatorSizeProperty = new SimpleDoubleProperty(1.0);
    
    // Performance settings
    private final BooleanProperty memoryMonitoringEnabledProperty = new SimpleBooleanProperty(true);
    private final IntegerProperty maxLogEntriesProperty = new SimpleIntegerProperty(1000);
    private final BooleanProperty parallelProcessingEnabledProperty = new SimpleBooleanProperty(false);
    
    // File handling settings
    private final BooleanProperty autoSaveSettingsProperty = new SimpleBooleanProperty(true);
    private final IntegerProperty pathHistorySizeProperty = new SimpleIntegerProperty(20);
    private final BooleanProperty validateInputFilesProperty = new SimpleBooleanProperty(true);
    
    // Window settings
    private final DoubleProperty windowWidthProperty = new SimpleDoubleProperty(1100);
    private final DoubleProperty windowHeightProperty = new SimpleDoubleProperty(500);
    private final BooleanProperty rememberWindowSizeProperty = new SimpleBooleanProperty(true);
    private final BooleanProperty startMaximizedProperty = new SimpleBooleanProperty(false);
    
    // Advanced settings
    private final StringProperty logLevelProperty = new SimpleStringProperty("INFO");
    private final BooleanProperty debugModeEnabledProperty = new SimpleBooleanProperty(false);
    private final IntegerProperty threadPoolSizeProperty = new SimpleIntegerProperty(2);
    
    // Preferences storage
    private Preferences preferences;
    
    /**
     * Constructor.
     */
    public SettingsViewModel()
    {
        super();
        this.preferences = Preferences.userNodeForPackage(SettingsViewModel.class);
        setupPropertyBindings();
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        // Update theme name when dark mode changes
        this.darkModeEnabledProperty.addListener((obs, oldValue, newValue) -> {
            this.themeNameProperty.set(newValue ? "Dark" : "Light");
        });
        
        // Auto-save settings when they change (if enabled)
        setupAutoSaveListeners();
    }
    
    /**
     * Set up auto-save listeners for all properties.
     */
    private void setupAutoSaveListeners()
    {
        // Add listeners to save settings automatically
        this.darkModeEnabledProperty.addListener((obs, oldValue, newValue) -> autoSave());
        this.fontSizeProperty.addListener((obs, oldValue, newValue) -> autoSave());
        this.animationsEnabledProperty.addListener((obs, oldValue, newValue) -> autoSave());
        this.memoryMonitoringEnabledProperty.addListener((obs, oldValue, newValue) -> autoSave());
        this.windowWidthProperty.addListener((obs, oldValue, newValue) -> autoSave());
        this.windowHeightProperty.addListener((obs, oldValue, newValue) -> autoSave());
    }
    
    // Theme properties
    public BooleanProperty darkModeEnabledProperty() { return this.darkModeEnabledProperty; }
    public boolean isDarkModeEnabled() { return this.darkModeEnabledProperty.get(); }
    public void setDarkModeEnabled(final boolean enabled) { this.darkModeEnabledProperty.set(enabled); }
    
    public StringProperty themeNameProperty() { return this.themeNameProperty; }
    public String getThemeName() { return this.themeNameProperty.get(); }
    
    public BooleanProperty highContrastModeProperty() { return this.highContrastModeProperty; }
    public boolean isHighContrastMode() { return this.highContrastModeProperty.get(); }
    public void setHighContrastMode(final boolean enabled) { this.highContrastModeProperty.set(enabled); }
    
    // UI properties
    public DoubleProperty fontSizeProperty() { return this.fontSizeProperty; }
    public double getFontSize() { return this.fontSizeProperty.get(); }
    public void setFontSize(final double size) { this.fontSizeProperty.set(Math.max(8.0, Math.min(24.0, size))); }
    
    public BooleanProperty animationsEnabledProperty() { return this.animationsEnabledProperty; }
    public boolean areAnimationsEnabled() { return this.animationsEnabledProperty.get(); }
    public void setAnimationsEnabled(final boolean enabled) { this.animationsEnabledProperty.set(enabled); }
    
    public BooleanProperty tooltipsEnabledProperty() { return this.tooltipsEnabledProperty; }
    public boolean areTooltipsEnabled() { return this.tooltipsEnabledProperty.get(); }
    public void setTooltipsEnabled(final boolean enabled) { this.tooltipsEnabledProperty.set(enabled); }
    
    // Accessibility properties
    public BooleanProperty screenReaderSupportProperty() { return this.screenReaderSupportProperty; }
    public boolean isScreenReaderSupport() { return this.screenReaderSupportProperty.get(); }
    public void setScreenReaderSupport(final boolean enabled) { this.screenReaderSupportProperty.set(enabled); }
    
    public BooleanProperty keyboardNavigationEnhancedProperty() { return this.keyboardNavigationEnhancedProperty; }
    public boolean isKeyboardNavigationEnhanced() { return this.keyboardNavigationEnhancedProperty.get(); }
    public void setKeyboardNavigationEnhanced(final boolean enabled) { this.keyboardNavigationEnhancedProperty.set(enabled); }
    
    public DoubleProperty focusIndicatorSizeProperty() { return this.focusIndicatorSizeProperty; }
    public double getFocusIndicatorSize() { return this.focusIndicatorSizeProperty.get(); }
    public void setFocusIndicatorSize(final double size) { this.focusIndicatorSizeProperty.set(Math.max(0.5, Math.min(3.0, size))); }
    
    // Performance properties
    public BooleanProperty memoryMonitoringEnabledProperty() { return this.memoryMonitoringEnabledProperty; }
    public boolean isMemoryMonitoringEnabled() { return this.memoryMonitoringEnabledProperty.get(); }
    public void setMemoryMonitoringEnabled(final boolean enabled) { this.memoryMonitoringEnabledProperty.set(enabled); }
    
    public IntegerProperty maxLogEntriesProperty() { return this.maxLogEntriesProperty; }
    public int getMaxLogEntries() { return this.maxLogEntriesProperty.get(); }
    public void setMaxLogEntries(final int max) { this.maxLogEntriesProperty.set(Math.max(100, Math.min(10000, max))); }
    
    public BooleanProperty parallelProcessingEnabledProperty() { return this.parallelProcessingEnabledProperty; }
    public boolean isParallelProcessingEnabled() { return this.parallelProcessingEnabledProperty.get(); }
    public void setParallelProcessingEnabled(final boolean enabled) { this.parallelProcessingEnabledProperty.set(enabled); }
    
    // File handling properties
    public BooleanProperty autoSaveSettingsProperty() { return this.autoSaveSettingsProperty; }
    public boolean isAutoSaveSettings() { return this.autoSaveSettingsProperty.get(); }
    public void setAutoSaveSettings(final boolean enabled) { this.autoSaveSettingsProperty.set(enabled); }
    
    public IntegerProperty pathHistorySizeProperty() { return this.pathHistorySizeProperty; }
    public int getPathHistorySize() { return this.pathHistorySizeProperty.get(); }
    public void setPathHistorySize(final int size) { this.pathHistorySizeProperty.set(Math.max(5, Math.min(50, size))); }
    
    public BooleanProperty validateInputFilesProperty() { return this.validateInputFilesProperty; }
    public boolean isValidateInputFiles() { return this.validateInputFilesProperty.get(); }
    public void setValidateInputFiles(final boolean enabled) { this.validateInputFilesProperty.set(enabled); }
    
    // Window properties
    public DoubleProperty windowWidthProperty() { return this.windowWidthProperty; }
    public double getWindowWidth() { return this.windowWidthProperty.get(); }
    public void setWindowWidth(final double width) { this.windowWidthProperty.set(Math.max(800, width)); }
    
    public DoubleProperty windowHeightProperty() { return this.windowHeightProperty; }
    public double getWindowHeight() { return this.windowHeightProperty.get(); }
    public void setWindowHeight(final double height) { this.windowHeightProperty.set(Math.max(400, height)); }
    
    public BooleanProperty rememberWindowSizeProperty() { return this.rememberWindowSizeProperty; }
    public boolean isRememberWindowSize() { return this.rememberWindowSizeProperty.get(); }
    public void setRememberWindowSize(final boolean remember) { this.rememberWindowSizeProperty.set(remember); }
    
    public BooleanProperty startMaximizedProperty() { return this.startMaximizedProperty; }
    public boolean isStartMaximized() { return this.startMaximizedProperty.get(); }
    public void setStartMaximized(final boolean maximized) { this.startMaximizedProperty.set(maximized); }
    
    // Advanced properties
    public StringProperty logLevelProperty() { return this.logLevelProperty; }
    public String getLogLevel() { return this.logLevelProperty.get(); }
    public void setLogLevel(final String level) { this.logLevelProperty.set(level); }
    
    public BooleanProperty debugModeEnabledProperty() { return this.debugModeEnabledProperty; }
    public boolean isDebugModeEnabled() { return this.debugModeEnabledProperty.get(); }
    public void setDebugModeEnabled(final boolean enabled) { this.debugModeEnabledProperty.set(enabled); }
    
    public IntegerProperty threadPoolSizeProperty() { return this.threadPoolSizeProperty; }
    public int getThreadPoolSize() { return this.threadPoolSizeProperty.get(); }
    public void setThreadPoolSize(final int size) { this.threadPoolSizeProperty.set(Math.max(1, Math.min(8, size))); }
    
    /**
     * Load settings from preferences.
     */
    public void loadSettings()
    {
        setDarkModeEnabled(this.preferences.getBoolean("darkMode", false));
        setHighContrastMode(this.preferences.getBoolean("highContrast", false));
        setFontSize(this.preferences.getDouble("fontSize", 12.0));
        setAnimationsEnabled(this.preferences.getBoolean("animations", true));
        setTooltipsEnabled(this.preferences.getBoolean("tooltips", true));
        setScreenReaderSupport(this.preferences.getBoolean("screenReader", false));
        setKeyboardNavigationEnhanced(this.preferences.getBoolean("enhancedKeyboard", false));
        setFocusIndicatorSize(this.preferences.getDouble("focusIndicatorSize", 1.0));
        setMemoryMonitoringEnabled(this.preferences.getBoolean("memoryMonitoring", true));
        setMaxLogEntries(this.preferences.getInt("maxLogEntries", 1000));
        setParallelProcessingEnabled(this.preferences.getBoolean("parallelProcessing", false));
        setAutoSaveSettings(this.preferences.getBoolean("autoSave", true));
        setPathHistorySize(this.preferences.getInt("pathHistorySize", 20));
        setValidateInputFiles(this.preferences.getBoolean("validateFiles", true));
        setWindowWidth(this.preferences.getDouble("windowWidth", 1100));
        setWindowHeight(this.preferences.getDouble("windowHeight", 500));
        setRememberWindowSize(this.preferences.getBoolean("rememberWindowSize", true));
        setStartMaximized(this.preferences.getBoolean("startMaximized", false));
        setLogLevel(this.preferences.get("logLevel", "INFO"));
        setDebugModeEnabled(this.preferences.getBoolean("debugMode", false));
        setThreadPoolSize(this.preferences.getInt("threadPoolSize", 2));
    }
    
    /**
     * Save settings to preferences.
     */
    public void saveSettings()
    {
        this.preferences.putBoolean("darkMode", isDarkModeEnabled());
        this.preferences.putBoolean("highContrast", isHighContrastMode());
        this.preferences.putDouble("fontSize", getFontSize());
        this.preferences.putBoolean("animations", areAnimationsEnabled());
        this.preferences.putBoolean("tooltips", areTooltipsEnabled());
        this.preferences.putBoolean("screenReader", isScreenReaderSupport());
        this.preferences.putBoolean("enhancedKeyboard", isKeyboardNavigationEnhanced());
        this.preferences.putDouble("focusIndicatorSize", getFocusIndicatorSize());
        this.preferences.putBoolean("memoryMonitoring", isMemoryMonitoringEnabled());
        this.preferences.putInt("maxLogEntries", getMaxLogEntries());
        this.preferences.putBoolean("parallelProcessing", isParallelProcessingEnabled());
        this.preferences.putBoolean("autoSave", isAutoSaveSettings());
        this.preferences.putInt("pathHistorySize", getPathHistorySize());
        this.preferences.putBoolean("validateFiles", isValidateInputFiles());
        this.preferences.putDouble("windowWidth", getWindowWidth());
        this.preferences.putDouble("windowHeight", getWindowHeight());
        this.preferences.putBoolean("rememberWindowSize", isRememberWindowSize());
        this.preferences.putBoolean("startMaximized", isStartMaximized());
        this.preferences.put("logLevel", getLogLevel());
        this.preferences.putBoolean("debugMode", isDebugModeEnabled());
        this.preferences.putInt("threadPoolSize", getThreadPoolSize());
    }
    
    /**
     * Reset all settings to defaults.
     */
    public void resetToDefaults()
    {
        setDarkModeEnabled(false);
        setHighContrastMode(false);
        setFontSize(12.0);
        setAnimationsEnabled(true);
        setTooltipsEnabled(true);
        setScreenReaderSupport(false);
        setKeyboardNavigationEnhanced(false);
        setFocusIndicatorSize(1.0);
        setMemoryMonitoringEnabled(true);
        setMaxLogEntries(1000);
        setParallelProcessingEnabled(false);
        setAutoSaveSettings(true);
        setPathHistorySize(20);
        setValidateInputFiles(true);
        setWindowWidth(1100);
        setWindowHeight(500);
        setRememberWindowSize(true);
        setStartMaximized(false);
        setLogLevel("INFO");
        setDebugModeEnabled(false);
        setThreadPoolSize(2);
    }
    
    /**
     * Auto-save settings if enabled.
     */
    private void autoSave()
    {
        if (isAutoSaveSettings())
        {
            saveSettings();
        }
    }
    
    @Override
    public void initialize()
    {
        loadSettings();
    }
    
    @Override
    public void cleanup()
    {
        super.cleanup();
        if (isAutoSaveSettings())
        {
            saveSettings();
        }
    }
}
