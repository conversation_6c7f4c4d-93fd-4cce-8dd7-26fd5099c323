package de.mossgrabers.projectconverter.services;

import de.mossgrabers.projectconverter.core.ErrorCode;
import de.mossgrabers.projectconverter.core.ProjectConverterException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.NoSuchElementException;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * Streaming file reader for efficiently processing large project files.
 * Provides line-by-line reading with minimal memory footprint.
 * 
 * <AUTHOR> ProjectConverter
 */
public class StreamingFileReader implements AutoCloseable, Iterable<String>
{
    private static final Logger LOGGER = LoggerFactory.getLogger(StreamingFileReader.class);
    
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    private static final long LARGE_FILE_THRESHOLD = 100 * 1024 * 1024; // 100MB
    
    private final File file;
    private final Charset charset;
    private final int bufferSize;
    private BufferedReader reader;
    private long linesRead;
    private long bytesRead;
    private boolean closed;
    
    /**
     * Constructor with default settings.
     * 
     * @param file The file to read
     * @throws ProjectConverterException If file cannot be opened
     */
    public StreamingFileReader(final File file) throws ProjectConverterException
    {
        this(file, StandardCharsets.UTF_8, DEFAULT_BUFFER_SIZE);
    }
    
    /**
     * Constructor with custom charset.
     * 
     * @param file The file to read
     * @param charset The character encoding
     * @throws ProjectConverterException If file cannot be opened
     */
    public StreamingFileReader(final File file, final Charset charset) throws ProjectConverterException
    {
        this(file, charset, DEFAULT_BUFFER_SIZE);
    }
    
    /**
     * Constructor with full customization.
     * 
     * @param file The file to read
     * @param charset The character encoding
     * @param bufferSize The buffer size for reading
     * @throws ProjectConverterException If file cannot be opened
     */
    public StreamingFileReader(final File file, final Charset charset, final int bufferSize) 
            throws ProjectConverterException
    {
        if (file == null)
        {
            throw new ProjectConverterException(ErrorCode.FILE_NOT_FOUND, 
                "File is null", "File parameter cannot be null", false);
        }
        
        if (!file.exists())
        {
            throw new ProjectConverterException(ErrorCode.FILE_NOT_FOUND, 
                "File does not exist: " + file.getAbsolutePath(), 
                "File: " + file.getAbsolutePath(), false);
        }
        
        if (!file.canRead())
        {
            throw new ProjectConverterException(ErrorCode.FILE_ACCESS_DENIED, 
                "File is not readable: " + file.getAbsolutePath(), 
                "File: " + file.getAbsolutePath(), false);
        }
        
        this.file = file;
        this.charset = charset != null ? charset : StandardCharsets.UTF_8;
        this.bufferSize = bufferSize > 0 ? bufferSize : DEFAULT_BUFFER_SIZE;
        this.linesRead = 0;
        this.bytesRead = 0;
        this.closed = false;
        
        initializeReader();
        
        if (file.length() > LARGE_FILE_THRESHOLD)
        {
            LOGGER.info("Opening large file for streaming: {} ({} MB)", 
                       file.getName(), file.length() / (1024 * 1024));
        }
    }
    
    /**
     * Read the next line from the file.
     * 
     * @return The next line, or null if end of file
     * @throws ProjectConverterException If reading fails
     */
    public String readLine() throws ProjectConverterException
    {
        if (this.closed)
        {
            throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                "Reader is closed", "Cannot read from closed reader", false);
        }
        
        try
        {
            final String line = this.reader.readLine();
            if (line != null)
            {
                this.linesRead++;
                this.bytesRead += line.getBytes(this.charset).length + 1; // +1 for newline
            }
            return line;
        }
        catch (final IOException e)
        {
            throw new ProjectConverterException(ErrorCode.FILE_CORRUPTED, 
                "Error reading line from file", 
                "File: " + this.file.getAbsolutePath() + ", Line: " + this.linesRead, 
                false, e);
        }
    }
    
    /**
     * Process each line with a consumer function.
     * 
     * @param lineProcessor The function to process each line
     * @throws ProjectConverterException If processing fails
     */
    public void processLines(final Consumer<String> lineProcessor) throws ProjectConverterException
    {
        if (lineProcessor == null)
        {
            throw new ProjectConverterException(ErrorCode.INVALID_CONFIGURATION, 
                "Line processor is null", "Processor parameter cannot be null", false);
        }
        
        String line;
        while ((line = readLine()) != null)
        {
            try
            {
                lineProcessor.accept(line);
            }
            catch (final Exception e)
            {
                throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                    "Error processing line " + this.linesRead, 
                    "Line content: " + line, true, e);
            }
        }
    }
    
    /**
     * Process lines that match a predicate.
     * 
     * @param filter The predicate to filter lines
     * @param lineProcessor The function to process matching lines
     * @throws ProjectConverterException If processing fails
     */
    public void processFilteredLines(final Predicate<String> filter, 
                                   final Consumer<String> lineProcessor) throws ProjectConverterException
    {
        if (filter == null || lineProcessor == null)
        {
            throw new ProjectConverterException(ErrorCode.INVALID_CONFIGURATION, 
                "Filter or processor is null", "Parameters cannot be null", false);
        }
        
        String line;
        while ((line = readLine()) != null)
        {
            try
            {
                if (filter.test(line))
                {
                    lineProcessor.accept(line);
                }
            }
            catch (final Exception e)
            {
                throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                    "Error processing filtered line " + this.linesRead, 
                    "Line content: " + line, true, e);
            }
        }
    }
    
    /**
     * Skip a number of lines.
     * 
     * @param linesToSkip Number of lines to skip
     * @return Number of lines actually skipped
     * @throws ProjectConverterException If skipping fails
     */
    public long skipLines(final long linesToSkip) throws ProjectConverterException
    {
        if (linesToSkip < 0)
        {
            throw new ProjectConverterException(ErrorCode.INVALID_CONFIGURATION, 
                "Cannot skip negative number of lines", "Lines to skip: " + linesToSkip, false);
        }
        
        long skipped = 0;
        while (skipped < linesToSkip && readLine() != null)
        {
            skipped++;
        }
        
        return skipped;
    }
    
    /**
     * Get the number of lines read so far.
     * 
     * @return Number of lines read
     */
    public long getLinesRead()
    {
        return this.linesRead;
    }
    
    /**
     * Get the approximate number of bytes read so far.
     * 
     * @return Number of bytes read
     */
    public long getBytesRead()
    {
        return this.bytesRead;
    }
    
    /**
     * Get the file being read.
     * 
     * @return The file
     */
    public File getFile()
    {
        return this.file;
    }
    
    /**
     * Get the charset being used.
     * 
     * @return The charset
     */
    public Charset getCharset()
    {
        return this.charset;
    }
    
    /**
     * Check if the reader is closed.
     * 
     * @return True if closed
     */
    public boolean isClosed()
    {
        return this.closed;
    }
    
    /**
     * Get reading progress as a ratio (0.0 to 1.0).
     * 
     * @return Progress ratio
     */
    public double getProgress()
    {
        final long fileSize = this.file.length();
        return fileSize > 0 ? Math.min(1.0, (double) this.bytesRead / fileSize) : 0.0;
    }
    
    /**
     * Get reading statistics as a formatted string.
     * 
     * @return Formatted statistics
     */
    public String getStatistics()
    {
        return String.format("Lines: %d, Bytes: %d, Progress: %.1f%%", 
                           this.linesRead, this.bytesRead, getProgress() * 100);
    }
    
    @Override
    public void close() throws ProjectConverterException
    {
        if (this.closed)
            return;
            
        try
        {
            if (this.reader != null)
            {
                this.reader.close();
            }
            this.closed = true;
            
            LOGGER.debug("Closed streaming reader for file: {} ({})", 
                        this.file.getName(), getStatistics());
        }
        catch (final IOException e)
        {
            throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                "Error closing file reader", 
                "File: " + this.file.getAbsolutePath(), false, e);
        }
    }
    
    @Override
    public Iterator<String> iterator()
    {
        return new StreamingIterator();
    }
    
    private void initializeReader() throws ProjectConverterException
    {
        try
        {
            final FileInputStream fis = new FileInputStream(this.file);
            final InputStreamReader isr = new InputStreamReader(fis, this.charset);
            this.reader = new BufferedReader(isr, this.bufferSize);
        }
        catch (final IOException e)
        {
            throw new ProjectConverterException(ErrorCode.FILE_ACCESS_DENIED, 
                "Cannot open file for reading", 
                "File: " + this.file.getAbsolutePath(), false, e);
        }
    }
    
    /**
     * Iterator implementation for streaming file reading.
     */
    private class StreamingIterator implements Iterator<String>
    {
        private String nextLine;
        private boolean hasNextCached;
        
        @Override
        public boolean hasNext()
        {
            if (!this.hasNextCached)
            {
                try
                {
                    this.nextLine = readLine();
                    this.hasNextCached = true;
                }
                catch (final ProjectConverterException e)
                {
                    LOGGER.error("Error reading next line in iterator", e);
                    this.nextLine = null;
                    this.hasNextCached = true;
                }
            }
            return this.nextLine != null;
        }
        
        @Override
        public String next()
        {
            if (!hasNext())
            {
                throw new NoSuchElementException("No more lines available");
            }
            
            final String line = this.nextLine;
            this.nextLine = null;
            this.hasNextCached = false;
            return line;
        }
    }
    
    /**
     * Create a streaming reader with automatic charset detection.
     * 
     * @param file The file to read
     * @return Streaming file reader
     * @throws ProjectConverterException If file cannot be opened
     */
    public static StreamingFileReader create(final File file) throws ProjectConverterException
    {
        return new StreamingFileReader(file);
    }
    
    /**
     * Create a streaming reader for large files with optimized settings.
     * 
     * @param file The file to read
     * @return Streaming file reader with large file optimizations
     * @throws ProjectConverterException If file cannot be opened
     */
    public static StreamingFileReader createForLargeFile(final File file) throws ProjectConverterException
    {
        return new StreamingFileReader(file, StandardCharsets.UTF_8, 32768); // 32KB buffer
    }
}
