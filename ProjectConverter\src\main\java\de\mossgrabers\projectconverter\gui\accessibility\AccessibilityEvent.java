package de.mossgrabers.projectconverter.gui.accessibility;

import javafx.scene.Node;

import java.time.LocalDateTime;

/**
 * Event class for accessibility-related events in the application.
 * Used to notify listeners about focus changes, screen reader announcements, etc.
 * 
 * <AUTHOR> ProjectConverter
 */
public class AccessibilityEvent
{
    /**
     * Types of accessibility events.
     */
    public enum Type
    {
        FOCUS_CHANGED,
        SCREEN_READER_ANNOUNCEMENT,
        HIGH_CONTRAST_CHANGED,
        FONT_SIZE_CHANGED,
        KEYBOARD_NAVIGATION_CHANGED,
        ACCESSIBILITY_FEATURE_TOGGLED
    }
    
    private final Type type;
    private final Node source;
    private final String message;
    private final Object data;
    private final LocalDateTime timestamp;
    
    /**
     * Constructor.
     * 
     * @param type The event type
     * @param source The source node (can be null)
     */
    public AccessibilityEvent(final Type type, final Node source)
    {
        this(type, source, null, null);
    }
    
    /**
     * Constructor with message.
     * 
     * @param type The event type
     * @param source The source node (can be null)
     * @param message The event message
     */
    public AccessibilityEvent(final Type type, final Node source, final String message)
    {
        this(type, source, message, null);
    }
    
    /**
     * Constructor with message and data.
     * 
     * @param type The event type
     * @param source The source node (can be null)
     * @param message The event message
     * @param data Additional event data
     */
    public AccessibilityEvent(final Type type, final Node source, final String message, final Object data)
    {
        this.type = type;
        this.source = source;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * Get the event type.
     * 
     * @return The event type
     */
    public Type getType()
    {
        return this.type;
    }
    
    /**
     * Get the source node.
     * 
     * @return The source node
     */
    public Node getSource()
    {
        return this.source;
    }
    
    /**
     * Get the event message.
     * 
     * @return The event message
     */
    public String getMessage()
    {
        return this.message;
    }
    
    /**
     * Get additional event data.
     * 
     * @return The event data
     */
    public Object getData()
    {
        return this.data;
    }
    
    /**
     * Get the event timestamp.
     * 
     * @return The timestamp
     */
    public LocalDateTime getTimestamp()
    {
        return this.timestamp;
    }
    
    /**
     * Check if this event has a message.
     * 
     * @return True if the event has a message
     */
    public boolean hasMessage()
    {
        return this.message != null && !this.message.trim().isEmpty();
    }
    
    /**
     * Check if this event has additional data.
     * 
     * @return True if the event has data
     */
    public boolean hasData()
    {
        return this.data != null;
    }
    
    /**
     * Get the data as a specific type.
     * 
     * @param <T> The expected data type
     * @param clazz The class of the expected type
     * @return The data cast to the specified type, or null if not compatible
     */
    @SuppressWarnings("unchecked")
    public <T> T getDataAs(final Class<T> clazz)
    {
        if (this.data != null && clazz.isInstance(this.data))
        {
            return (T) this.data;
        }
        return null;
    }
    
    @Override
    public String toString()
    {
        final StringBuilder sb = new StringBuilder();
        sb.append("AccessibilityEvent{");
        sb.append("type=").append(this.type);
        sb.append(", timestamp=").append(this.timestamp);
        
        if (this.source != null)
        {
            sb.append(", source=").append(this.source.getClass().getSimpleName());
        }
        
        if (hasMessage())
        {
            sb.append(", message='").append(this.message).append("'");
        }
        
        if (hasData())
        {
            sb.append(", data=").append(this.data);
        }
        
        sb.append("}");
        return sb.toString();
    }
    
    /**
     * Create a focus changed event.
     * 
     * @param newFocusNode The newly focused node
     * @return A focus changed event
     */
    public static AccessibilityEvent focusChanged(final Node newFocusNode)
    {
        return new AccessibilityEvent(Type.FOCUS_CHANGED, newFocusNode, 
                                    "Focus changed to " + getNodeDescription(newFocusNode));
    }
    
    /**
     * Create a screen reader announcement event.
     * 
     * @param announcement The announcement text
     * @return A screen reader announcement event
     */
    public static AccessibilityEvent screenReaderAnnouncement(final String announcement)
    {
        return new AccessibilityEvent(Type.SCREEN_READER_ANNOUNCEMENT, null, announcement);
    }
    
    /**
     * Create a high contrast mode changed event.
     * 
     * @param enabled Whether high contrast mode is enabled
     * @return A high contrast changed event
     */
    public static AccessibilityEvent highContrastChanged(final boolean enabled)
    {
        return new AccessibilityEvent(Type.HIGH_CONTRAST_CHANGED, null, 
                                    "High contrast mode " + (enabled ? "enabled" : "disabled"), enabled);
    }
    
    /**
     * Create a font size changed event.
     * 
     * @param multiplier The new font size multiplier
     * @return A font size changed event
     */
    public static AccessibilityEvent fontSizeChanged(final double multiplier)
    {
        return new AccessibilityEvent(Type.FONT_SIZE_CHANGED, null, 
                                    String.format("Font size changed to %.1fx", multiplier), multiplier);
    }
    
    /**
     * Create a keyboard navigation changed event.
     * 
     * @param enhanced Whether enhanced keyboard navigation is enabled
     * @return A keyboard navigation changed event
     */
    public static AccessibilityEvent keyboardNavigationChanged(final boolean enhanced)
    {
        return new AccessibilityEvent(Type.KEYBOARD_NAVIGATION_CHANGED, null, 
                                    "Enhanced keyboard navigation " + (enhanced ? "enabled" : "disabled"), enhanced);
    }
    
    /**
     * Create an accessibility feature toggled event.
     * 
     * @param featureName The name of the feature
     * @param enabled Whether the feature is enabled
     * @return An accessibility feature toggled event
     */
    public static AccessibilityEvent featureToggled(final String featureName, final boolean enabled)
    {
        return new AccessibilityEvent(Type.ACCESSIBILITY_FEATURE_TOGGLED, null, 
                                    featureName + " " + (enabled ? "enabled" : "disabled"), enabled);
    }
    
    /**
     * Get a description of a node for accessibility purposes.
     * 
     * @param node The node
     * @return A description of the node
     */
    private static String getNodeDescription(final Node node)
    {
        if (node == null)
        {
            return "null";
        }
        
        if (node instanceof javafx.scene.control.Labeled)
        {
            final javafx.scene.control.Labeled labeled = (javafx.scene.control.Labeled) node;
            final String text = labeled.getText();
            if (text != null && !text.trim().isEmpty())
            {
                return text;
            }
        }
        
        if (node instanceof javafx.scene.control.TextInputControl)
        {
            return "text input";
        }
        
        // Use accessible text if available
        final String accessibleText = node.getAccessibleText();
        if (accessibleText != null && !accessibleText.trim().isEmpty())
        {
            return accessibleText;
        }
        
        // Fall back to class name
        return node.getClass().getSimpleName();
    }
}
