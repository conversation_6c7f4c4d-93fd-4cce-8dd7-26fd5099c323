{"doc": "\n A container for all DAWproject components: metadata, project and embedded files.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getName", "paramTypes": [], "doc": "\n Get the name of the project.\r\n\r\n @return The projects' name\r\n"}, {"name": "getMetadata", "paramTypes": [], "doc": "\n Get the metadata.\r\n\r\n @return The metadata\r\n"}, {"name": "getProject", "paramTypes": [], "doc": "\n Get the project.\r\n\r\n @return The project\r\n"}, {"name": "getMediaFiles", "paramTypes": [], "doc": "\n Get the accessor to the media files.\r\n\r\n @return The media files\r\n"}, {"name": "getTempo", "paramTypes": [], "doc": "\n Get the beats per second of the timeline. Currently, static but should calculated from the\r\n timeline position in the future.\r\n\r\n @return The beats per second\r\n"}, {"name": "close", "paramTypes": [], "doc": "{@inheritDoc} "}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String", "de.mossgrabers.projectconverter.core.IMediaFiles"], "doc": "\n Constructor.\r\n\r\n @param name The name of the project\r\n @param mediaFiles Access to additional media files\r\n"}, {"name": "<init>", "paramTypes": ["java.io.File", "de.mossgrabers.projectconverter.core.IMediaFiles"], "doc": "\n Constructor.\r\n\r\n @param dawProjectSourceFile A dawproject to load\r\n @param mediaFiles Access to additional media files\r\n @throws IOException Could not load the file\r\n"}]}