{"doc": " Exception thrown during format conversion operations.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getSourceFormat", "paramTypes": [], "doc": " Get the source format.\n \n @return The source format\n"}, {"name": "getTargetFormat", "paramTypes": [], "doc": " Get the target format.\n \n @return The target format\n"}, {"name": "getConversionStep", "paramTypes": [], "doc": " Get the conversion step where the error occurred.\n \n @return The conversion step\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean"], "doc": " Constructor for conversion exception.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param sourceFormat The source format\n @param targetFormat The target format\n @param conversionStep The step where conversion failed\n @param recoverable Whether the error is recoverable\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.lang.Throwable"], "doc": " Constructor with cause.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param sourceFormat The source format\n @param targetFormat The target format\n @param conversionStep The step where conversion failed\n @param recoverable Whether the error is recoverable\n @param cause The underlying cause\n"}]}