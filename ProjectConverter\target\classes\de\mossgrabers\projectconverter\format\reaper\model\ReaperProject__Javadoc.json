{"doc": "\n Support for parsing and formatting Reaper project files.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "parse", "paramTypes": ["java.util.List"], "doc": "\n Parses the lines of a Reaper project files into its' parts the so-called chunks.\r\n\r\n @param lines The lines to parse\r\n @return The parsed chunks\r\n @throws ParseException Error during parsing\r\n"}, {"name": "parseChunk", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.util.List", "int"], "doc": "\n Parses a chunk.\r\n\r\n @param chunk The chunk to fill\r\n @param lines The lines to parse from\r\n @param lineIndex The current index of the line to parse\r\n @return The index of the last parsed chunk\r\n @throws ParseException Error during parsing\r\n"}, {"name": "parseNode", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "java.lang.String"], "doc": "\n Parses a node (name/values pair) of a chunk.\r\n\r\n @param node The node to fill\r\n @param line The line to parse from\r\n @return The node for convenience\r\n"}, {"name": "parseLine", "paramTypes": ["java.lang.String"], "doc": "\n Splits the line of a node into its' parts. Handles single and double quotes.\r\n\r\n @param line The line to split\r\n @return The parts\r\n"}, {"name": "format", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Formats the root chunk as a Reaper project file.\r\n\r\n @param rootChunk The root chunk\r\n @return The formatted file content\r\n"}, {"name": "formatChunk", "paramTypes": ["java.lang.StringBuilder", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Format recursively the chunk and all sub-chunks.\r\n\r\n @param result Where to append the formatted sub-chunks\r\n @param chunk The chunk to format\r\n"}, {"name": "formatNode", "paramTypes": ["java.lang.StringBuilder", "de.mossgrabers.projectconverter.format.reaper.model.Node"], "doc": "\n Format one node (one name/values line).\r\n\r\n @param result Where to add the formatted line\r\n @param node The node to format\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Constructor.\r\n"}]}