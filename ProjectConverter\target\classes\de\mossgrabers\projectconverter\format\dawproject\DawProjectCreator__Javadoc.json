{"doc": "\n The dawproject project destination.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEditPane", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "loadSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "saveSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "needsOverwrite", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "write", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "replaceArrangementClips", "paramTypes": ["com.bitwig.dawproject.Project"], "doc": "\n Create an arrangement from the Clip Launcher Data. This puts all clips from the Scenes in the\r\n DAWproject's Arranger. Each scene section has the length of the longest clip in the scene.\r\n Additionally, range markers are created for each scene using the name of the scene.\r\n\r\n @param project The DAWproject to modify\r\n"}, {"name": "getClipSlots", "paramTypes": ["java.util.List"], "doc": "\n Get all timeline objects which are clip slots.\r\n\r\n @param timelineObjects The timeline objects to filter\r\n @return The clip slots\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param notifier The notifier for error messages\r\n"}]}