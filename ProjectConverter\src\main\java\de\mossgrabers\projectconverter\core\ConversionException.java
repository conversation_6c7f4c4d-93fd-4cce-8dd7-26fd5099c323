package de.mossgrabers.projectconverter.core;

/**
 * Exception thrown during format conversion operations.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ConversionException extends ProjectConverterException
{
    private static final long serialVersionUID = 1L;
    
    private final String sourceFormat;
    private final String targetFormat;
    private final String conversionStep;
    
    /**
     * Constructor for conversion exception.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param sourceFormat The source format
     * @param targetFormat The target format
     * @param conversionStep The step where conversion failed
     * @param recoverable Whether the error is recoverable
     */
    public ConversionException(final ErrorCode errorCode, final String userMessage, 
                             final String sourceFormat, final String targetFormat, 
                             final String conversionStep, final boolean recoverable)
    {
        super(errorCode, userMessage, 
              String.format("Conversion from %s to %s failed at step: %s", 
                          sourceFormat, targetFormat, conversionStep), 
              recoverable);
        this.sourceFormat = sourceFormat;
        this.targetFormat = targetFormat;
        this.conversionStep = conversionStep;
    }
    
    /**
     * Constructor with cause.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param sourceFormat The source format
     * @param targetFormat The target format
     * @param conversionStep The step where conversion failed
     * @param recoverable Whether the error is recoverable
     * @param cause The underlying cause
     */
    public ConversionException(final ErrorCode errorCode, final String userMessage, 
                             final String sourceFormat, final String targetFormat, 
                             final String conversionStep, final boolean recoverable, 
                             final Throwable cause)
    {
        super(errorCode, userMessage, 
              String.format("Conversion from %s to %s failed at step: %s", 
                          sourceFormat, targetFormat, conversionStep), 
              recoverable, cause);
        this.sourceFormat = sourceFormat;
        this.targetFormat = targetFormat;
        this.conversionStep = conversionStep;
    }
    
    /**
     * Get the source format.
     * 
     * @return The source format
     */
    public String getSourceFormat()
    {
        return this.sourceFormat;
    }
    
    /**
     * Get the target format.
     * 
     * @return The target format
     */
    public String getTargetFormat()
    {
        return this.targetFormat;
    }
    
    /**
     * Get the conversion step where the error occurred.
     * 
     * @return The conversion step
     */
    public String getConversionStep()
    {
        return this.conversionStep;
    }
}
