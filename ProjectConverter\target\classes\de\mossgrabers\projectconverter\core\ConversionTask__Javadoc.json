{"doc": "\n The task to run the actual conversion process with enhanced error handling and validation.\r\n\r\n <AUTHOR>\r\n <AUTHOR> ProjectConverter\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "call", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "performPreConversionValidation", "paramTypes": [], "doc": "\n Perform pre-conversion validation of input files and settings.\r\n\r\n @return True if validation passed, false otherwise\r\n"}, {"name": "performProjectValidation", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer"], "doc": "\n Perform project validation using the enhanced project validator.\r\n\r\n @param dawProject The project to validate\r\n @return True if validation passed, false otherwise\r\n"}, {"name": "performOutputValidation", "paramTypes": [], "doc": "\n Perform output validation.\r\n\r\n @return True if validation passed, false otherwise\r\n"}, {"name": "hasValidationErrors", "paramTypes": ["java.util.List"], "doc": "\n Check if validation results contain errors.\r\n\r\n @param results The validation results\r\n @return True if there are errors\r\n"}, {"name": "logValidationResults", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n Log validation results to the notifier.\r\n\r\n @param context The validation context\r\n @param results The validation results\r\n"}, {"name": "cleanup", "paramTypes": [], "doc": "\n Cleanup resources used during conversion.\r\n"}, {"name": "waitForDelivery", "paramTypes": [], "doc": "\n Wait a bit.\r\n\r\n @return The thread was cancelled if true\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.io.File", "java.io.File", "de.mossgrabers.projectconverter.core.ISourceFormat", "de.mossgrabers.projectconverter.core.IDestinationFormat", "de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor with enhanced error handling and validation.\r\n\r\n @param sourceFile The source file to convert\r\n @param outputPath The output path in which to write the destination file\r\n @param sourceFormat The format of the source file\r\n @param destinationFormat The destination format\r\n @param notifier Where to log to\r\n"}]}