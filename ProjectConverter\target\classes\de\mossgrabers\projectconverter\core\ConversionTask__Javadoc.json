{"doc": "\n The task to run the actual conversion process.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "call", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "waitForDelivery", "paramTypes": [], "doc": "\n Wait a bit.\r\n\r\n @return The thread was cancelled if true\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.io.File", "java.io.File", "de.mossgrabers.projectconverter.core.ISourceFormat", "de.mossgrabers.projectconverter.core.IDestinationFormat", "de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param sourceFile The source file to convert\r\n @param outputPath The output path in which to write the destination file\r\n @param sourceFormat The format of the source file\r\n @param destinationFormat The destination format\r\n @param notifier Where to log to\r\n"}]}