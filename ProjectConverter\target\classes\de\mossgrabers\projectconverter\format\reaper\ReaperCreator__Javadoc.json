{"doc": "\n Converts a Reaper project file (the already loaded chunks to be more specific) into a dawproject\r\n structure. Needs to be state-less.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEditPane", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "loadSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "saveSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "needsOverwrite", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "write", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "saveProject", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.core.DawProjectContainer", "de.mossgrabers.projectconverter.core.IMediaFiles", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters", "java.io.File"], "doc": "\n Stores the filled Reaper structure into the given file.\r\n\r\n @param rootChunk The root chunk of the Reaper project to store\r\n @param dawProject The name of the project file to store to\r\n @param mediaFiles The media files\r\n @param parameters The parameters\r\n @param destinationPath The path to store the project and audio files to\r\n @throws IOException Could not write the file\r\n"}, {"name": "convertMetadata", "paramTypes": ["com.bitwig.dawproject.MetaData", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Assigns the metadata to different Reaper settings.\r\n\r\n @param metadata The metadata to read from\r\n @param rootChunk The root chunk to add the information\r\n"}, {"name": "convertTransport", "paramTypes": ["com.bitwig.dawproject.Project", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Assigns the Transport data to different Reaper settings.\r\n\r\n @param project The project to read from\r\n @param rootChunk The root chunk to add the information\r\n @param parameters The parameters\r\n"}, {"name": "convertMaster", "paramTypes": ["de.mossgrabers.projectconverter.core.IMediaFiles", "com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Assigns the Master track data to different Reaper settings.\r\n\r\n @param mediaFiles Access to additional media files\r\n @param masterTrack The master track\r\n @param rootChunk The root chunk to add the information\r\n @throws IOException Units must be linear\r\n"}, {"name": "convertTracks", "paramTypes": ["de.mossgrabers.projectconverter.core.IMediaFiles", "java.util.List", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Assigns the data of all Tracks to different Reaper settings.\r\n\r\n @param mediaFiles Access to additional media files\r\n @param lanes The lanes which contain the tracks to convert\r\n @param rootChunk The root chunk to add the information\r\n @param parameters The parameters\r\n @throws IOException Units must be linear\r\n"}, {"name": "convertSends", "paramTypes": ["com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Convert all sends of the track.\r\n\r\n @param track The track\r\n @param parameters The parameters\r\n @throws IOException Units must be linear\r\n"}, {"name": "convertDevices", "paramTypes": ["de.mossgrabers.projectconverter.core.IMediaFiles", "java.util.List", "boolean", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.lang.String"], "doc": "\n Assigns the data of all Devices of a track to different Reaper settings.\r\n\r\n @param mediaFiles Access to additional media files\r\n @param devices The devices to convert\r\n @param isTrackActive Is the track active?\r\n @param parentChunk The chunk where to add the data\r\n @param fxChainName The name of the FX chain chunk\r\n @throws IOException Could not create the VST chunks\r\n"}, {"name": "convertDevice", "paramTypes": ["com.bitwig.dawproject.device.Device", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.core.IMediaFiles", "boolean"], "doc": "\n Assigns the data of one Device of a track to different Reaper settings.\r\n\r\n @param device The device to convert\r\n @param fxChunk The FX chunk where to add the device information\r\n @param mediaFiles Access to additional media files\r\n @param isTrackActive Is the track active?\r\n @throws IOException Could not create the VST chunks\r\n"}, {"name": "convertVstDevice", "paramTypes": ["com.bitwig.dawproject.device.Device", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.core.IMediaFiles"], "doc": "\n Convert a VST device to Reaper.\r\n\r\n @param device The device to convert\r\n @param fxChunk The FX chunk where to add the device information\r\n @param mediaFiles Access to additional media files\r\n @throws IOException Could not create the VST chunks\r\n"}, {"name": "convertClapDevice", "paramTypes": ["com.bitwig.dawproject.device.ClapPlugin", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.core.IMediaFiles"], "doc": "\n Convert a CLAP device to Reaper.\r\n\r\n @param clapPlugin The CLAP plug-in to convert\r\n @param fxChunk The FX chunk where to add the device information\r\n @param mediaFiles Access to additional media files\r\n @throws IOException Could not create the VST chunks\r\n"}, {"name": "convertTrack", "paramTypes": ["de.mossgrabers.projectconverter.core.IMediaFiles", "java.util.List", "int", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Convert a single track.\r\n\r\n @param mediaFiles All media files, needed for the device states\r\n @param flatTracks All flattened\r\n @param trackIndex The index of the track to convert\r\n @param rootChunk The root chunk to add the information\r\n @param parameters The parameters\r\n @throws IOException Error converting the track\r\n"}, {"name": "convertClips", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "com.bitwig.dawproject.Track", "com.bitwig.dawproject.timeline.Clips", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.ParentClip", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Recursively convert grouped clips into top level media items, since Reaper does not support\r\n to wrap clips into a parent clip.\r\n\r\n @param trackChunk The Reaper track chunk\r\n @param track The track which contains the items\r\n @param clips The clips to convert\r\n @param parentClip Some aggregated info about the parent clip(s)\r\n @param parameters The parameters\r\n"}, {"name": "convertWarps", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters", "com.bitwig.dawproject.timeline.Warps", "boolean"], "doc": "\n Converts a warped audio clip. Can only handle simple Warps with one warp marker at the\r\n beginning and one at the end of the audio clip.\r\n\r\n @param itemChunk The item chunk\r\n @param parameters The parameters to add to the node\r\n @param warps The warps information with an audio file and several warp events\r\n @param sourceIsBeats True if the time is in beats otherwise in seconds\r\n"}, {"name": "calcPlayrate", "paramTypes": ["java.util.List", "boolean", "boolean", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Calculate the play rate of the media item in Reaper from the a list of warp events. Since\r\n there is only one play rate the logic takes only the first 2 warp events into account.\r\n\r\n @param events The warp events\r\n @param sourceIsBeats True if the time is in beats otherwise in seconds\r\n @param contentTimeIsBeats True if the time of the warped content is in beats otherwise in\r\n            seconds\r\n @param parameters The parameters to add to the node\r\n @return The play rate (1 = normal playback, < 1 slower, > 1 faster)\r\n"}, {"name": "convertAudio", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters", "com.bitwig.dawproject.timeline.Audio", "double"], "doc": "\n Convert an audio clip.\r\n\r\n @param itemChunk The item chunk\r\n @param parameters The parameters to add to the node\r\n @param audio The audio file\r\n @param playRate The play rate of the media item\r\n"}, {"name": "convertMIDI", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.lang.Double", "com.bitwig.dawproject.timeline.Clip", "com.bitwig.dawproject.timeline.Notes", "double", "boolean", "boolean"], "doc": "\n Convert a MIDI clip.\r\n\r\n @param itemChunk The chunk of the media item to fill\r\n @param tempo The current tempo\r\n @param clip The clip to convert\r\n @param notes The notes of the clip\r\n @param clipDuration The duration of the clip\r\n @param clipDurationIsBeats True if the clip duration time is in beats otherwise in seconds\r\n @param clipContentIsBeats True if the time is in beats otherwise in seconds\r\n"}, {"name": "convertArrangementLanes", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "com.bitwig.dawproject.Project", "com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters", "boolean"], "doc": "\n Convert all lanes. Assigns the Markers data to different Reaper settings.\r\n\r\n @param rootChunk The root chunk to add the information\r\n @param project The project to read from\r\n @param masterTrack The master track\r\n @param parameters The parameters\r\n @param arrangementIsBeats If true the arrangement time base is in beats otherwise seconds\r\n"}, {"name": "convertScenes", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "com.bitwig.dawproject.Project", "com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters", "boolean"], "doc": "\n Converts all scenes.\r\n\r\n @param rootChunk The root chunk to add the information\r\n @param project The project to read from\r\n @param masterTrack The master track\r\n @param parameters The parameters\r\n @param sourceIsBeats If true the source time base is in beats otherwise seconds\r\n"}, {"name": "createTempoSignatureEnvelope", "paramTypes": ["boolean", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Combines the tempo and signature automation into the Reaper tempo envelope.\r\n\r\n @param sourceIsBeats If true the source time base is in beats otherwise seconds\r\n @param rootChunk The root chunk to add the information\r\n @param parameters The parameters to add to the node\r\n"}, {"name": "createTrack", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "com.bitwig.dawproject.Track", "int", "int"], "doc": "\n Set the basic track information, like structure, name and color.\r\n\r\n @param trackChunk The track chunk to add the information\r\n @param track The DAWproject object\r\n @param type The folder type\r\n @param direction The level direction\r\n"}, {"name": "createTrackStructure", "paramTypes": ["java.util.List", "java.util.List", "boolean", "de.mossgrabers.projectconverter.format.reaper.ReaperCreator.Parameters"], "doc": "\n Convert the track hierarchy into a flat list in which two parameters indicate the track state\r\n (1 = start track, 2 = end of track) and a direction the number of levels to move into our out\r\n of folders (1 = move into a folder, -X = X number of levels to move up).\r\n\r\n @param tracks The current list of sub-folders and -tracks.\r\n @param flatTracks The list with all flat tracks so far\r\n @param isTop True if this is the top level\r\n @param parameters The parameters\r\n"}, {"name": "createDeviceName", "paramTypes": ["com.bitwig.dawproject.device.Device"], "doc": "\n Creates the Reaper device name for the VST chunk.\r\n\r\n @param device The device for which to create a name\r\n @return The name\r\n"}, {"name": "addChunk", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.lang.String", "java.lang.String[]"], "doc": "\n Create and add a chunk to a parent chunk.\r\n\r\n @param parentChunk The parent chunk to which to add the new chunk\r\n @param childName The name of the new chunk\r\n @param parameters The parameters to add to the chunk\r\n @return The newly created chunk\r\n"}, {"name": "addNode", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.lang.String", "java.lang.String[]"], "doc": "\n Create and add a node to a parent chunk.\r\n\r\n @param parentChunk The parent chunk to which to add the new node\r\n @param childName The name of the new node\r\n @param parameters The parameters to add to the node\r\n @return The newly created node\r\n"}, {"name": "addNode", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.model.Node", "java.lang.String", "java.lang.String[]"], "doc": "\n Add a node to a parent chunk.\r\n\r\n @param parentChunk The parent chunk to which to add the new node\r\n @param child The child node to add to the parent chunk\r\n @param childName The name to set for the child node\r\n @param parameters The parameters to add to the node\r\n @return The added node\r\n"}, {"name": "setNode", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "java.lang.String", "java.lang.String[]"], "doc": "\n Set the name and parameters of a node.\r\n\r\n @param node The node\r\n @param nodeName The name to set for the node\r\n @param parameters The parameters to add to the node\r\n"}, {"name": "fromHexColor", "paramTypes": ["java.lang.String"], "doc": "\n Parse a hex string to a ARGB color.\r\n\r\n @param hexColor The hex color to parse\r\n @return The color\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param notifier The notifier\r\n"}]}