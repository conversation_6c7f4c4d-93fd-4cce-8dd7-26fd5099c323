{"doc": "\n Helper class to convert between seconds and beats on a tempo timeline.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "secondsTo<PERSON><PERSON>s", "paramTypes": ["double", "double"], "doc": "\n Convert a time value in seconds to beats.\r\n\r\n @param seconds The time value in seconds\r\n @param tempo The tempo in BPM\r\n @return The time in beats\r\n"}, {"name": "secondsTo<PERSON><PERSON>s", "paramTypes": ["double", "java.util.List"], "doc": "\n Calculates the number of beats at a certain position which is given in seconds based on a\r\n tempo map. If the tempo change is linear (isLinear is true), it will calculate the average\r\n tempo between the current tempo and the next tempo, and use this average tempo to calculate\r\n the beats. If seconds is within a linear tempo change, it will calculate the proportion of\r\n the elapsed time within this tempo change and adjust the current tempo accordingly.\r\n \r\n @param seconds The seconds value\r\n @param tempoMap The tempo map\r\n @return The value in beats\r\n"}, {"name": "beatsToSeconds", "paramTypes": ["double", "double"], "doc": "\n Convert a time value in beats to seconds.\r\n\r\n @param beats The time value in beats\r\n @param tempo The tempo in BPM\r\n @return The time in seconds\r\n"}, {"name": "beatsToSeconds", "paramTypes": ["double", "java.util.List"], "doc": "\n Calculates the seconds at a certain position which is given in number of beats based on a\r\n tempo map. If the tempo change is linear (isLinear is true), it will calculate the proportion\r\n of the elapsed beats within this tempo change and adjust the current time accordingly. If\r\n beats is within a linear tempo change, it will calculate the average tempo using the\r\n proportion of the elapsed beats and use this average tempo to calculate the seconds.\r\n \r\n @param beats The beats value\r\n @param tempoMap The tempo-map\r\n @return The value in seconds\r\n"}], "constructors": []}