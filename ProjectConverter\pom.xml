<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>de.mossgrabers</groupId>
	<artifactId>projectconverter</artifactId>
	<version>1.2.10</version>
	<name>ProjectConverter</name>
	<description>Convert from/to a specific DAW project format to/from generic dawproject</description>
	<organization>
		<name><PERSON><PERSON><PERSON></name>
		<url>http://www.mossgrabers.de</url>
	</organization>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<main.class>de.mossgrabers.projectconverter.ui.ProjectConverterApp</main.class>
	</properties>

	<repositories>
		<repository>
			<id>maven-local-repository</id>
			<url>file:///${project.basedir}/maven-local-repository</url>
		</repository>
		<repository>
			<id>MavenCentral</id>
			<name>Maven Central Repository</name>
			<url>https://mvnrepository.com</url>
		</repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>org.openjfx</groupId>
			<artifactId>javafx-controls</artifactId>
			<version>23.0.2</version>
		</dependency>
		<dependency>
			<artifactId>uitools</artifactId>
			<groupId>de.mossgrabers</groupId>
			<version>1.6.0</version>
		</dependency>
		<dependency>
			<groupId>com.bitwig.open</groupId>
			<artifactId>dawproject</artifactId>
			<version>1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.github.trilarion</groupId>
			<artifactId>java-vorbis-support</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.tianscar.javasound</groupId>
			<artifactId>javasound-flac</artifactId>
			<version>1.4.1</version>
		</dependency>
	</dependencies>

	<build>

		<plugins>

			<!-- Enforce a minimum Maven version -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.5.0</version>
				<executions>
					<execution>
						<id>enforce-maven</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireMavenVersion>
									<version>3.8.1</version>
								</requireMavenVersion>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.13.0</version>
				<configuration>
					<fork>true</fork>
					<source>21</source>
					<target>21</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.4.2</version>
				<configuration>
					<outputDirectory>target/lib</outputDirectory>
					<archive>
						<manifest>
							<mainClass>${main.class}</mainClass>
							<addClasspath>true</addClasspath>
						</manifest>
						<manifestEntries>
							<Specification-Title>${project.name}</Specification-Title>
							<Specification-Version>${project.version}</Specification-Version>
							<Specification-Vendor>${project.organization.name}</Specification-Vendor>
							<Implementation-Title>${project.name}</Implementation-Title>
							<Implementation-Version>${project.version}</Implementation-Version>
							<Implementation-Vendor-Id>${project.groupId}</Implementation-Vendor-Id>
							<Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.openjfx</groupId>
				<artifactId>javafx-maven-plugin</artifactId>
				<version>0.0.8</version>
				<configuration>
					<mainClass>de.mossgrabers.projectconverter/${main.class}</mainClass>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.8.1</version>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>resolve</goal>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<includeScope>runtime</includeScope>
							<outputDirectory>${project.build.directory}/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.panteleyev</groupId>
				<artifactId>jpackage-maven-plugin</artifactId>
				<version>1.6.5</version>
				<configuration>
					<input>target/lib</input>
					<destination>target/release</destination>
					<mainJar>projectconverter-${project.version}.jar</mainJar>
					<mainClass>${main.class}</mainClass>
					<name>${project.name}</name>
					<appVersion>${project.version}</appVersion>
					<copyright>(c) 2023-2024 by ${project.organization.name}</copyright>
					<description>A tool for converting multi-sample from one format to another.</description>
					<vendor>${project.organization.name}</vendor>
					<licenseFile>LICENSE</licenseFile>
				</configuration>
				<executions>
					<execution>
						<id>mac</id>
						<configuration>
							<type>DMG</type>
							<icon>icons/projectconverter.icns</icon>
						</configuration>
					</execution>
					<execution>
						<id>win</id>
						<configuration>
							<type>MSI</type>
							<icon>icons/projectconverter.ico</icon>
							<winMenu>true</winMenu>
							<winPerUserInstall>false</winPerUserInstall>
							<winDirChooser>true</winDirChooser>
							<winUpgradeUuid>9cd929a1-b574-4998-9d48-bccf86ca3b6b</winUpgradeUuid>
						</configuration>
					</execution>
					<execution>
						<id>linux</id>
						<configuration>
							<type>DEB</type>
							<icon>src/main/resources/de/mossgrabers/projectconverter/images/AppIcon.png</icon>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Check for outdated libraries -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<version>2.18.0</version>
				<configuration>
					<ignoredVersions>.*-M.*,.*-alpha.*,.*-beta.*,.*-ea.*</ignoredVersions>
					<generateBackupPoms>false</generateBackupPoms>
				</configuration>
			</plugin>

			<!-- Plugins without configuration but for version settings. -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-clean-plugin</artifactId>
				<version>3.4.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>3.1.3</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<version>3.1.3</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.21.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.5.2</version>
			</plugin>

		</plugins>

	</build>

</project>