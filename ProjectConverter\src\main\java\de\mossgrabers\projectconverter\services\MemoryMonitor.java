package de.mossgrabers.projectconverter.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Memory monitoring service for tracking memory usage and detecting potential issues.
 * Provides real-time memory monitoring and alerts for memory pressure.
 * 
 * <AUTHOR> ProjectConverter
 */
public class MemoryMonitor
{
    private static final Logger LOGGER = LoggerFactory.getLogger(MemoryMonitor.class);
    
    private static final double WARNING_THRESHOLD = 0.8; // 80% memory usage
    private static final double CRITICAL_THRESHOLD = 0.9; // 90% memory usage
    private static final long DEFAULT_MONITORING_INTERVAL = 5000; // 5 seconds
    
    private final MemoryMXBean memoryBean;
    private final List<Consumer<MemoryStatus>> listeners;
    private final ScheduledExecutorService scheduler;
    
    private boolean monitoring;
    private long monitoringInterval;
    private MemoryStatus lastStatus;
    
    /**
     * Constructor with default monitoring interval.
     */
    public MemoryMonitor()
    {
        this(DEFAULT_MONITORING_INTERVAL);
    }
    
    /**
     * Constructor with custom monitoring interval.
     * 
     * @param monitoringInterval Monitoring interval in milliseconds
     */
    public MemoryMonitor(final long monitoringInterval)
    {
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        this.listeners = new ArrayList<>();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            final Thread t = new Thread(r, "MemoryMonitor");
            t.setDaemon(true);
            return t;
        });
        this.monitoringInterval = monitoringInterval;
        this.monitoring = false;
    }
    
    /**
     * Start memory monitoring.
     */
    public synchronized void startMonitoring()
    {
        if (this.monitoring)
        {
            LOGGER.debug("Memory monitoring is already running");
            return;
        }
        
        this.monitoring = true;
        this.scheduler.scheduleAtFixedRate(this::checkMemoryStatus, 
                                         0, this.monitoringInterval, TimeUnit.MILLISECONDS);
        LOGGER.info("Memory monitoring started with interval: {} ms", this.monitoringInterval);
    }
    
    /**
     * Stop memory monitoring.
     */
    public synchronized void stopMonitoring()
    {
        if (!this.monitoring)
        {
            LOGGER.debug("Memory monitoring is not running");
            return;
        }
        
        this.monitoring = false;
        LOGGER.info("Memory monitoring stopped");
    }
    
    /**
     * Shutdown the memory monitor and cleanup resources.
     */
    public void shutdown()
    {
        stopMonitoring();
        this.scheduler.shutdown();
        try
        {
            if (!this.scheduler.awaitTermination(5, TimeUnit.SECONDS))
            {
                this.scheduler.shutdownNow();
            }
        }
        catch (final InterruptedException e)
        {
            this.scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        LOGGER.info("Memory monitor shutdown complete");
    }
    
    /**
     * Get current memory status.
     * 
     * @return Current memory status
     */
    public MemoryStatus getCurrentStatus()
    {
        final MemoryUsage heapUsage = this.memoryBean.getHeapMemoryUsage();
        final MemoryUsage nonHeapUsage = this.memoryBean.getNonHeapMemoryUsage();
        
        return new MemoryStatus(
            System.currentTimeMillis(),
            heapUsage.getUsed(),
            heapUsage.getMax(),
            nonHeapUsage.getUsed(),
            nonHeapUsage.getMax(),
            Runtime.getRuntime().freeMemory(),
            Runtime.getRuntime().totalMemory(),
            Runtime.getRuntime().maxMemory()
        );
    }
    
    /**
     * Add a memory status listener.
     * 
     * @param listener The listener to add
     */
    public void addListener(final Consumer<MemoryStatus> listener)
    {
        synchronized (this.listeners)
        {
            this.listeners.add(listener);
        }
    }
    
    /**
     * Remove a memory status listener.
     * 
     * @param listener The listener to remove
     */
    public void removeListener(final Consumer<MemoryStatus> listener)
    {
        synchronized (this.listeners)
        {
            this.listeners.remove(listener);
        }
    }
    
    /**
     * Force garbage collection and return memory status.
     * 
     * @return Memory status after garbage collection
     */
    public MemoryStatus forceGarbageCollection()
    {
        LOGGER.debug("Forcing garbage collection");
        final MemoryStatus beforeGC = getCurrentStatus();
        
        System.gc();
        System.runFinalization();
        
        // Wait a bit for GC to complete
        try
        {
            Thread.sleep(100);
        }
        catch (final InterruptedException e)
        {
            Thread.currentThread().interrupt();
        }
        
        final MemoryStatus afterGC = getCurrentStatus();
        LOGGER.debug("GC completed. Memory freed: {} MB", 
                   (beforeGC.getHeapUsed() - afterGC.getHeapUsed()) / (1024 * 1024));
        
        return afterGC;
    }
    
    /**
     * Check if memory usage is above warning threshold.
     * 
     * @return True if memory usage is high
     */
    public boolean isMemoryUsageHigh()
    {
        final MemoryStatus status = getCurrentStatus();
        return status.getHeapUsageRatio() > WARNING_THRESHOLD;
    }
    
    /**
     * Check if memory usage is critical.
     * 
     * @return True if memory usage is critical
     */
    public boolean isMemoryUsageCritical()
    {
        final MemoryStatus status = getCurrentStatus();
        return status.getHeapUsageRatio() > CRITICAL_THRESHOLD;
    }
    
    /**
     * Get memory usage statistics as a formatted string.
     * 
     * @return Formatted memory statistics
     */
    public String getMemoryStatistics()
    {
        final MemoryStatus status = getCurrentStatus();
        return status.getFormattedStatistics();
    }
    
    /**
     * Set the monitoring interval.
     * 
     * @param intervalMs Monitoring interval in milliseconds
     */
    public void setMonitoringInterval(final long intervalMs)
    {
        this.monitoringInterval = intervalMs;
        if (this.monitoring)
        {
            // Restart monitoring with new interval
            stopMonitoring();
            startMonitoring();
        }
    }
    
    private void checkMemoryStatus()
    {
        try
        {
            final MemoryStatus currentStatus = getCurrentStatus();
            
            // Check for significant changes or threshold breaches
            if (shouldNotifyListeners(currentStatus))
            {
                notifyListeners(currentStatus);
            }
            
            this.lastStatus = currentStatus;
        }
        catch (final Exception e)
        {
            LOGGER.error("Error during memory status check", e);
        }
    }
    
    private boolean shouldNotifyListeners(final MemoryStatus currentStatus)
    {
        if (this.lastStatus == null)
            return true;
            
        // Notify if crossing thresholds
        final double currentRatio = currentStatus.getHeapUsageRatio();
        final double lastRatio = this.lastStatus.getHeapUsageRatio();
        
        // Crossing warning threshold
        if (currentRatio > WARNING_THRESHOLD && lastRatio <= WARNING_THRESHOLD)
            return true;
            
        // Crossing critical threshold
        if (currentRatio > CRITICAL_THRESHOLD && lastRatio <= CRITICAL_THRESHOLD)
            return true;
            
        // Dropping below warning threshold
        if (currentRatio <= WARNING_THRESHOLD && lastRatio > WARNING_THRESHOLD)
            return true;
            
        // Significant change in memory usage (>10%)
        final double changeRatio = Math.abs(currentRatio - lastRatio);
        if (changeRatio > 0.1)
            return true;
            
        return false;
    }
    
    private void notifyListeners(final MemoryStatus status)
    {
        synchronized (this.listeners)
        {
            for (final Consumer<MemoryStatus> listener : this.listeners)
            {
                try
                {
                    listener.accept(status);
                }
                catch (final Exception e)
                {
                    LOGGER.warn("Error notifying memory status listener", e);
                }
            }
        }
    }
    
    /**
     * Memory status information.
     */
    public static class MemoryStatus
    {
        private final long timestamp;
        private final long heapUsed;
        private final long heapMax;
        private final long nonHeapUsed;
        private final long nonHeapMax;
        private final long freeMemory;
        private final long totalMemory;
        private final long maxMemory;
        
        public MemoryStatus(final long timestamp, final long heapUsed, final long heapMax,
                          final long nonHeapUsed, final long nonHeapMax, final long freeMemory,
                          final long totalMemory, final long maxMemory)
        {
            this.timestamp = timestamp;
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.nonHeapUsed = nonHeapUsed;
            this.nonHeapMax = nonHeapMax;
            this.freeMemory = freeMemory;
            this.totalMemory = totalMemory;
            this.maxMemory = maxMemory;
        }
        
        public long getTimestamp() { return this.timestamp; }
        public long getHeapUsed() { return this.heapUsed; }
        public long getHeapMax() { return this.heapMax; }
        public long getNonHeapUsed() { return this.nonHeapUsed; }
        public long getNonHeapMax() { return this.nonHeapMax; }
        public long getFreeMemory() { return this.freeMemory; }
        public long getTotalMemory() { return this.totalMemory; }
        public long getMaxMemory() { return this.maxMemory; }
        
        public double getHeapUsageRatio()
        {
            return this.heapMax > 0 ? (double) this.heapUsed / this.heapMax : 0.0;
        }
        
        public boolean isWarningLevel()
        {
            return getHeapUsageRatio() > WARNING_THRESHOLD;
        }
        
        public boolean isCriticalLevel()
        {
            return getHeapUsageRatio() > CRITICAL_THRESHOLD;
        }
        
        public String getFormattedStatistics()
        {
            return String.format(
                "Memory Status: Heap: %d/%d MB (%.1f%%), Non-Heap: %d/%d MB, Free: %d MB",
                this.heapUsed / (1024 * 1024),
                this.heapMax / (1024 * 1024),
                getHeapUsageRatio() * 100,
                this.nonHeapUsed / (1024 * 1024),
                this.nonHeapMax / (1024 * 1024),
                this.freeMemory / (1024 * 1024)
            );
        }
    }
}
