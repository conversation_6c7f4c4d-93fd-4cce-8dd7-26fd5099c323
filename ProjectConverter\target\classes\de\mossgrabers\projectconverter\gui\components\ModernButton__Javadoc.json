{"doc": " Modern styled button component with animations, accessibility features, and Material Design styling.\n Supports different button styles, loading states, and smooth animations.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": [], "doc": " Initialize the button.\n"}, {"name": "setupStyleClasses", "paramTypes": [], "doc": " Set up style classes.\n"}, {"name": "setupAnimations", "paramTypes": [], "doc": " Set up animations.\n"}, {"name": "setupAccessibility", "paramTypes": [], "doc": " Set up accessibility features.\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings.\n"}, {"name": "primary", "paramTypes": [], "doc": " Set the button as primary style.\n \n @return This button for method chaining\n"}, {"name": "secondary", "paramTypes": [], "doc": " Set the button as secondary style.\n \n @return This button for method chaining\n"}, {"name": "outlined", "paramTypes": [], "doc": " Set the button as outlined style.\n \n @return This button for method chaining\n"}, {"name": "textStyle", "paramTypes": [], "doc": " Set the button as text style.\n \n @return This button for method chaining\n"}, {"name": "floating", "paramTypes": [], "doc": " Set the button as floating action button style.\n \n @return This button for method chaining\n"}, {"name": "small", "paramTypes": [], "doc": " Set the button size to small.\n \n @return This button for method chaining\n"}, {"name": "medium", "paramTypes": [], "doc": " Set the button size to medium.\n \n @return This button for method chaining\n"}, {"name": "large", "paramTypes": [], "doc": " Set the button size to large.\n \n @return This button for method chaining\n"}, {"name": "withTooltip", "paramTypes": ["java.lang.String"], "doc": " Set a tooltip for the button.\n \n @param text The tooltip text\n @return This button for method chaining\n"}, {"name": "withAccessibleDescription", "paramTypes": ["java.lang.String"], "doc": " Set an accessible description for the button.\n \n @param description The accessible description\n @return This button for method chaining\n"}, {"name": "withAnimations", "paramTypes": ["boolean"], "doc": " Enable or disable animations for this button.\n \n @param enabled True to enable animations\n @return This button for method chaining\n"}, {"name": "updateAccessibilityInfo", "paramTypes": [], "doc": " Update accessibility information.\n"}, {"name": "createPrimary", "paramTypes": ["java.lang.String"], "doc": " Create a primary button.\n \n @param text The button text\n @return A primary button\n"}, {"name": "createSecondary", "paramTypes": ["java.lang.String"], "doc": " Create a secondary button.\n \n @param text The button text\n @return A secondary button\n"}, {"name": "createOutlined", "paramTypes": ["java.lang.String"], "doc": " Create an outlined button.\n \n @param text The button text\n @return An outlined button\n"}, {"name": "createText", "paramTypes": ["java.lang.String"], "doc": " Create a text button.\n \n @param text The button text\n @return A text button\n"}, {"name": "createFloating", "paramTypes": ["java.lang.String"], "doc": " Create a floating action button.\n \n @param text The button text\n @return A floating action button\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}, {"name": "<init>", "paramTypes": ["java.lang.String"], "doc": " Constructor with text.\n \n @param text The button text\n"}, {"name": "<init>", "paramTypes": ["java.lang.String", "de.mossgrabers.projectconverter.gui.components.ModernButton.Style"], "doc": " Constructor with text and style.\n \n @param text The button text\n @param style The button style\n"}]}