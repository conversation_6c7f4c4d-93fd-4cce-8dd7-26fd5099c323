{"doc": "\n Loads a dawproject as the source.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getExtensionFilter", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "getEditPane", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "read", "paramTypes": ["java.io.File"], "doc": "{@inheritDoc} Note: Resource is closed in DawProjectContainer. "}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param notifier The notifier\r\n"}]}