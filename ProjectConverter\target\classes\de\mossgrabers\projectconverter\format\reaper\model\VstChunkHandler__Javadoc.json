{"doc": "\n The data of a VST chunk in the Reaper project file.\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "vstID", "doc": "The VST ID. "}, {"name": "dataType", "doc": "The data type (at least I guess so). "}, {"name": "presetName", "doc": "Null terminated preset name. "}], "enumConstants": [], "methods": [{"name": "chunkToFile", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.OutputStream"], "doc": "{@inheritDoc} "}, {"name": "fileToChunk", "paramTypes": ["java.io.InputStream", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "{@inheritDoc} "}, {"name": "readLastLine", "paramTypes": ["byte[]"], "doc": "\n Footer - contains the preset name and some unknown stuff.\r\n\r\n @param bs The bytes to read\r\n @throws IOException Could not read the bytes\r\n"}, {"name": "writeVST2Preset", "paramTypes": ["java.io.OutputStream", "byte[]"], "doc": "\n Write a VST 2 preset file.\r\n\r\n @param out The output stream to write to\r\n @param data The data to store\r\n @throws IOException If an error occurs\r\n"}, {"name": "readVST2Preset", "paramTypes": ["java.io.InputStream"], "doc": "\n Read a VST 2 preset file.\r\n\r\n @param in The output stream to write to\r\n @return The read preset data\r\n @throws IOException If an error occurs\r\n"}, {"name": "writeVST3Preset", "paramTypes": ["java.io.OutputStream", "byte[]"], "doc": "\n Write a VST 3 preset file.\r\n\r\n @param out The output stream to write to\r\n @param data The data to write\r\n @throws IOException If an error occurs\r\n"}, {"name": "readVST3Preset", "paramTypes": ["java.io.InputStream"], "doc": "\n Read a VST 3 preset file.\r\n\r\n @param in The output stream to write to\r\n @return The read preset data\r\n @throws IOException If an error occurs\r\n"}, {"name": "getPresetName", "paramTypes": [], "doc": "\n Get the preset name limited to a maximum of 28 character. If the preset name is not available\r\n try the program name.\r\n\r\n @return The name\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["boolean", "java.lang.String"], "doc": "\n Constructor.\r\n\r\n @param isVST2 True if VST2 otherwise VST3\r\n @param deviceID The device ID, if VST3\r\n"}]}