{"doc": "\n Tags used in Reaper project files.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "isInstrumentPlugin", "paramTypes": ["java.lang.String"], "doc": "\n Is the given tag an instrument plugin tag?\r\n\r\n @param tag The tag to check\r\n @return True if it is an instrument plugin tag\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Constructor.\r\n"}]}