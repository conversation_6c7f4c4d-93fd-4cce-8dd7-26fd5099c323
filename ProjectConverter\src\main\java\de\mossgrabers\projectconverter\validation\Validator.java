package de.mossgrabers.projectconverter.validation;

import java.util.List;

/**
 * Base interface for all validators in the ProjectConverter application.
 * 
 * @param <T> The type of object to validate
 * <AUTHOR> ProjectConverter
 */
public interface Validator<T>
{
    /**
     * Validate an object and return validation results.
     * 
     * @param object The object to validate
     * @return List of validation results (empty if valid)
     */
    List<ValidationResult> validate(T object);
    
    /**
     * Check if an object is valid (convenience method).
     * 
     * @param object The object to validate
     * @return True if valid, false otherwise
     */
    default boolean isValid(final T object)
    {
        return validate(object).isEmpty();
    }
    
    /**
     * Validate an object and throw an exception if invalid.
     * 
     * @param object The object to validate
     * @throws ValidationException If validation fails
     */
    default void validateOrThrow(final T object) throws ValidationException
    {
        final List<ValidationResult> results = validate(object);
        if (!results.isEmpty())
        {
            final ValidationResult firstError = results.stream()
                .filter(r -> r.getSeverity() == ValidationSeverity.ERROR)
                .findFirst()
                .orElse(results.get(0));
            
            throw new ValidationException(
                firstError.getErrorCode(),
                firstError.getMessage(),
                firstError.getFieldName(),
                firstError.getInvalidValue()
            );
        }
    }
    
    /**
     * Get the name of this validator.
     * 
     * @return The validator name
     */
    String getName();
    
    /**
     * Get the description of what this validator checks.
     * 
     * @return The validator description
     */
    String getDescription();
}
