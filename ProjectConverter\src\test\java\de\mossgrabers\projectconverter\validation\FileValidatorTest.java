package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for the FileValidator class.
 * 
 * <AUTHOR> ProjectConverter
 */
class FileValidatorTest
{
    @TempDir
    Path tempDir;
    
    private FileValidator inputValidator;
    private FileValidator outputValidator;
    
    @BeforeEach
    void setUp()
    {
        final Set<String> supportedExtensions = Set.of("rpp", "dawproject", "xml");
        this.inputValidator = new FileValidator(supportedExtensions, true, false);
        this.outputValidator = new FileValidator(null, false, true);
    }
    
    @Test
    void testValidateNullFile()
    {
        // When
        final List<ValidationResult> results = this.inputValidator.validate(null);
        
        // Then
        assertEquals(1, results.size());
        assertEquals(ErrorCode.FILE_NOT_FOUND, results.get(0).getErrorCode());
        assertEquals(ValidationSeverity.ERROR, results.get(0).getSeverity());
    }
    
    @Test
    void testValidateNonExistentFile()
    {
        // Given
        final File nonExistentFile = new File(this.tempDir.toFile(), "nonexistent.rpp");
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(nonExistentFile);
        
        // Then
        assertEquals(1, results.size());
        assertEquals(ErrorCode.FILE_NOT_FOUND, results.get(0).getErrorCode());
        assertTrue(results.get(0).getMessage().contains("does not exist"));
    }
    
    @Test
    void testValidateDirectory()
    {
        // Given
        final File directory = this.tempDir.toFile();
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(directory);
        
        // Then
        assertEquals(1, results.size());
        assertEquals(ErrorCode.INVALID_PROJECT_FORMAT, results.get(0).getErrorCode());
        assertTrue(results.get(0).getMessage().contains("not a file"));
    }
    
    @Test
    void testValidateValidFile() throws IOException
    {
        // Given
        final File validFile = this.tempDir.resolve("test.rpp").toFile();
        Files.write(validFile.toPath(), "test content".getBytes());
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(validFile);
        
        // Then
        assertTrue(results.isEmpty(), "Valid file should have no validation errors");
    }
    
    @Test
    void testValidateEmptyFile() throws IOException
    {
        // Given
        final File emptyFile = this.tempDir.resolve("empty.rpp").toFile();
        Files.write(emptyFile.toPath(), new byte[0]);
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(emptyFile);
        
        // Then
        assertEquals(1, results.size());
        assertEquals(ErrorCode.FILE_CORRUPTED, results.get(0).getErrorCode());
        assertTrue(results.get(0).getMessage().contains("empty"));
    }
    
    @Test
    void testValidateUnsupportedExtension() throws IOException
    {
        // Given
        final File unsupportedFile = this.tempDir.resolve("test.txt").toFile();
        Files.write(unsupportedFile.toPath(), "test content".getBytes());
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(unsupportedFile);
        
        // Then
        assertEquals(1, results.size());
        assertEquals(ErrorCode.UNSUPPORTED_VERSION, results.get(0).getErrorCode());
        assertEquals(ValidationSeverity.WARNING, results.get(0).getSeverity());
        assertTrue(results.get(0).getMessage().contains("may not be supported"));
    }
    
    @Test
    void testValidateLargeFile() throws IOException
    {
        // Given - Create a file larger than 2GB threshold (simulate with smaller size for test)
        final File largeFile = this.tempDir.resolve("large.rpp").toFile();
        final byte[] content = new byte[1024 * 1024]; // 1MB for test
        Files.write(largeFile.toPath(), content);
        
        // Create validator with smaller threshold for testing
        final FileValidator testValidator = new FileValidator(Set.of("rpp"), true, false) {
            // Override for testing with smaller threshold
        };
        
        // When
        final List<ValidationResult> results = testValidator.validate(largeFile);
        
        // Then - Should not have size warning for 1MB file with default threshold
        assertTrue(results.stream().noneMatch(r -> r.getErrorCode() == ErrorCode.FILE_TOO_LARGE));
    }
    
    @Test
    void testValidateReadableFile() throws IOException
    {
        // Given
        final File readableFile = this.tempDir.resolve("readable.rpp").toFile();
        Files.write(readableFile.toPath(), "test content".getBytes());
        
        // When
        final List<ValidationResult> results = this.inputValidator.validate(readableFile);
        
        // Then
        assertTrue(results.isEmpty(), "Readable file should pass validation");
    }
    
    @Test
    void testValidateOutputFile() throws IOException
    {
        // Given
        final File outputFile = this.tempDir.resolve("output.dawproject").toFile();
        
        // When
        final List<ValidationResult> results = this.outputValidator.validate(outputFile);
        
        // Then
        assertTrue(results.isEmpty(), "Non-existent output file should be valid for creation");
    }
    
    @Test
    void testIsValidConvenienceMethod() throws IOException
    {
        // Given
        final File validFile = this.tempDir.resolve("test.rpp").toFile();
        Files.write(validFile.toPath(), "test content".getBytes());
        
        final File invalidFile = new File(this.tempDir.toFile(), "nonexistent.rpp");
        
        // When/Then
        assertTrue(this.inputValidator.isValid(validFile));
        assertFalse(this.inputValidator.isValid(invalidFile));
    }
    
    @Test
    void testValidateOrThrowWithValidFile() throws IOException
    {
        // Given
        final File validFile = this.tempDir.resolve("test.rpp").toFile();
        Files.write(validFile.toPath(), "test content".getBytes());
        
        // When/Then - Should not throw
        assertDoesNotThrow(() -> this.inputValidator.validateOrThrow(validFile));
    }
    
    @Test
    void testValidateOrThrowWithInvalidFile()
    {
        // Given
        final File invalidFile = new File(this.tempDir.toFile(), "nonexistent.rpp");
        
        // When/Then
        final ValidationException exception = assertThrows(ValidationException.class, 
            () -> this.inputValidator.validateOrThrow(invalidFile));
        
        assertEquals(ErrorCode.FILE_NOT_FOUND, exception.getErrorCode());
    }
    
    @Test
    void testForInputFileFactory()
    {
        // Given
        final Set<String> extensions = Set.of("rpp", "dawproject");
        
        // When
        final FileValidator validator = FileValidator.forInputFile(extensions);
        
        // Then
        assertEquals("File Validator", validator.getName());
        assertEquals("Validates file existence, accessibility, size, and basic integrity", 
                    validator.getDescription());
    }
    
    @Test
    void testForOutputFileFactory()
    {
        // When
        final FileValidator validator = FileValidator.forOutputFile();
        
        // Then
        assertEquals("File Validator", validator.getName());
    }
    
    @Test
    void testValidationResultProperties()
    {
        // Given
        final ValidationResult errorResult = ValidationResult.error(
            ErrorCode.FILE_NOT_FOUND, "Test error", "testField", "testValue");
        
        final ValidationResult warningResult = ValidationResult.warning(
            ErrorCode.UNSUPPORTED_VERSION, "Test warning", "testField", "testValue", "Test suggestion");
        
        final ValidationResult infoResult = ValidationResult.info(
            ErrorCode.UNSUPPORTED_FEATURE, "Test info", "Test suggestion");
        
        // Then
        assertTrue(errorResult.isError());
        assertFalse(errorResult.isWarning());
        assertEquals("testField", errorResult.getFieldName());
        assertEquals("testValue", errorResult.getInvalidValue());
        
        assertFalse(warningResult.isError());
        assertTrue(warningResult.isWarning());
        assertEquals("Test suggestion", warningResult.getSuggestion());
        
        assertFalse(infoResult.isError());
        assertFalse(infoResult.isWarning());
        assertNull(infoResult.getFieldName());
    }
    
    @Test
    void testValidationResultToString()
    {
        // Given
        final ValidationResult result = ValidationResult.warning(
            ErrorCode.UNSUPPORTED_VERSION, "Test message", "testField", "testValue", "Test suggestion");
        
        // When
        final String toString = result.toString();
        
        // Then
        assertTrue(toString.contains("WARNING"));
        assertTrue(toString.contains("Test message"));
        assertTrue(toString.contains("testField"));
        assertTrue(toString.contains("Test suggestion"));
    }
}
