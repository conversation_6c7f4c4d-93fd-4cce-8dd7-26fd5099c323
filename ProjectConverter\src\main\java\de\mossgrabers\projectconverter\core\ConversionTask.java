// Written by <PERSON><PERSON><PERSON> - mossgra<PERSON>.de
// (c) 2021-2024
// Licensed under LGPLv3 - http://www.gnu.org/licenses/lgpl-3.0.txt

package de.mossgrabers.projectconverter.core;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Set;

import com.bitwig.dawproject.DawProject;

import de.mossgrabers.projectconverter.INotifier;
import de.mossgrabers.projectconverter.services.ErrorHandler;
import de.mossgrabers.projectconverter.services.MemoryMonitor;
import de.mossgrabers.projectconverter.validation.FileValidator;
import de.mossgrabers.projectconverter.validation.ProjectValidator;
import de.mossgrabers.projectconverter.validation.ValidationResult;
import de.mossgrabers.projectconverter.validation.ValidationSeverity;
import javafx.concurrent.Task;


/**
 * The task to run the actual conversion process with enhanced error handling and validation.
 *
 * <AUTHOR>
 * <AUTHOR> ProjectConverter
 */
public class ConversionTask extends Task<Void>
{
    private final File               sourceFile;
    private final ISourceFormat      sourceFormat;
    private final IDestinationFormat destinationFormat;
    private final INotifier          notifier;
    private final File               outputPath;

    // Enhanced components
    private final ErrorHandler       errorHandler;
    private final MemoryMonitor      memoryMonitor;
    private final FileValidator      inputFileValidator;
    private final FileValidator      outputFileValidator;
    private final ProjectValidator   projectValidator;


    /**
     * Constructor with enhanced error handling and validation.
     *
     * @param sourceFile The source file to convert
     * @param outputPath The output path in which to write the destination file
     * @param sourceFormat The format of the source file
     * @param destinationFormat The destination format
     * @param notifier Where to log to
     */
    public ConversionTask (final File sourceFile, final File outputPath, final ISourceFormat sourceFormat, final IDestinationFormat destinationFormat, final INotifier notifier)
    {
        this.sourceFile = sourceFile;
        this.outputPath = outputPath;
        this.sourceFormat = sourceFormat;
        this.destinationFormat = destinationFormat;
        this.notifier = notifier;

        // Initialize enhanced components
        this.errorHandler = new ErrorHandler(notifier);
        this.memoryMonitor = new MemoryMonitor();

        // Setup file validators
        final Set<String> supportedExtensions = Set.of("rpp", "dawproject", "xml");
        this.inputFileValidator = FileValidator.forInputFile(supportedExtensions);
        this.outputFileValidator = FileValidator.forOutputFile();
        this.projectValidator = ProjectValidator.createDefault();

        // Start memory monitoring
        this.memoryMonitor.startMonitoring();
    }


    /** {@inheritDoc} */
    @Override
    protected Void call ()
    {
        try
        {
            // Enhanced pre-conversion validation
            if (!performPreConversionValidation())
            {
                return null;
            }

            // Check memory status before starting
            if (this.memoryMonitor.isMemoryUsageHigh())
            {
                this.notifier.log("High memory usage detected. Consider closing other applications.");
            }

            // Parse the project file
            this.notifier.log ("IDS_NOTIFY_PARSING_FILE", this.sourceFile.getAbsolutePath ());

            try (final DawProjectContainer dawProject = this.sourceFormat.read (this.sourceFile);)
            {
                if (this.waitForDelivery ())
                    return null;

                // Enhanced project validation
                if (!performProjectValidation(dawProject))
                {
                    return null;
                }

                if (this.waitForDelivery ())
                    return null;

                // Enhanced output validation
                if (!performOutputValidation())
                {
                    return null;
                }

                // Write output file(s)
                this.notifier.log ("IDS_NOTIFY_WRITING_FILE", this.outputPath.getAbsolutePath ());

                try
                {
                    this.destinationFormat.write (dawProject, this.outputPath);
                }
                catch (final IOException ex)
                {
                    final ConversionException conversionEx = new ConversionException(
                        ErrorCode.CONVERSION_FAILED,
                        "Failed to write output file",
                        this.sourceFormat.getName(),
                        this.destinationFormat.getName(),
                        "writing output",
                        false,
                        ex
                    );
                    this.errorHandler.handleException(conversionEx);
                    return null;
                }
            }
            catch (final IOException | ParseException ex)
            {
                final ConversionException conversionEx = new ConversionException(
                    ErrorCode.CONVERSION_FAILED,
                    "Failed to read source file",
                    this.sourceFormat.getName(),
                    this.destinationFormat.getName(),
                    "reading source",
                    false,
                    ex
                );
                this.errorHandler.handleException(conversionEx);
                return null;
            }
            catch (final Exception ex)
            {
                this.errorHandler.handleThrowable(ex, "during conversion process");
                return null;
            }

            final boolean cancelled = this.isCancelled ();
            this.notifier.log (cancelled ? "IDS_NOTIFY_CANCELED" : "IDS_NOTIFY_CONVERSION_FINISHED");
            return null;
        }
        finally
        {
            // Cleanup resources
            cleanup();
        }
    }


    /**
     * Perform pre-conversion validation of input files and settings.
     *
     * @return True if validation passed, false otherwise
     */
    private boolean performPreConversionValidation()
    {
        try
        {
            this.notifier.log("Validating input file...");

            // Validate source file
            final List<ValidationResult> sourceResults = this.inputFileValidator.validate(this.sourceFile);
            if (hasValidationErrors(sourceResults))
            {
                logValidationResults("Source file", sourceResults);
                return false;
            }

            // Validate output directory
            if (!this.outputPath.exists())
            {
                if (!this.outputPath.mkdirs())
                {
                    this.errorHandler.handleThrowable(
                        new IOException("Cannot create output directory: " + this.outputPath),
                        "validating output directory"
                    );
                    return false;
                }
            }

            logValidationResults("Source file", sourceResults);
            return true;
        }
        catch (final Exception ex)
        {
            this.errorHandler.handleThrowable(ex, "during pre-conversion validation");
            return false;
        }
    }

    /**
     * Perform project validation using the enhanced project validator.
     *
     * @param dawProject The project to validate
     * @return True if validation passed, false otherwise
     */
    private boolean performProjectValidation(final DawProjectContainer dawProject)
    {
        try
        {
            this.notifier.log ("IDS_NOTIFY_VALIDATING_FILE");

            // Use built-in DAWproject validation
            try
            {
                DawProject.validate (dawProject.getProject ());
            }
            catch (final IOException ex)
            {
                this.notifier.logError ("IDS_NOTIFY_COULD_NOT_VALIDATE_PROJECT", ex);
                // Continue with enhanced validation even if built-in validation fails
            }

            // Enhanced project validation
            final List<ValidationResult> projectResults = this.projectValidator.validate(dawProject.getProject());
            logValidationResults("Project structure", projectResults);

            // Only fail on errors, not warnings
            return !hasValidationErrors(projectResults);
        }
        catch (final Exception ex)
        {
            this.errorHandler.handleThrowable(ex, "during project validation");
            return false;
        }
    }

    /**
     * Perform output validation.
     *
     * @return True if validation passed, false otherwise
     */
    private boolean performOutputValidation()
    {
        try
        {
            // Check available disk space
            final long freeSpace = this.outputPath.getFreeSpace();
            final long requiredSpace = this.sourceFile.length() * 2; // Estimate 2x source size

            if (freeSpace < requiredSpace)
            {
                this.errorHandler.handleException(new ProjectConverterException(
                    ErrorCode.INSUFFICIENT_DISK_SPACE,
                    "Insufficient disk space for conversion",
                    String.format("Available: %d MB, Required: %d MB",
                                freeSpace / (1024 * 1024), requiredSpace / (1024 * 1024)),
                    false
                ));
                return false;
            }

            return true;
        }
        catch (final Exception ex)
        {
            this.errorHandler.handleThrowable(ex, "during output validation");
            return false;
        }
    }

    /**
     * Check if validation results contain errors.
     *
     * @param results The validation results
     * @return True if there are errors
     */
    private boolean hasValidationErrors(final List<ValidationResult> results)
    {
        return results.stream().anyMatch(ValidationResult::isError);
    }

    /**
     * Log validation results to the notifier.
     *
     * @param context The validation context
     * @param results The validation results
     */
    private void logValidationResults(final String context, final List<ValidationResult> results)
    {
        if (results.isEmpty())
        {
            this.notifier.log(context + " validation passed");
            return;
        }

        for (final ValidationResult result : results)
        {
            if (result.getSeverity() == ValidationSeverity.ERROR)
            {
                this.notifier.logError(context + " error: " + result.getMessage());
            }
            else if (result.getSeverity() == ValidationSeverity.WARNING)
            {
                this.notifier.log(context + " warning: " + result.getMessage());
            }
            else
            {
                this.notifier.log(context + " info: " + result.getMessage());
            }
        }
    }

    /**
     * Cleanup resources used during conversion.
     */
    private void cleanup()
    {
        try
        {
            if (this.memoryMonitor != null)
            {
                this.memoryMonitor.stopMonitoring();
                this.memoryMonitor.shutdown();
            }
        }
        catch (final Exception ex)
        {
            // Log cleanup errors but don't fail the conversion
            this.notifier.log("Warning: Error during cleanup: " + ex.getMessage());
        }
    }

    /**
     * Wait a bit.
     *
     * @return The thread was cancelled if true
     */
    protected boolean waitForDelivery ()
    {
        try
        {
            Thread.sleep (10);
        }
        catch (final InterruptedException ex)
        {
            if (this.isCancelled ())
                return true;
            Thread.currentThread ().interrupt ();
        }
        return false;
    }
}
