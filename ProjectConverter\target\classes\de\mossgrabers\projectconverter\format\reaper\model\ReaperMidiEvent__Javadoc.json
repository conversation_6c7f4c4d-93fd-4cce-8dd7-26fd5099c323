{"doc": "\n A MIDI event in a clip.\r\n\r\n The start of a note: \"E 0 90 24 2d\". The end of a note: \"E 480 80 24 00\"\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "toNode", "paramTypes": [], "doc": "\n Convert the values back to a note node.\r\n\r\n @return The node\r\n"}, {"name": "isMidiEvent", "paramTypes": [], "doc": "\n Is it a MIDI event?\r\n\r\n @return True if yes\r\n"}, {"name": "getPosition", "paramTypes": [], "doc": "\n Get the start of the event.\r\n\r\n @return The start\r\n"}, {"name": "setPosition", "paramTypes": ["long"], "doc": "\n Set the start of the event.\r\n\r\n @param position The start\r\n"}, {"name": "getOffset", "paramTypes": [], "doc": "\n The offset of the event in PPQ (ticks per quarters) to the previous MIDI event. The maximum\r\n number of ticks per quarter is defined for the source of this event (normally 960).\r\n\r\n @return The offset\r\n"}, {"name": "setOffset", "paramTypes": ["long"], "doc": "\n Set the offset of the event to the previous MIDI event.\r\n\r\n @param offset The offset in PPQ (ticks per quarters)\r\n"}, {"name": "getCode", "paramTypes": [], "doc": "\n Get the MIDI event code.\r\n\r\n @return The code\r\n"}, {"name": "getChannel", "paramTypes": [], "doc": "\n Get the channel of the MIDI event.\r\n\r\n @return The channel\r\n"}, {"name": "getData1", "paramTypes": [], "doc": "\n Get the first value byte of the MIDI event.\r\n\r\n @return The byte\r\n"}, {"name": "getData2", "paramTypes": [], "doc": "\n Get the second value byte of the MIDI event.\r\n\r\n @return The byte\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node"], "doc": "\n Constructor.\r\n\r\n @param noteNode The node of the note\r\n @throws ParseException Could not parse the node\r\n"}, {"name": "<init>", "paramTypes": ["long", "int", "int", "int", "int"], "doc": "\n Constructor.\r\n\r\n @param position The position of the MIDI event\r\n @param channel The MIDI channel of the event\r\n @param code The status code of the event (without the channel)\r\n @param data1 The first data byte of the event\r\n @param data2 The second data byte of the event\r\n"}]}