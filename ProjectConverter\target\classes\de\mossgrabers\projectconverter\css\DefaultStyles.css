.hbox, .vbox {
	-fx-padding: 15.0;
	-fx-spacing: 10.0;
}

.grid {
	-fx-background-insets: 0.0 0.0 0.0 0.0;
	-fx-padding: 15.0 15.0 15.0 15.0;
	-fx-hgap: 10.0;
	-fx-vgap: 10.0;
}

.tab-pane .tab-content-area {
	-fx-border-color: -fx-box-border;
}

.tab-pane .tab-header-area .tab-header-background {
    -fx-background-color: transparent;
}

.horizontal-tab-pane *.tab {
	/* Determines the tab height */
	-fx-pref-width: 30px; 
	/* Everything else is just aesthetics */
	-fx-padding: 12px;
	-fx-background-insets: 2 -1 -1 -1;
	-fx-border-width: 1 0 1 1;
	-fx-border-color: rgb(55, 55, 56) black black black;
}

/** Remove the focus border which interlapped the text. */
.tab-pane > .tab-header-area > .headers-region > .tab:selected .focus-indicator {
    -fx-border-color: transparent;
}

.tab-pane > .tab-header-area > .headers-region > .tab {
    -fx-background-insets: 0 0 0 0, 0 1 0 0, 1 2 0 1;
}

.horizontal-tab-pane *.tab:selected {
	-fx-border-width: 4px 0px 1px 1px;
	-fx-border-color: rgb(200, 0, 0) black rgb(45, 45, 46) black;
}

.horizontal-tab-pane:focused {
	-fx-border-color: -fx-focus-color;
}

.titled-pane>*.content {
	-fx-background-insets: 0.0;
	-fx-padding: 0.0;
}

.padding {
    -fx-padding: 15.0;
}

.paddingLeft {
    -fx-padding: 0.0 0.0 0.0 15.0;
}

.paddingLeftTop {
    -fx-padding: 15.0 0.0 0.0 15.0;
}

.paddingTop {
    -fx-padding: 15.0 0.0 0.0 0.0;
}

.paddingRight {
    -fx-padding: 0.0 15.0 0.0 0.0;
}

.paddingLeftBottomRight {
    -fx-padding: 0.0 15.0 15.0 15.0;
}

.no-padding {
	-fx-padding: 0.0;
}

.tile-pane {
    -fx-hgap: 15.0;
    -fx-vgap: 15.0;
}

.titled-separator {
    -fx-font-weight: bold;
    -fx-padding: 0.0 10.0 0.0 0.0;
}

.titled-separator-pane {
    -fx-padding: 20.0 0.0 0.0 0.0;
}

.flat-button {
    -fx-background-color: transparent;
    -fx-padding: 0.0;
}

/*********************
 * Log View
**********************/

.log-view {
	-fx-font-family: 'monospace';
	-fx-font-size: 1.5em;	
}

.log-view .list-cell:selected {
	-fx-background-color: -fx-focus-color;
}

.log-view .list-cell:info {
}

.log-view .list-cell:error {
    -fx-text-fill: red;
}