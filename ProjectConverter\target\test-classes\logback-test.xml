<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender for test output -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- File appender for test logs -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>target/test-logs/projectconverter-test.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Logger for ProjectConverter classes -->
    <logger name="de.mossgrabers.projectconverter" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>
    
    <!-- Logger for test classes -->
    <logger name="de.mossgrabers.projectconverter.testing" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>
    
    <!-- Reduce noise from external libraries during testing -->
    <logger name="org.testfx" level="WARN"/>
    <logger name="javafx" level="WARN"/>
    <logger name="com.sun.javafx" level="WARN"/>
    
    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
