{"doc": "\n Access to additional Reaper media files. Audio and plug-in states.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "stream", "paramTypes": ["java.lang.String"], "doc": "{@inheritDoc} "}, {"name": "add", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "getAll", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "close", "paramTypes": [], "doc": "{@inheritDoc} "}], "constructors": []}