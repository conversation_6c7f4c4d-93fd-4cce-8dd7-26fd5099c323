package de.mossgrabers.projectconverter.gui.models;

import de.mossgrabers.projectconverter.core.IDestinationFormat;
import de.mossgrabers.projectconverter.core.ISourceFormat;
import de.mossgrabers.projectconverter.services.ErrorHandler;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ListProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleListProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Main view model for the ProjectConverter application.
 * Manages the overall application state, file selection, and conversion settings.
 * 
 * <AUTHOR> ProjectConverter
 */
public class MainViewModel extends BaseViewModel
{
    // File selection properties
    private final ObjectProperty<File> sourceFileProperty = new SimpleObjectProperty<>();
    private final ObjectProperty<File> outputPathProperty = new SimpleObjectProperty<>();
    private final StringProperty sourcePathTextProperty = new SimpleStringProperty("");
    private final StringProperty destinationPathTextProperty = new SimpleStringProperty("");
    
    // Format selection properties
    private final ListProperty<ISourceFormat> sourceFormatsProperty = new SimpleListProperty<>(FXCollections.observableArrayList());
    private final ListProperty<IDestinationFormat> destinationFormatsProperty = new SimpleListProperty<>(FXCollections.observableArrayList());
    private final IntegerProperty selectedSourceFormatIndexProperty = new SimpleIntegerProperty(-1);
    private final IntegerProperty selectedDestinationFormatIndexProperty = new SimpleIntegerProperty(-1);
    
    // UI state properties
    private final BooleanProperty darkModeEnabledProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty conversionInProgressProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty canConvertProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty canCancelProperty = new SimpleBooleanProperty(false);
    
    // History properties
    private final ListProperty<String> sourcePathHistoryProperty = new SimpleListProperty<>(FXCollections.observableArrayList());
    private final ListProperty<String> destinationPathHistoryProperty = new SimpleListProperty<>(FXCollections.observableArrayList());
    
    // Services
    private ErrorHandler errorHandler;
    
    /**
     * Constructor.
     */
    public MainViewModel()
    {
        super();
        setupPropertyBindings();
    }
    
    /**
     * Initialize the view model with required services.
     * 
     * @param errorHandler The error handler service
     */
    public void initializeServices(final ErrorHandler errorHandler)
    {
        this.errorHandler = errorHandler;
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        // Update can convert property when relevant properties change
        this.canConvertProperty.bind(
            this.sourceFileProperty.isNotNull()
                .and(this.outputPathProperty.isNotNull())
                .and(this.selectedSourceFormatIndexProperty.greaterThanOrEqualTo(0))
                .and(this.selectedDestinationFormatIndexProperty.greaterThanOrEqualTo(0))
                .and(this.conversionInProgressProperty.not())
        );
        
        // Update can cancel property
        this.canCancelProperty.bind(this.conversionInProgressProperty);
        
        // Update source path text when source file changes
        this.sourceFileProperty.addListener((obs, oldFile, newFile) -> {
            if (newFile != null)
            {
                this.sourcePathTextProperty.set(newFile.getAbsolutePath());
                updateSourcePathHistory(newFile.getAbsolutePath());
            }
            else
            {
                this.sourcePathTextProperty.set("");
            }
        });
        
        // Update destination path text when output path changes
        this.outputPathProperty.addListener((obs, oldPath, newPath) -> {
            if (newPath != null)
            {
                this.destinationPathTextProperty.set(newPath.getAbsolutePath());
                updateDestinationPathHistory(newPath.getAbsolutePath());
            }
            else
            {
                this.destinationPathTextProperty.set("");
            }
        });
    }
    
    // Source file properties
    public ObjectProperty<File> sourceFileProperty() { return this.sourceFileProperty; }
    public File getSourceFile() { return this.sourceFileProperty.get(); }
    public void setSourceFile(final File file) { this.sourceFileProperty.set(file); }
    
    // Output path properties
    public ObjectProperty<File> outputPathProperty() { return this.outputPathProperty; }
    public File getOutputPath() { return this.outputPathProperty.get(); }
    public void setOutputPath(final File path) { this.outputPathProperty.set(path); }
    
    // Path text properties
    public StringProperty sourcePathTextProperty() { return this.sourcePathTextProperty; }
    public String getSourcePathText() { return this.sourcePathTextProperty.get(); }
    public void setSourcePathText(final String text) { this.sourcePathTextProperty.set(text); }
    
    public StringProperty destinationPathTextProperty() { return this.destinationPathTextProperty; }
    public String getDestinationPathText() { return this.destinationPathTextProperty.get(); }
    public void setDestinationPathText(final String text) { this.destinationPathTextProperty.set(text); }
    
    // Format properties
    public ListProperty<ISourceFormat> sourceFormatsProperty() { return this.sourceFormatsProperty; }
    public ObservableList<ISourceFormat> getSourceFormats() { return this.sourceFormatsProperty.get(); }
    public void setSourceFormats(final List<ISourceFormat> formats) { this.sourceFormatsProperty.setAll(formats); }
    
    public ListProperty<IDestinationFormat> destinationFormatsProperty() { return this.destinationFormatsProperty; }
    public ObservableList<IDestinationFormat> getDestinationFormats() { return this.destinationFormatsProperty.get(); }
    public void setDestinationFormats(final List<IDestinationFormat> formats) { this.destinationFormatsProperty.setAll(formats); }
    
    // Format selection properties
    public IntegerProperty selectedSourceFormatIndexProperty() { return this.selectedSourceFormatIndexProperty; }
    public int getSelectedSourceFormatIndex() { return this.selectedSourceFormatIndexProperty.get(); }
    public void setSelectedSourceFormatIndex(final int index) { this.selectedSourceFormatIndexProperty.set(index); }
    
    public IntegerProperty selectedDestinationFormatIndexProperty() { return this.selectedDestinationFormatIndexProperty; }
    public int getSelectedDestinationFormatIndex() { return this.selectedDestinationFormatIndexProperty.get(); }
    public void setSelectedDestinationFormatIndex(final int index) { this.selectedDestinationFormatIndexProperty.set(index); }
    
    // UI state properties
    public BooleanProperty darkModeEnabledProperty() { return this.darkModeEnabledProperty; }
    public boolean isDarkModeEnabled() { return this.darkModeEnabledProperty.get(); }
    public void setDarkModeEnabled(final boolean enabled) { this.darkModeEnabledProperty.set(enabled); }
    
    public BooleanProperty conversionInProgressProperty() { return this.conversionInProgressProperty; }
    public boolean isConversionInProgress() { return this.conversionInProgressProperty.get(); }
    public void setConversionInProgress(final boolean inProgress) { this.conversionInProgressProperty.set(inProgress); }
    
    public BooleanProperty canConvertProperty() { return this.canConvertProperty; }
    public boolean canConvert() { return this.canConvertProperty.get(); }
    
    public BooleanProperty canCancelProperty() { return this.canCancelProperty; }
    public boolean canCancel() { return this.canCancelProperty.get(); }
    
    // History properties
    public ListProperty<String> sourcePathHistoryProperty() { return this.sourcePathHistoryProperty; }
    public ObservableList<String> getSourcePathHistory() { return this.sourcePathHistoryProperty.get(); }
    
    public ListProperty<String> destinationPathHistoryProperty() { return this.destinationPathHistoryProperty; }
    public ObservableList<String> getDestinationPathHistory() { return this.destinationPathHistoryProperty.get(); }
    
    /**
     * Get the currently selected source format.
     * 
     * @return The selected source format, or null if none selected
     */
    public ISourceFormat getSelectedSourceFormat()
    {
        final int index = getSelectedSourceFormatIndex();
        if (index >= 0 && index < getSourceFormats().size())
        {
            return getSourceFormats().get(index);
        }
        return null;
    }
    
    /**
     * Get the currently selected destination format.
     * 
     * @return The selected destination format, or null if none selected
     */
    public IDestinationFormat getSelectedDestinationFormat()
    {
        final int index = getSelectedDestinationFormatIndex();
        if (index >= 0 && index < getDestinationFormats().size())
        {
            return getDestinationFormats().get(index);
        }
        return null;
    }
    
    /**
     * Update the source path history.
     * 
     * @param path The path to add to history
     */
    private void updateSourcePathHistory(final String path)
    {
        final ObservableList<String> history = getSourcePathHistory();
        history.remove(path); // Remove if already exists
        history.add(0, path); // Add to beginning
        
        // Limit history size
        while (history.size() > 20)
        {
            history.remove(history.size() - 1);
        }
    }
    
    /**
     * Update the destination path history.
     * 
     * @param path The path to add to history
     */
    private void updateDestinationPathHistory(final String path)
    {
        final ObservableList<String> history = getDestinationPathHistory();
        history.remove(path); // Remove if already exists
        history.add(0, path); // Add to beginning
        
        // Limit history size
        while (history.size() > 20)
        {
            history.remove(history.size() - 1);
        }
    }
    
    @Override
    public boolean isValid()
    {
        return getSourceFile() != null && 
               getOutputPath() != null && 
               getSelectedSourceFormat() != null && 
               getSelectedDestinationFormat() != null;
    }
    
    @Override
    public List<String> getValidationErrors()
    {
        final List<String> errors = new ArrayList<>();
        
        if (getSourceFile() == null)
        {
            errors.add("Source file must be selected");
        }
        else if (!getSourceFile().exists())
        {
            errors.add("Source file does not exist");
        }
        
        if (getOutputPath() == null)
        {
            errors.add("Output path must be selected");
        }
        
        if (getSelectedSourceFormat() == null)
        {
            errors.add("Source format must be selected");
        }
        
        if (getSelectedDestinationFormat() == null)
        {
            errors.add("Destination format must be selected");
        }
        
        return errors;
    }
    
    @Override
    public void reset()
    {
        super.reset();
        setSourceFile(null);
        setOutputPath(null);
        setSelectedSourceFormatIndex(-1);
        setSelectedDestinationFormatIndex(-1);
        setConversionInProgress(false);
    }
}
