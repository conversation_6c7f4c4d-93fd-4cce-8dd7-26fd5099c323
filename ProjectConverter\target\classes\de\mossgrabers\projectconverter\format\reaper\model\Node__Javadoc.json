{"doc": "\n A node in a Reaper project.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "setName", "paramTypes": ["java.lang.String"], "doc": "\n Set the name of the node.\r\n\r\n @param name The name\r\n"}, {"name": "getName", "paramTypes": [], "doc": "\n Get the name of the node.\r\n\r\n @return The name\r\n"}, {"name": "addParameters", "paramTypes": ["java.util.List"], "doc": "\n Add parameters to the node.\r\n\r\n @param parameters The parameters to add\r\n"}, {"name": "getParameters", "paramTypes": [], "doc": "\n Get the parameters of the node.\r\n\r\n @return The parameters\r\n"}, {"name": "setLine", "paramTypes": ["java.lang.String"], "doc": "\n Set the raw text line of the node.\r\n\r\n @param line THe text\r\n"}, {"name": "getLine", "paramTypes": [], "doc": "\n Get the text line.\r\n\r\n @return The text\r\n"}], "constructors": []}