package de.mossgrabers.projectconverter.gui.themes;

import javafx.application.Platform;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ObservableList;
import javafx.scene.Scene;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Theme management service for dynamic theme switching and CSS management.
 * Supports light/dark themes, high contrast mode, and custom styling.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ThemeManager
{
    private static final Logger LOGGER = LoggerFactory.getLogger(ThemeManager.class);
    
    private static ThemeManager instance;
    
    // Available themes
    private final Map<String, Theme> availableThemes = new HashMap<>();
    private final ObjectProperty<Theme> currentThemeProperty = new SimpleObjectProperty<>();
    
    // Registered scenes for theme application
    private final List<Scene> registeredScenes = new ArrayList<>();
    private final List<Consumer<Theme>> themeChangeListeners = new ArrayList<>();
    
    // Theme configuration
    private boolean highContrastMode = false;
    private double fontSizeMultiplier = 1.0;
    
    /**
     * Private constructor for singleton pattern.
     */
    private ThemeManager()
    {
        initializeDefaultThemes();
        setupPropertyBindings();
    }
    
    /**
     * Get the singleton instance.
     * 
     * @return The theme manager instance
     */
    public static synchronized ThemeManager getInstance()
    {
        if (instance == null)
        {
            instance = new ThemeManager();
        }
        return instance;
    }
    
    /**
     * Initialize default themes.
     */
    private void initializeDefaultThemes()
    {
        // Light theme
        final Theme lightTheme = new Theme(
            "Light",
            "Light theme with modern styling",
            "/de/mossgrabers/projectconverter/css/light-theme.css",
            false
        );
        this.availableThemes.put("Light", lightTheme);
        
        // Dark theme
        final Theme darkTheme = new Theme(
            "Dark",
            "Dark theme for reduced eye strain",
            "/de/mossgrabers/projectconverter/css/dark-theme.css",
            true
        );
        this.availableThemes.put("Dark", darkTheme);
        
        // High contrast light theme
        final Theme highContrastLightTheme = new Theme(
            "High Contrast Light",
            "High contrast light theme for accessibility",
            "/de/mossgrabers/projectconverter/css/high-contrast-light-theme.css",
            false
        );
        this.availableThemes.put("High Contrast Light", highContrastLightTheme);
        
        // High contrast dark theme
        final Theme highContrastDarkTheme = new Theme(
            "High Contrast Dark",
            "High contrast dark theme for accessibility",
            "/de/mossgrabers/projectconverter/css/high-contrast-dark-theme.css",
            true
        );
        this.availableThemes.put("High Contrast Dark", highContrastDarkTheme);
        
        // Set default theme
        setCurrentTheme("Light");
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        this.currentThemeProperty.addListener((obs, oldTheme, newTheme) -> {
            if (newTheme != null)
            {
                applyThemeToAllScenes(newTheme);
                notifyThemeChangeListeners(newTheme);
            }
        });
    }
    
    /**
     * Get the current theme property.
     * 
     * @return The current theme property
     */
    public ObjectProperty<Theme> currentThemeProperty()
    {
        return this.currentThemeProperty;
    }
    
    /**
     * Get the current theme.
     * 
     * @return The current theme
     */
    public Theme getCurrentTheme()
    {
        return this.currentThemeProperty.get();
    }
    
    /**
     * Set the current theme by name.
     * 
     * @param themeName The name of the theme to set
     * @return True if the theme was set successfully
     */
    public boolean setCurrentTheme(final String themeName)
    {
        final Theme theme = this.availableThemes.get(themeName);
        if (theme != null)
        {
            this.currentThemeProperty.set(theme);
            LOGGER.info("Theme changed to: {}", themeName);
            return true;
        }
        else
        {
            LOGGER.warn("Theme not found: {}", themeName);
            return false;
        }
    }
    
    /**
     * Set the current theme.
     * 
     * @param theme The theme to set
     */
    public void setCurrentTheme(final Theme theme)
    {
        if (theme != null)
        {
            this.currentThemeProperty.set(theme);
        }
    }
    
    /**
     * Get all available themes.
     * 
     * @return Map of theme names to themes
     */
    public Map<String, Theme> getAvailableThemes()
    {
        return new HashMap<>(this.availableThemes);
    }
    
    /**
     * Get theme names.
     * 
     * @return List of theme names
     */
    public List<String> getThemeNames()
    {
        return new ArrayList<>(this.availableThemes.keySet());
    }
    
    /**
     * Register a scene for automatic theme application.
     * 
     * @param scene The scene to register
     */
    public void registerScene(final Scene scene)
    {
        if (scene != null && !this.registeredScenes.contains(scene))
        {
            this.registeredScenes.add(scene);
            
            // Apply current theme to the new scene
            final Theme currentTheme = getCurrentTheme();
            if (currentTheme != null)
            {
                applyThemeToScene(scene, currentTheme);
            }
            
            LOGGER.debug("Scene registered for theme management");
        }
    }
    
    /**
     * Unregister a scene from theme management.
     * 
     * @param scene The scene to unregister
     */
    public void unregisterScene(final Scene scene)
    {
        this.registeredScenes.remove(scene);
        LOGGER.debug("Scene unregistered from theme management");
    }
    
    /**
     * Add a theme change listener.
     * 
     * @param listener The listener to add
     */
    public void addThemeChangeListener(final Consumer<Theme> listener)
    {
        this.themeChangeListeners.add(listener);
    }
    
    /**
     * Remove a theme change listener.
     * 
     * @param listener The listener to remove
     */
    public void removeThemeChangeListener(final Consumer<Theme> listener)
    {
        this.themeChangeListeners.remove(listener);
    }
    
    /**
     * Enable or disable high contrast mode.
     * 
     * @param enabled True to enable high contrast mode
     */
    public void setHighContrastMode(final boolean enabled)
    {
        this.highContrastMode = enabled;
        
        // Switch to appropriate high contrast theme
        final Theme currentTheme = getCurrentTheme();
        if (currentTheme != null)
        {
            final String targetThemeName;
            if (enabled)
            {
                targetThemeName = currentTheme.isDark() ? "High Contrast Dark" : "High Contrast Light";
            }
            else
            {
                targetThemeName = currentTheme.isDark() ? "Dark" : "Light";
            }
            
            setCurrentTheme(targetThemeName);
        }
    }
    
    /**
     * Check if high contrast mode is enabled.
     * 
     * @return True if high contrast mode is enabled
     */
    public boolean isHighContrastMode()
    {
        return this.highContrastMode;
    }
    
    /**
     * Set the font size multiplier.
     * 
     * @param multiplier The font size multiplier (1.0 = normal)
     */
    public void setFontSizeMultiplier(final double multiplier)
    {
        this.fontSizeMultiplier = Math.max(0.5, Math.min(3.0, multiplier));
        
        // Reapply current theme to update font sizes
        final Theme currentTheme = getCurrentTheme();
        if (currentTheme != null)
        {
            applyThemeToAllScenes(currentTheme);
        }
    }
    
    /**
     * Get the font size multiplier.
     * 
     * @return The font size multiplier
     */
    public double getFontSizeMultiplier()
    {
        return this.fontSizeMultiplier;
    }
    
    /**
     * Apply a theme to all registered scenes.
     * 
     * @param theme The theme to apply
     */
    private void applyThemeToAllScenes(final Theme theme)
    {
        Platform.runLater(() -> {
            for (final Scene scene : this.registeredScenes)
            {
                applyThemeToScene(scene, theme);
            }
        });
    }
    
    /**
     * Apply a theme to a specific scene.
     * 
     * @param scene The scene to apply the theme to
     * @param theme The theme to apply
     */
    private void applyThemeToScene(final Scene scene, final Theme theme)
    {
        if (scene == null || theme == null)
        {
            return;
        }
        
        try
        {
            final ObservableList<String> stylesheets = scene.getStylesheets();
            
            // Remove existing theme stylesheets
            stylesheets.removeIf(stylesheet -> 
                stylesheet.contains("theme.css") || 
                stylesheet.contains("dark") || 
                stylesheet.contains("light") ||
                stylesheet.contains("contrast"));
            
            // Add base theme stylesheet
            final URL themeUrl = getClass().getResource(theme.getStylesheetPath());
            if (themeUrl != null)
            {
                stylesheets.add(themeUrl.toExternalForm());
            }
            else
            {
                LOGGER.warn("Theme stylesheet not found: {}", theme.getStylesheetPath());
            }
            
            // Add font size adjustments if needed
            if (this.fontSizeMultiplier != 1.0)
            {
                final String fontSizeStyle = String.format(
                    "data:text/css,.root { -fx-font-size: %.1fem; }", 
                    this.fontSizeMultiplier
                );
                stylesheets.add(fontSizeStyle);
            }
            
            LOGGER.debug("Applied theme '{}' to scene", theme.getName());
        }
        catch (final Exception e)
        {
            LOGGER.error("Error applying theme to scene", e);
        }
    }
    
    /**
     * Notify all theme change listeners.
     * 
     * @param theme The new theme
     */
    private void notifyThemeChangeListeners(final Theme theme)
    {
        for (final Consumer<Theme> listener : this.themeChangeListeners)
        {
            try
            {
                listener.accept(theme);
            }
            catch (final Exception e)
            {
                LOGGER.warn("Error in theme change listener", e);
            }
        }
    }
    
    /**
     * Check if a theme resource exists.
     * 
     * @param resourcePath The path to the theme resource
     * @return True if the resource exists
     */
    private boolean themeResourceExists(final String resourcePath)
    {
        try (final InputStream stream = getClass().getResourceAsStream(resourcePath))
        {
            return stream != null;
        }
        catch (final Exception e)
        {
            return false;
        }
    }
    
    /**
     * Add a custom theme.
     * 
     * @param theme The theme to add
     */
    public void addCustomTheme(final Theme theme)
    {
        if (theme != null && !this.availableThemes.containsKey(theme.getName()))
        {
            this.availableThemes.put(theme.getName(), theme);
            LOGGER.info("Custom theme added: {}", theme.getName());
        }
    }
    
    /**
     * Remove a custom theme.
     * 
     * @param themeName The name of the theme to remove
     * @return True if the theme was removed
     */
    public boolean removeCustomTheme(final String themeName)
    {
        if (this.availableThemes.containsKey(themeName))
        {
            // Don't allow removal of default themes
            if (!"Light".equals(themeName) && !"Dark".equals(themeName) && 
                !"High Contrast Light".equals(themeName) && !"High Contrast Dark".equals(themeName))
            {
                this.availableThemes.remove(themeName);
                LOGGER.info("Custom theme removed: {}", themeName);
                return true;
            }
        }
        return false;
    }
}
