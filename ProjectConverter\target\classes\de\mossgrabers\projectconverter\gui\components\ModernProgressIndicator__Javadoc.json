{"doc": " Modern progress indicator component with multiple styles and smooth animations.\n Supports linear, circular, and indeterminate progress indicators with accessibility features.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": [], "doc": " Initialize the progress indicator.\n"}, {"name": "setupComponents", "paramTypes": [], "doc": " Set up UI components.\n"}, {"name": "setupAnimations", "paramTypes": [], "doc": " Set up animations.\n"}, {"name": "setupAccessibility", "paramTypes": [], "doc": " Set up accessibility features.\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings.\n"}, {"name": "setupStyleClasses", "paramTypes": [], "doc": " Set up style classes.\n"}, {"name": "updateProgress", "paramTypes": ["double"], "doc": " Update progress with optional animation.\n \n @param newProgress The new progress value\n"}, {"name": "updateIndeterminateState", "paramTypes": ["boolean"], "doc": " Update indeterminate state.\n \n @param indeterminate True if indeterminate\n"}, {"name": "updateComponentsForStyle", "paramTypes": [], "doc": " Update components based on current style.\n"}, {"name": "updateProgressText", "paramTypes": ["java.lang.String"], "doc": " Update progress text.\n \n @param text The new text\n"}, {"name": "updateAccessibilityInfo", "paramTypes": [], "doc": " Update accessibility information.\n"}, {"name": "updateAccessibilityAnnouncement", "paramTypes": [], "doc": " Update accessibility announcement for progress changes.\n"}, {"name": "animateToProgress", "paramTypes": ["double", "javafx.util.Duration"], "doc": " Set progress with animation.\n \n @param progress The target progress\n @param duration The animation duration\n"}, {"name": "createLinear", "paramTypes": [], "doc": " Create a linear progress indicator.\n \n @return A linear progress indicator\n"}, {"name": "createCircular", "paramTypes": [], "doc": " Create a circular progress indicator.\n \n @return A circular progress indicator\n"}, {"name": "createCircularWithText", "paramTypes": [], "doc": " Create a circular progress indicator with text.\n \n @return A circular progress indicator with text\n"}, {"name": "createMinimal", "paramTypes": [], "doc": " Create a minimal progress indicator.\n \n @return A minimal progress indicator\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ModernProgressIndicator.Style"], "doc": " Constructor with style.\n \n @param style The progress indicator style\n"}]}