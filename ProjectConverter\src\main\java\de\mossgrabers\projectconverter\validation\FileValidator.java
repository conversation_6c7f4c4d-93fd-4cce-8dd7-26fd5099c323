package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Validator for file system operations and file integrity.
 * 
 * <AUTHOR> ProjectConverter
 */
public class FileValidator implements Validator<File>
{
    private static final long MAX_FILE_SIZE = 2L * 1024 * 1024 * 1024; // 2GB
    private static final long MIN_FREE_SPACE = 100L * 1024 * 1024; // 100MB
    
    private final Set<String> supportedExtensions;
    private final boolean checkReadability;
    private final boolean checkWritability;
    
    /**
     * Constructor.
     * 
     * @param supportedExtensions Set of supported file extensions (without dot)
     * @param checkReadability Whether to check if file is readable
     * @param checkWritability Whether to check if file is writable
     */
    public FileValidator(final Set<String> supportedExtensions, 
                        final boolean checkReadability, final boolean checkWritability)
    {
        this.supportedExtensions = supportedExtensions;
        this.checkReadability = checkReadability;
        this.checkWritability = checkWritability;
    }
    
    @Override
    public List<ValidationResult> validate(final File file)
    {
        final List<ValidationResult> results = new ArrayList<>();
        
        if (file == null)
        {
            results.add(ValidationResult.error(ErrorCode.FILE_NOT_FOUND, 
                "File is null", "file", null));
            return results;
        }
        
        // Check if file exists
        if (!file.exists())
        {
            results.add(ValidationResult.error(ErrorCode.FILE_NOT_FOUND, 
                "File does not exist: " + file.getAbsolutePath(), "file", file));
            return results; // No point checking further if file doesn't exist
        }
        
        // Check if it's actually a file (not a directory)
        if (!file.isFile())
        {
            results.add(ValidationResult.error(ErrorCode.INVALID_PROJECT_FORMAT, 
                "Path is not a file: " + file.getAbsolutePath(), "file", file));
            return results;
        }
        
        // Check file extension
        if (this.supportedExtensions != null && !this.supportedExtensions.isEmpty())
        {
            final String extension = getFileExtension(file);
            if (!this.supportedExtensions.contains(extension.toLowerCase()))
            {
                results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                    "File extension '" + extension + "' may not be supported", 
                    "extension", extension,
                    "Supported extensions: " + this.supportedExtensions));
            }
        }
        
        // Check file size
        final long fileSize = file.length();
        if (fileSize == 0)
        {
            results.add(ValidationResult.error(ErrorCode.FILE_CORRUPTED, 
                "File is empty", "size", fileSize));
        }
        else if (fileSize > MAX_FILE_SIZE)
        {
            results.add(ValidationResult.warning(ErrorCode.FILE_TOO_LARGE, 
                "File is very large (" + FileUtils.byteCountToDisplaySize(fileSize) + 
                ") and may cause performance issues", "size", fileSize,
                "Consider using a smaller file or increasing available memory"));
        }
        
        // Check readability
        if (this.checkReadability && !file.canRead())
        {
            results.add(ValidationResult.error(ErrorCode.FILE_ACCESS_DENIED, 
                "File is not readable", "readable", false));
        }
        
        // Check writability (for output files)
        if (this.checkWritability)
        {
            if (file.exists() && !file.canWrite())
            {
                results.add(ValidationResult.error(ErrorCode.FILE_ACCESS_DENIED, 
                    "File is not writable", "writable", false));
            }
            
            // Check parent directory writability
            final File parentDir = file.getParentFile();
            if (parentDir != null && parentDir.exists() && !parentDir.canWrite())
            {
                results.add(ValidationResult.error(ErrorCode.FILE_ACCESS_DENIED, 
                    "Parent directory is not writable", "parentWritable", false));
            }
        }
        
        // Check available disk space
        try
        {
            final long freeSpace = file.getFreeSpace();
            if (freeSpace < MIN_FREE_SPACE)
            {
                results.add(ValidationResult.warning(ErrorCode.INSUFFICIENT_DISK_SPACE, 
                    "Low disk space: " + FileUtils.byteCountToDisplaySize(freeSpace), 
                    "freeSpace", freeSpace,
                    "Free up disk space before proceeding"));
            }
        }
        catch (final SecurityException e)
        {
            results.add(ValidationResult.warning(ErrorCode.FILE_ACCESS_DENIED, 
                "Cannot check disk space due to security restrictions", 
                "diskSpace", null, null));
        }
        
        // Check if file is potentially corrupted (basic check)
        if (this.checkReadability)
        {
            try
            {
                final Path path = file.toPath();
                if (!Files.isReadable(path))
                {
                    results.add(ValidationResult.error(ErrorCode.FILE_CORRUPTED, 
                        "File appears to be corrupted or inaccessible", "corrupted", true));
                }
            }
            catch (final Exception e)
            {
                results.add(ValidationResult.warning(ErrorCode.FILE_CORRUPTED, 
                    "Cannot verify file integrity: " + e.getMessage(), 
                    "integrity", null, "Try opening the file in its native application"));
            }
        }
        
        return results;
    }
    
    @Override
    public String getName()
    {
        return "File Validator";
    }
    
    @Override
    public String getDescription()
    {
        return "Validates file existence, accessibility, size, and basic integrity";
    }
    
    /**
     * Get the file extension without the dot.
     * 
     * @param file The file
     * @return The extension (empty string if no extension)
     */
    private String getFileExtension(final File file)
    {
        final String name = file.getName();
        final int lastDot = name.lastIndexOf('.');
        return lastDot > 0 ? name.substring(lastDot + 1) : "";
    }
    
    /**
     * Create a validator for input files.
     * 
     * @param supportedExtensions Supported file extensions
     * @return File validator for input files
     */
    public static FileValidator forInputFile(final Set<String> supportedExtensions)
    {
        return new FileValidator(supportedExtensions, true, false);
    }
    
    /**
     * Create a validator for output files.
     * 
     * @return File validator for output files
     */
    public static FileValidator forOutputFile()
    {
        return new FileValidator(null, false, true);
    }
}
