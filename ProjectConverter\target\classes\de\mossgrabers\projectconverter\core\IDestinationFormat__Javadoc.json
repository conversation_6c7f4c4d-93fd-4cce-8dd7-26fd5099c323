{"doc": "\n The interface to a project destination.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "write", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.io.File"], "doc": "\n Write the destination project file(s).\r\n\r\n @param dawProject The dawproject to store\r\n @param outputPath The path in which to store the output file(s)\r\n @throws IOException Could not write the file(s)\r\n"}, {"name": "needsOverwrite", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "\n Check if the file(s) already exist and therefore need an overwrite confirmation.\r\n\r\n @param projectName The name of the project\r\n @param outputPath The path in which to store the output file(s)\r\n @return True if files exist\r\n"}], "constructors": []}