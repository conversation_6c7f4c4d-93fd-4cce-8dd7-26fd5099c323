{"doc": " Validator for plugin compatibility and availability.\n Checks plugin formats, IDs, and potential compatibility issues.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "createDefault", "paramTypes": [], "doc": " Create a default plugin validator.\n \n @return Default plugin validator\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.util.Set", "java.util.Map", "boolean"], "doc": " Constructor.\n \n @param supportedFormats Set of supported plugin formats\n @param knownPlugins Map of known plugin information\n @param checkAvailability Whether to check plugin availability\n"}]}