{"doc": " Result of a validation operation.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "error", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " Create an error validation result.\n \n @param errorCode The error code\n @param message The error message\n @param fieldName The field name\n @param invalidValue The invalid value\n @return The validation result\n"}, {"name": "warning", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.Object", "java.lang.String"], "doc": " Create a warning validation result.\n \n @param errorCode The error code\n @param message The warning message\n @param fieldName The field name\n @param invalidValue The invalid value\n @param suggestion Suggestion for improvement\n @return The validation result\n"}, {"name": "info", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String"], "doc": " Create an info validation result.\n \n @param errorCode The error code\n @param message The info message\n @param suggestion Suggestion for improvement\n @return The validation result\n"}, {"name": "getSeverity", "paramTypes": [], "doc": " Get the severity.\n \n @return The severity\n"}, {"name": "getErrorCode", "paramTypes": [], "doc": " Get the error code.\n \n @return The error code\n"}, {"name": "getMessage", "paramTypes": [], "doc": " Get the message.\n \n @return The message\n"}, {"name": "getFieldName", "paramTypes": [], "doc": " Get the field name.\n \n @return The field name\n"}, {"name": "getInvalidValue", "paramTypes": [], "doc": " Get the invalid value.\n \n @return The invalid value\n"}, {"name": "getSuggestion", "paramTypes": [], "doc": " Get the suggestion.\n \n @return The suggestion\n"}, {"name": "isError", "paramTypes": [], "doc": " Check if this is an error result.\n \n @return True if error\n"}, {"name": "isWarning", "paramTypes": [], "doc": " Check if this is a warning result.\n \n @return True if warning\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.validation.ValidationSeverity", "de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.Object", "java.lang.String"], "doc": " Constructor for validation result.\n \n @param severity The severity of the validation issue\n @param errorCode The error code\n @param message The validation message\n @param fieldName The field that failed validation\n @param invalidValue The invalid value\n @param suggestion Suggestion for fixing the issue\n"}]}