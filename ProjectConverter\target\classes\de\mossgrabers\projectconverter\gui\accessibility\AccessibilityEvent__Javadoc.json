{"doc": " Event class for accessibility-related events in the application.\n Used to notify listeners about focus changes, screen reader announcements, etc.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getType", "paramTypes": [], "doc": " Get the event type.\n \n @return The event type\n"}, {"name": "getSource", "paramTypes": [], "doc": " Get the source node.\n \n @return The source node\n"}, {"name": "getMessage", "paramTypes": [], "doc": " Get the event message.\n \n @return The event message\n"}, {"name": "getData", "paramTypes": [], "doc": " Get additional event data.\n \n @return The event data\n"}, {"name": "getTimestamp", "paramTypes": [], "doc": " Get the event timestamp.\n \n @return The timestamp\n"}, {"name": "hasMessage", "paramTypes": [], "doc": " Check if this event has a message.\n \n @return True if the event has a message\n"}, {"name": "hasData", "paramTypes": [], "doc": " Check if this event has additional data.\n \n @return True if the event has data\n"}, {"name": "getDataAs", "paramTypes": ["java.lang.Class"], "doc": " Get the data as a specific type.\n \n @param <T> The expected data type\n @param clazz The class of the expected type\n @return The data cast to the specified type, or null if not compatible\n"}, {"name": "focusChanged", "paramTypes": ["javafx.scene.Node"], "doc": " Create a focus changed event.\n \n @param newFocusNode The newly focused node\n @return A focus changed event\n"}, {"name": "screenReaderAnnouncement", "paramTypes": ["java.lang.String"], "doc": " Create a screen reader announcement event.\n \n @param announcement The announcement text\n @return A screen reader announcement event\n"}, {"name": "highContrastChanged", "paramTypes": ["boolean"], "doc": " Create a high contrast mode changed event.\n \n @param enabled Whether high contrast mode is enabled\n @return A high contrast changed event\n"}, {"name": "fontSizeChanged", "paramTypes": ["double"], "doc": " Create a font size changed event.\n \n @param multiplier The new font size multiplier\n @return A font size changed event\n"}, {"name": "keyboardNavigationChanged", "paramTypes": ["boolean"], "doc": " Create a keyboard navigation changed event.\n \n @param enhanced Whether enhanced keyboard navigation is enabled\n @return A keyboard navigation changed event\n"}, {"name": "featureToggled", "paramTypes": ["java.lang.String", "boolean"], "doc": " Create an accessibility feature toggled event.\n \n @param featureName The name of the feature\n @param enabled Whether the feature is enabled\n @return An accessibility feature toggled event\n"}, {"name": "getNodeDescription", "paramTypes": ["javafx.scene.Node"], "doc": " Get a description of a node for accessibility purposes.\n \n @param node The node\n @return A description of the node\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.accessibility.AccessibilityEvent.Type", "javafx.scene.Node"], "doc": " Constructor.\n \n @param type The event type\n @param source The source node (can be null)\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.accessibility.AccessibilityEvent.Type", "javafx.scene.Node", "java.lang.String"], "doc": " Constructor with message.\n \n @param type The event type\n @param source The source node (can be null)\n @param message The event message\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.accessibility.AccessibilityEvent.Type", "javafx.scene.Node", "java.lang.String", "java.lang.Object"], "doc": " Constructor with message and data.\n \n @param type The event type\n @param source The source node (can be null)\n @param message The event message\n @param data Additional event data\n"}]}