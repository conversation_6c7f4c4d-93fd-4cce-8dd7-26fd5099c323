/* High Contrast Dark Theme for ProjectConverter */
/* Accessibility-focused high contrast dark styling */

:root {
    /* High contrast dark color palette */
    --primary-color: #00ffff;
    --primary-variant: #00cccc;
    --secondary-color: #00ff00;
    --secondary-variant: #00cc00;
    
    /* Surface colors */
    --background: #000000;
    --surface: #000000;
    --surface-variant: #1a1a1a;
    --outline: #ffffff;
    
    /* Text colors */
    --on-background: #ffffff;
    --on-surface: #ffffff;
    --on-surface-variant: #ffffff;
    --on-primary: #000000;
    --on-secondary: #000000;
    
    /* State colors */
    --error: #ff0000;
    --warning: #ffff00;
    --success: #00ff00;
    --info: #00ffff;
    
    /* Interactive states */
    --hover: #333333;
    --pressed: #555555;
    --focus: #ffff00;
    --selected: #00ffff;
    --disabled: #808080;
    
    /* Shadows - minimal for high contrast */
    --shadow-1: none;
    --shadow-2: none;
    --shadow-3: none;
    
    /* Border radius - reduced for clarity */
    --border-radius-small: 2px;
    --border-radius-medium: 4px;
    --border-radius-large: 6px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Typography - larger for accessibility */
    --font-family: -fx-font-family;
    --font-size-small: 1em;
    --font-size-medium: 1.125em;
    --font-size-large: 1.25em;
    --font-size-xlarge: 1.5em;
}

/* Root styling */
.root {
    -fx-base: var(--background);
    -fx-background: var(--background);
    -fx-control-inner-background: var(--surface);
    -fx-accent: var(--primary-color);
    -fx-default-button: var(--primary-color);
    -fx-focus-color: var(--focus);
    -fx-faint-focus-color: var(--focus);
    -fx-text-fill: var(--on-background);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

/* Buttons */
.button {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-medium);
    -fx-background-radius: var(--border-radius-medium);
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-md) var(--spacing-lg);
    -fx-font-weight: bold;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: var(--hover);
    -fx-border-width: 3px;
}

.button:pressed {
    -fx-background-color: var(--pressed);
}

.button:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 3px;
}

.button:disabled {
    -fx-background-color: var(--disabled);
    -fx-text-fill: var(--surface);
    -fx-cursor: default;
}

/* Primary button */
.button.primary, .button:default {
    -fx-background-color: var(--primary-color);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--primary-color);
}

.button.primary:hover, .button:default:hover {
    -fx-background-color: var(--primary-variant);
    -fx-border-color: var(--primary-variant);
}

/* Text fields and combo boxes */
.text-field, .combo-box {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-sm);
    -fx-font-weight: bold;
}

.text-field:focused, .combo-box:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 3px;
}

/* Tab pane */
.tab-pane {
    -fx-background-color: var(--background);
}

.tab-pane .tab-header-area {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 0 0 2px 0;
}

.tab-pane .tab {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-md) var(--spacing-lg);
    -fx-font-weight: bold;
}

.tab-pane .tab:selected {
    -fx-background-color: var(--selected);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--selected);
    -fx-border-width: 3px;
}

.tab-pane .tab:hover {
    -fx-background-color: var(--hover);
    -fx-border-width: 3px;
}

/* Progress bar */
.progress-bar {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.progress-bar .bar {
    -fx-background-color: var(--primary-color);
    -fx-background-radius: var(--border-radius-small);
}

/* Check box */
.check-box {
    -fx-text-fill: var(--on-surface);
    -fx-font-weight: bold;
}

.check-box .box {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 3px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.check-box:selected .box {
    -fx-background-color: var(--primary-color);
    -fx-border-color: var(--primary-color);
}

.check-box:selected .mark {
    -fx-background-color: var(--on-primary);
}

.check-box:focused .box {
    -fx-background-color: var(--focus);
    -fx-border-color: var(--on-primary);
}

/* Scroll pane */
.scroll-pane {
    -fx-background-color: var(--background);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
}

.scroll-bar {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 1px;
}

.scroll-bar .thumb {
    -fx-background-color: var(--outline);
    -fx-border-color: var(--surface);
    -fx-border-width: 1px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: var(--primary-color);
}

/* Text area */
.text-area {
    -fx-background-color: var(--surface);
    -fx-text-fill: var(--on-surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
    -fx-font-weight: bold;
}

.text-area:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 3px;
}

/* Tooltips */
.tooltip {
    -fx-background-color: var(--on-surface);
    -fx-text-fill: var(--surface);
    -fx-border-color: var(--surface);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-small);
    -fx-background-radius: var(--border-radius-small);
    -fx-padding: var(--spacing-sm) var(--spacing-md);
    -fx-font-size: var(--font-size-medium);
    -fx-font-weight: bold;
}

/* Menu and context menu */
.menu-bar {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 0 0 2px 0;
}

.menu-item {
    -fx-background-color: var(--surface);
    -fx-text-fill: var(--on-surface);
    -fx-padding: var(--spacing-sm) var(--spacing-md);
    -fx-font-weight: bold;
    -fx-border-color: transparent;
    -fx-border-width: 1px;
}

.menu-item:hover {
    -fx-background-color: var(--hover);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
}

.menu-item:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 2px;
}

/* Separator */
.separator {
    -fx-background-color: var(--outline);
    -fx-min-height: 2px;
}

/* Labels */
.label {
    -fx-text-fill: var(--on-surface);
    -fx-font-weight: bold;
}

.label.title {
    -fx-font-size: var(--font-size-large);
    -fx-font-weight: bold;
}

.label.subtitle {
    -fx-text-fill: var(--on-surface);
    -fx-font-size: var(--font-size-medium);
    -fx-font-weight: bold;
}

/* Error states */
.error {
    -fx-text-fill: var(--error);
    -fx-border-color: var(--error);
    -fx-border-width: 3px;
    -fx-font-weight: bold;
}

.warning {
    -fx-text-fill: var(--warning);
    -fx-border-color: var(--warning);
    -fx-border-width: 3px;
    -fx-font-weight: bold;
}

.success {
    -fx-text-fill: var(--success);
    -fx-border-color: var(--success);
    -fx-border-width: 3px;
    -fx-font-weight: bold;
}

.info {
    -fx-text-fill: var(--info);
    -fx-border-color: var(--info);
    -fx-border-width: 3px;
    -fx-font-weight: bold;
}

/* Custom classes */
.card {
    -fx-background-color: var(--surface);
    -fx-border-color: var(--outline);
    -fx-border-width: 2px;
    -fx-border-radius: var(--border-radius-medium);
    -fx-background-radius: var(--border-radius-medium);
    -fx-padding: var(--spacing-lg);
}

/* Focus indicators for accessibility - enhanced */
.button:focused,
.text-field:focused,
.combo-box:focused,
.check-box:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 3px;
}

/* Ensure all interactive elements have strong focus indicators */
*:focused {
    -fx-background-color: var(--focus);
    -fx-text-fill: var(--on-primary);
    -fx-border-color: var(--on-primary);
    -fx-border-width: 3px;
}
