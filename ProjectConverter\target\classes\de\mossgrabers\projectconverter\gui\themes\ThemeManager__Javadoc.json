{"doc": " Theme management service for dynamic theme switching and CSS management.\n Supports light/dark themes, high contrast mode, and custom styling.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getInstance", "paramTypes": [], "doc": " Get the singleton instance.\n \n @return The theme manager instance\n"}, {"name": "initializeDefaultThemes", "paramTypes": [], "doc": " Initialize default themes.\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "currentThemeProperty", "paramTypes": [], "doc": " Get the current theme property.\n \n @return The current theme property\n"}, {"name": "getCurrentTheme", "paramTypes": [], "doc": " Get the current theme.\n \n @return The current theme\n"}, {"name": "setCurrentTheme", "paramTypes": ["java.lang.String"], "doc": " Set the current theme by name.\n \n @param themeName The name of the theme to set\n @return True if the theme was set successfully\n"}, {"name": "setCurrentTheme", "paramTypes": ["de.mossgrabers.projectconverter.gui.themes.Theme"], "doc": " Set the current theme.\n \n @param theme The theme to set\n"}, {"name": "getAvailableThemes", "paramTypes": [], "doc": " Get all available themes.\n \n @return Map of theme names to themes\n"}, {"name": "getThemeNames", "paramTypes": [], "doc": " Get theme names.\n \n @return List of theme names\n"}, {"name": "registerScene", "paramTypes": ["javafx.scene.Scene"], "doc": " Register a scene for automatic theme application.\n \n @param scene The scene to register\n"}, {"name": "unregisterScene", "paramTypes": ["javafx.scene.Scene"], "doc": " Unregister a scene from theme management.\n \n @param scene The scene to unregister\n"}, {"name": "addThemeChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add a theme change listener.\n \n @param listener The listener to add\n"}, {"name": "removeThemeChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove a theme change listener.\n \n @param listener The listener to remove\n"}, {"name": "setHighContrastMode", "paramTypes": ["boolean"], "doc": " Enable or disable high contrast mode.\n \n @param enabled True to enable high contrast mode\n"}, {"name": "isHighContrastMode", "paramTypes": [], "doc": " Check if high contrast mode is enabled.\n \n @return True if high contrast mode is enabled\n"}, {"name": "setFontSizeMultiplier", "paramTypes": ["double"], "doc": " Set the font size multiplier.\n \n @param multiplier The font size multiplier (1.0 = normal)\n"}, {"name": "getFontSizeMultiplier", "paramTypes": [], "doc": " Get the font size multiplier.\n \n @return The font size multiplier\n"}, {"name": "applyThemeToAllScenes", "paramTypes": ["de.mossgrabers.projectconverter.gui.themes.Theme"], "doc": " Apply a theme to all registered scenes.\n \n @param theme The theme to apply\n"}, {"name": "applyThemeToScene", "paramTypes": ["javafx.scene.Scene", "de.mossgrabers.projectconverter.gui.themes.Theme"], "doc": " Apply a theme to a specific scene.\n \n @param scene The scene to apply the theme to\n @param theme The theme to apply\n"}, {"name": "notifyThemeChangeListeners", "paramTypes": ["de.mossgrabers.projectconverter.gui.themes.Theme"], "doc": " Notify all theme change listeners.\n \n @param theme The new theme\n"}, {"name": "themeResourceExists", "paramTypes": ["java.lang.String"], "doc": " Check if a theme resource exists.\n \n @param resourcePath The path to the theme resource\n @return True if the resource exists\n"}, {"name": "addCustomTheme", "paramTypes": ["de.mossgrabers.projectconverter.gui.themes.Theme"], "doc": " Add a custom theme.\n \n @param theme The theme to add\n"}, {"name": "removeCustomTheme", "paramTypes": ["java.lang.String"], "doc": " Remove a custom theme.\n \n @param themeName The name of the theme to remove\n @return True if the theme was removed\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Private constructor for singleton pattern.\n"}]}