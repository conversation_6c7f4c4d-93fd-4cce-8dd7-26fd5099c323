package de.mossgrabers.projectconverter.gui.components;

import javafx.animation.ScaleTransition;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.effect.DropShadow;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.util.Duration;

/**
 * Modern card component with Material Design styling, hover effects, and flexible content layout.
 * Supports different elevation levels, interactive states, and responsive design.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ModernCard extends VBox
{
    /**
     * Card elevation levels.
     */
    public enum Elevation
    {
        FLAT(0),
        LOW(2),
        MEDIUM(4),
        HIGH(8),
        VERY_HIGH(16);
        
        private final int level;
        
        Elevation(final int level)
        {
            this.level = level;
        }
        
        public int getLevel()
        {
            return this.level;
        }
        
        public String getStyleClass()
        {
            return "elevation-" + this.level;
        }
    }
    
    /**
     * Card styles.
     */
    public enum Style
    {
        DEFAULT("default"),
        OUTLINED("outlined"),
        FILLED("filled"),
        ELEVATED("elevated");
        
        private final String styleClass;
        
        Style(final String styleClass)
        {
            this.styleClass = styleClass;
        }
        
        public String getStyleClass()
        {
            return this.styleClass;
        }
    }
    
    // Properties
    private final StringProperty titleProperty = new SimpleStringProperty();
    private final StringProperty subtitleProperty = new SimpleStringProperty();
    private final ObjectProperty<Elevation> elevationProperty = new SimpleObjectProperty<>(Elevation.LOW);
    private final ObjectProperty<Style> styleProperty = new SimpleObjectProperty<>(Style.DEFAULT);
    private final BooleanProperty interactiveProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty animationsEnabledProperty = new SimpleBooleanProperty(true);
    
    // UI Components
    private Label titleLabel;
    private Label subtitleLabel;
    private VBox headerContainer;
    private VBox contentContainer;
    private HBox actionContainer;
    
    // Animations
    private ScaleTransition hoverAnimation;
    
    /**
     * Constructor.
     */
    public ModernCard()
    {
        initialize();
    }
    
    /**
     * Constructor with title.
     * 
     * @param title The card title
     */
    public ModernCard(final String title)
    {
        setTitle(title);
        initialize();
    }
    
    /**
     * Constructor with title and content.
     * 
     * @param title The card title
     * @param content The card content
     */
    public ModernCard(final String title, final Node content)
    {
        setTitle(title);
        setContent(content);
        initialize();
    }
    
    /**
     * Initialize the card.
     */
    private void initialize()
    {
        setupComponents();
        setupAnimations();
        setupPropertyBindings();
        setupStyleClasses();
    }
    
    /**
     * Set up UI components.
     */
    private void setupComponents()
    {
        // Title label
        this.titleLabel = new Label();
        this.titleLabel.getStyleClass().add("card-title");
        
        // Subtitle label
        this.subtitleLabel = new Label();
        this.subtitleLabel.getStyleClass().add("card-subtitle");
        
        // Header container
        this.headerContainer = new VBox(4);
        this.headerContainer.getStyleClass().add("card-header");
        
        // Content container
        this.contentContainer = new VBox(8);
        this.contentContainer.getStyleClass().add("card-content");
        VBox.setVgrow(this.contentContainer, Priority.ALWAYS);
        
        // Action container
        this.actionContainer = new HBox(8);
        this.actionContainer.getStyleClass().add("card-actions");
        this.actionContainer.setAlignment(Pos.CENTER_RIGHT);
        
        // Add components to card
        getChildren().addAll(this.headerContainer, this.contentContainer, this.actionContainer);
        
        // Set default spacing and padding
        setSpacing(12);
        setPadding(new Insets(16));
    }
    
    /**
     * Set up animations.
     */
    private void setupAnimations()
    {
        // Hover animation
        this.hoverAnimation = new ScaleTransition(Duration.millis(200), this);
        this.hoverAnimation.setFromX(1.0);
        this.hoverAnimation.setFromY(1.0);
        this.hoverAnimation.setToX(1.02);
        this.hoverAnimation.setToY(1.02);
        
        // Set up hover effects
        setOnMouseEntered(e -> {
            if (isInteractive() && isAnimationsEnabled())
            {
                this.hoverAnimation.setRate(1.0);
                this.hoverAnimation.play();
                updateElevation(Elevation.MEDIUM);
            }
        });
        
        setOnMouseExited(e -> {
            if (isInteractive() && isAnimationsEnabled())
            {
                this.hoverAnimation.setRate(-1.0);
                this.hoverAnimation.play();
                updateElevation(getElevation());
            }
        });
    }
    
    /**
     * Set up property bindings.
     */
    private void setupPropertyBindings()
    {
        // Title property binding
        this.titleProperty.addListener((obs, oldTitle, newTitle) -> {
            updateTitle(newTitle);
        });
        
        // Subtitle property binding
        this.subtitleProperty.addListener((obs, oldSubtitle, newSubtitle) -> {
            updateSubtitle(newSubtitle);
        });
        
        // Elevation property binding
        this.elevationProperty.addListener((obs, oldElevation, newElevation) -> {
            updateElevation(newElevation);
        });
        
        // Style property binding
        this.styleProperty.addListener((obs, oldStyle, newStyle) -> {
            updateStyle(oldStyle, newStyle);
        });
        
        // Interactive property binding
        this.interactiveProperty.addListener((obs, oldInteractive, newInteractive) -> {
            updateInteractiveState(newInteractive);
        });
    }
    
    /**
     * Set up style classes.
     */
    private void setupStyleClasses()
    {
        getStyleClass().addAll("modern-card", "card-" + getCardStyle().getStyleClass(), 
                              getElevation().getStyleClass());
    }
    
    // Property getters and setters
    public StringProperty titleProperty() { return this.titleProperty; }
    public String getTitle() { return this.titleProperty.get(); }
    public void setTitle(final String title) { this.titleProperty.set(title); }
    
    public StringProperty subtitleProperty() { return this.subtitleProperty; }
    public String getSubtitle() { return this.subtitleProperty.get(); }
    public void setSubtitle(final String subtitle) { this.subtitleProperty.set(subtitle); }
    
    public ObjectProperty<Elevation> elevationProperty() { return this.elevationProperty; }
    public Elevation getElevation() { return this.elevationProperty.get(); }
    public void setElevation(final Elevation elevation) { this.elevationProperty.set(elevation); }
    
    public ObjectProperty<Style> styleProperty() { return this.styleProperty; }
    public Style getCardStyle() { return this.styleProperty.get(); }
    public void setCardStyle(final Style style) { this.styleProperty.set(style); }
    
    public BooleanProperty interactiveProperty() { return this.interactiveProperty; }
    public boolean isInteractive() { return this.interactiveProperty.get(); }
    public void setInteractive(final boolean interactive) { this.interactiveProperty.set(interactive); }
    
    public BooleanProperty animationsEnabledProperty() { return this.animationsEnabledProperty; }
    public boolean isAnimationsEnabled() { return this.animationsEnabledProperty.get(); }
    public void setAnimationsEnabled(final boolean enabled) { this.animationsEnabledProperty.set(enabled); }
    
    /**
     * Set the card content.
     * 
     * @param content The content node
     */
    public void setContent(final Node content)
    {
        this.contentContainer.getChildren().clear();
        if (content != null)
        {
            this.contentContainer.getChildren().add(content);
        }
    }
    
    /**
     * Add content to the card.
     * 
     * @param content The content node to add
     */
    public void addContent(final Node content)
    {
        if (content != null)
        {
            this.contentContainer.getChildren().add(content);
        }
    }
    
    /**
     * Add an action button to the card.
     * 
     * @param action The action node (typically a button)
     */
    public void addAction(final Node action)
    {
        if (action != null)
        {
            this.actionContainer.getChildren().add(action);
        }
    }
    
    /**
     * Clear all actions from the card.
     */
    public void clearActions()
    {
        this.actionContainer.getChildren().clear();
    }
    
    /**
     * Get the content container for direct manipulation.
     * 
     * @return The content container
     */
    public VBox getContentContainer()
    {
        return this.contentContainer;
    }
    
    /**
     * Get the action container for direct manipulation.
     * 
     * @return The action container
     */
    public HBox getActionContainer()
    {
        return this.actionContainer;
    }
    
    /**
     * Update the title display.
     * 
     * @param title The new title
     */
    private void updateTitle(final String title)
    {
        if (title != null && !title.trim().isEmpty())
        {
            this.titleLabel.setText(title);
            if (!this.headerContainer.getChildren().contains(this.titleLabel))
            {
                this.headerContainer.getChildren().add(0, this.titleLabel);
            }
        }
        else
        {
            this.headerContainer.getChildren().remove(this.titleLabel);
        }
    }
    
    /**
     * Update the subtitle display.
     * 
     * @param subtitle The new subtitle
     */
    private void updateSubtitle(final String subtitle)
    {
        if (subtitle != null && !subtitle.trim().isEmpty())
        {
            this.subtitleLabel.setText(subtitle);
            if (!this.headerContainer.getChildren().contains(this.subtitleLabel))
            {
                this.headerContainer.getChildren().add(this.subtitleLabel);
            }
        }
        else
        {
            this.headerContainer.getChildren().remove(this.subtitleLabel);
        }
    }
    
    /**
     * Update the card elevation.
     * 
     * @param elevation The new elevation
     */
    private void updateElevation(final Elevation elevation)
    {
        // Remove existing elevation classes
        getStyleClass().removeIf(styleClass -> styleClass.startsWith("elevation-"));
        
        // Add new elevation class
        getStyleClass().add(elevation.getStyleClass());
        
        // Update drop shadow effect
        final DropShadow shadow = new DropShadow();
        shadow.setRadius(elevation.getLevel());
        shadow.setOffsetY(elevation.getLevel() / 2.0);
        shadow.setColor(Color.rgba(0, 0, 0, 0.2));
        setEffect(shadow);
    }
    
    /**
     * Update the card style.
     * 
     * @param oldStyle The old style
     * @param newStyle The new style
     */
    private void updateStyle(final Style oldStyle, final Style newStyle)
    {
        if (oldStyle != null)
        {
            getStyleClass().remove("card-" + oldStyle.getStyleClass());
        }
        if (newStyle != null)
        {
            getStyleClass().add("card-" + newStyle.getStyleClass());
        }
    }
    
    /**
     * Update the interactive state.
     * 
     * @param interactive True if interactive
     */
    private void updateInteractiveState(final boolean interactive)
    {
        if (interactive)
        {
            getStyleClass().add("interactive");
            setCursor(javafx.scene.Cursor.HAND);
        }
        else
        {
            getStyleClass().remove("interactive");
            setCursor(javafx.scene.Cursor.DEFAULT);
        }
    }
    
    /**
     * Create a simple card with title and content.
     * 
     * @param title The card title
     * @param content The card content
     * @return A simple card
     */
    public static ModernCard createSimple(final String title, final Node content)
    {
        return new ModernCard(title, content);
    }
    
    /**
     * Create an elevated card.
     * 
     * @param title The card title
     * @return An elevated card
     */
    public static ModernCard createElevated(final String title)
    {
        final ModernCard card = new ModernCard(title);
        card.setElevation(Elevation.MEDIUM);
        card.setCardStyle(Style.ELEVATED);
        return card;
    }
    
    /**
     * Create an interactive card.
     * 
     * @param title The card title
     * @return An interactive card
     */
    public static ModernCard createInteractive(final String title)
    {
        final ModernCard card = new ModernCard(title);
        card.setInteractive(true);
        return card;
    }
    
    /**
     * Create an outlined card.
     * 
     * @param title The card title
     * @return An outlined card
     */
    public static ModernCard createOutlined(final String title)
    {
        final ModernCard card = new ModernCard(title);
        card.setCardStyle(Style.OUTLINED);
        card.setElevation(Elevation.FLAT);
        return card;
    }
}
