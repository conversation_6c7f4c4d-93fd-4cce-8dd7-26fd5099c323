package de.mossgrabers.projectconverter.gui.components;

import de.mossgrabers.projectconverter.gui.accessibility.AccessibilityInfo;
import de.mossgrabers.projectconverter.gui.accessibility.AccessibilityManager;

import javafx.animation.Animation;
import javafx.animation.KeyFrame;
import javafx.animation.KeyValue;
import javafx.animation.Timeline;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.Pos;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.shape.Arc;
import javafx.scene.shape.ArcType;
import javafx.scene.shape.Circle;
import javafx.util.Duration;

/**
 * Modern progress indicator component with multiple styles and smooth animations.
 * Supports linear, circular, and indeterminate progress indicators with accessibility features.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ModernProgressIndicator extends StackPane
{
    /**
     * Progress indicator styles.
     */
    public enum Style
    {
        LINEAR,
        CIRCULAR,
        CIRCULAR_WITH_TEXT,
        MINIMAL
    }
    
    // Properties
    private final DoubleProperty progressProperty = new SimpleDoubleProperty(0.0);
    private final BooleanProperty indeterminateProperty = new SimpleBooleanProperty(false);
    private final ObjectProperty<Style> styleProperty = new SimpleObjectProperty<>(Style.LINEAR);
    private final StringProperty textProperty = new SimpleStringProperty("");
    private final BooleanProperty animationsEnabledProperty = new SimpleBooleanProperty(true);
    private final StringProperty accessibleDescriptionProperty = new SimpleStringProperty();
    
    // UI Components
    private ProgressBar linearProgressBar;
    private Arc circularProgressArc;
    private Circle circularBackground;
    private Label progressLabel;
    private VBox contentContainer;
    
    // Animations
    private Timeline progressAnimation;
    private Timeline indeterminateAnimation;
    
    /**
     * Constructor.
     */
    public ModernProgressIndicator()
    {
        this(Style.LINEAR);
    }
    
    /**
     * Constructor with style.
     * 
     * @param style The progress indicator style
     */
    public ModernProgressIndicator(final Style style)
    {
        setProgressStyle(style);
        initialize();
    }
    
    /**
     * Initialize the progress indicator.
     */
    private void initialize()
    {
        setupComponents();
        setupAnimations();
        setupAccessibility();
        setupPropertyBindings();
        setupStyleClasses();
    }
    
    /**
     * Set up UI components.
     */
    private void setupComponents()
    {
        // Linear progress bar
        this.linearProgressBar = new ProgressBar();
        this.linearProgressBar.getStyleClass().add("modern-progress-bar");
        
        // Circular progress components
        this.circularBackground = new Circle(50);
        this.circularBackground.getStyleClass().add("circular-progress-background");
        
        this.circularProgressArc = new Arc();
        this.circularProgressArc.setCenterX(0);
        this.circularProgressArc.setCenterY(0);
        this.circularProgressArc.setRadiusX(45);
        this.circularProgressArc.setRadiusY(45);
        this.circularProgressArc.setStartAngle(90);
        this.circularProgressArc.setType(ArcType.OPEN);
        this.circularProgressArc.getStyleClass().add("circular-progress-arc");
        
        // Progress label
        this.progressLabel = new Label();
        this.progressLabel.getStyleClass().add("progress-label");
        
        // Content container
        this.contentContainer = new VBox(8);
        this.contentContainer.setAlignment(Pos.CENTER);
        
        updateComponentsForStyle();
    }
    
    /**
     * Set up animations.
     */
    private void setupAnimations()
    {
        // Progress change animation
        this.progressAnimation = new Timeline();
        this.progressAnimation.setOnFinished(e -> updateAccessibilityAnnouncement());
        
        // Indeterminate animation
        this.indeterminateAnimation = new Timeline(
            new KeyFrame(Duration.ZERO, new KeyValue(this.circularProgressArc.startAngleProperty(), 90)),
            new KeyFrame(Duration.seconds(2), new KeyValue(this.circularProgressArc.startAngleProperty(), 450))
        );
        this.indeterminateAnimation.setCycleCount(Animation.INDEFINITE);
    }
    
    /**
     * Set up accessibility features.
     */
    private void setupAccessibility()
    {
        updateAccessibilityInfo();
    }
    
    /**
     * Set up property bindings.
     */
    private void setupPropertyBindings()
    {
        // Progress property binding
        this.progressProperty.addListener((obs, oldProgress, newProgress) -> {
            updateProgress(newProgress.doubleValue());
        });
        
        // Indeterminate property binding
        this.indeterminateProperty.addListener((obs, oldIndeterminate, newIndeterminate) -> {
            updateIndeterminateState(newIndeterminate);
        });
        
        // Style property binding
        this.styleProperty.addListener((obs, oldStyle, newStyle) -> {
            updateComponentsForStyle();
        });
        
        // Text property binding
        this.textProperty.addListener((obs, oldText, newText) -> {
            updateProgressText(newText);
        });
        
        // Accessibility description binding
        this.accessibleDescriptionProperty.addListener((obs, oldDesc, newDesc) -> {
            updateAccessibilityInfo();
        });
    }
    
    /**
     * Set up style classes.
     */
    private void setupStyleClasses()
    {
        getStyleClass().addAll("modern-progress-indicator", 
                              "progress-" + getProgressStyle().name().toLowerCase());
    }
    
    // Property getters and setters
    public DoubleProperty progressProperty() { return this.progressProperty; }
    public double getProgress() { return this.progressProperty.get(); }
    public void setProgress(final double progress) { this.progressProperty.set(Math.max(0.0, Math.min(1.0, progress))); }
    
    public BooleanProperty indeterminateProperty() { return this.indeterminateProperty; }
    public boolean isIndeterminate() { return this.indeterminateProperty.get(); }
    public void setIndeterminate(final boolean indeterminate) { this.indeterminateProperty.set(indeterminate); }
    
    public ObjectProperty<Style> styleProperty() { return this.styleProperty; }
    public Style getProgressStyle() { return this.styleProperty.get(); }
    public void setProgressStyle(final Style style) { this.styleProperty.set(style); }
    
    public StringProperty textProperty() { return this.textProperty; }
    public String getText() { return this.textProperty.get(); }
    public void setText(final String text) { this.textProperty.set(text); }
    
    public BooleanProperty animationsEnabledProperty() { return this.animationsEnabledProperty; }
    public boolean isAnimationsEnabled() { return this.animationsEnabledProperty.get(); }
    public void setAnimationsEnabled(final boolean enabled) { this.animationsEnabledProperty.set(enabled); }
    
    public StringProperty accessibleDescriptionProperty() { return this.accessibleDescriptionProperty; }
    public String getAccessibleDescription() { return this.accessibleDescriptionProperty.get(); }
    public void setAccessibleDescription(final String description) { this.accessibleDescriptionProperty.set(description); }
    
    /**
     * Update progress with optional animation.
     * 
     * @param newProgress The new progress value
     */
    private void updateProgress(final double newProgress)
    {
        final double clampedProgress = Math.max(0.0, Math.min(1.0, newProgress));
        
        if (isAnimationsEnabled() && !isIndeterminate())
        {
            // Animate progress change
            this.progressAnimation.stop();
            this.progressAnimation.getKeyFrames().clear();
            
            if (getProgressStyle() == Style.LINEAR || getProgressStyle() == Style.MINIMAL)
            {
                this.progressAnimation.getKeyFrames().add(
                    new KeyFrame(Duration.millis(300), 
                               new KeyValue(this.linearProgressBar.progressProperty(), clampedProgress))
                );
            }
            else if (getProgressStyle() == Style.CIRCULAR || getProgressStyle() == Style.CIRCULAR_WITH_TEXT)
            {
                final double arcLength = clampedProgress * 360;
                this.progressAnimation.getKeyFrames().add(
                    new KeyFrame(Duration.millis(300), 
                               new KeyValue(this.circularProgressArc.lengthProperty(), -arcLength))
                );
            }
            
            this.progressAnimation.play();
        }
        else
        {
            // Set progress immediately
            if (getProgressStyle() == Style.LINEAR || getProgressStyle() == Style.MINIMAL)
            {
                this.linearProgressBar.setProgress(clampedProgress);
            }
            else if (getProgressStyle() == Style.CIRCULAR || getProgressStyle() == Style.CIRCULAR_WITH_TEXT)
            {
                final double arcLength = clampedProgress * 360;
                this.circularProgressArc.setLength(-arcLength);
            }
        }
        
        // Update text if showing percentage
        if (getProgressStyle() == Style.CIRCULAR_WITH_TEXT && getText().isEmpty())
        {
            this.progressLabel.setText(String.format("%.0f%%", clampedProgress * 100));
        }
    }
    
    /**
     * Update indeterminate state.
     * 
     * @param indeterminate True if indeterminate
     */
    private void updateIndeterminateState(final boolean indeterminate)
    {
        if (indeterminate)
        {
            if (getProgressStyle() == Style.LINEAR || getProgressStyle() == Style.MINIMAL)
            {
                this.linearProgressBar.setProgress(-1);
            }
            else if (getProgressStyle() == Style.CIRCULAR || getProgressStyle() == Style.CIRCULAR_WITH_TEXT)
            {
                this.circularProgressArc.setLength(-90);
                if (isAnimationsEnabled())
                {
                    this.indeterminateAnimation.play();
                }
            }
        }
        else
        {
            this.indeterminateAnimation.stop();
            updateProgress(getProgress());
        }
    }
    
    /**
     * Update components based on current style.
     */
    private void updateComponentsForStyle()
    {
        getChildren().clear();
        
        switch (getProgressStyle())
        {
            case LINEAR:
            case MINIMAL:
                getChildren().add(this.linearProgressBar);
                break;
                
            case CIRCULAR:
                getChildren().addAll(this.circularBackground, this.circularProgressArc);
                break;
                
            case CIRCULAR_WITH_TEXT:
                getChildren().addAll(this.circularBackground, this.circularProgressArc, this.progressLabel);
                break;
        }
        
        // Update style classes
        getStyleClass().removeIf(styleClass -> styleClass.startsWith("progress-"));
        getStyleClass().add("progress-" + getProgressStyle().name().toLowerCase());
    }
    
    /**
     * Update progress text.
     * 
     * @param text The new text
     */
    private void updateProgressText(final String text)
    {
        if (this.progressLabel != null)
        {
            this.progressLabel.setText(text);
        }
    }
    
    /**
     * Update accessibility information.
     */
    private void updateAccessibilityInfo()
    {
        final String description = getAccessibleDescription();
        final double progress = getProgress();
        final AccessibilityInfo accessibilityInfo = AccessibilityInfo.forProgressBar(
            "Progress", progress, 0.0, 1.0, description);
        
        AccessibilityManager.getInstance().setAccessibilityInfo(this, accessibilityInfo);
    }
    
    /**
     * Update accessibility announcement for progress changes.
     */
    private void updateAccessibilityAnnouncement()
    {
        if (!isIndeterminate())
        {
            final double progress = getProgress();
            final String announcement = String.format("Progress: %.0f%% complete", progress * 100);
            AccessibilityManager.getInstance().announceToScreenReader(announcement);
        }
    }
    
    /**
     * Set progress with animation.
     * 
     * @param progress The target progress
     * @param duration The animation duration
     */
    public void animateToProgress(final double progress, final Duration duration)
    {
        if (isAnimationsEnabled())
        {
            this.progressAnimation.stop();
            this.progressAnimation.getKeyFrames().clear();
            
            final double clampedProgress = Math.max(0.0, Math.min(1.0, progress));
            
            if (getProgressStyle() == Style.LINEAR || getProgressStyle() == Style.MINIMAL)
            {
                this.progressAnimation.getKeyFrames().add(
                    new KeyFrame(duration, new KeyValue(this.linearProgressBar.progressProperty(), clampedProgress))
                );
            }
            else if (getProgressStyle() == Style.CIRCULAR || getProgressStyle() == Style.CIRCULAR_WITH_TEXT)
            {
                final double arcLength = clampedProgress * 360;
                this.progressAnimation.getKeyFrames().add(
                    new KeyFrame(duration, new KeyValue(this.circularProgressArc.lengthProperty(), -arcLength))
                );
            }
            
            this.progressAnimation.play();
        }
        else
        {
            setProgress(progress);
        }
    }
    
    /**
     * Create a linear progress indicator.
     * 
     * @return A linear progress indicator
     */
    public static ModernProgressIndicator createLinear()
    {
        return new ModernProgressIndicator(Style.LINEAR);
    }
    
    /**
     * Create a circular progress indicator.
     * 
     * @return A circular progress indicator
     */
    public static ModernProgressIndicator createCircular()
    {
        return new ModernProgressIndicator(Style.CIRCULAR);
    }
    
    /**
     * Create a circular progress indicator with text.
     * 
     * @return A circular progress indicator with text
     */
    public static ModernProgressIndicator createCircularWithText()
    {
        return new ModernProgressIndicator(Style.CIRCULAR_WITH_TEXT);
    }
    
    /**
     * Create a minimal progress indicator.
     * 
     * @return A minimal progress indicator
     */
    public static ModernProgressIndicator createMinimal()
    {
        return new ModernProgressIndicator(Style.MINIMAL);
    }
}
