{"doc": " Base interface for all validators in the ProjectConverter application.\n \n @param <T> The type of object to validate\n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "validate", "paramTypes": ["java.lang.Object"], "doc": " Validate an object and return validation results.\n \n @param object The object to validate\n @return List of validation results (empty if valid)\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Object"], "doc": " Check if an object is valid (convenience method).\n \n @param object The object to validate\n @return True if valid, false otherwise\n"}, {"name": "validateOrThrow", "paramTypes": ["java.lang.Object"], "doc": " Validate an object and throw an exception if invalid.\n \n @param object The object to validate\n @throws ValidationException If validation fails\n"}, {"name": "getName", "paramTypes": [], "doc": " Get the name of this validator.\n \n @return The validator name\n"}, {"name": "getDescription", "paramTypes": [], "doc": " Get the description of what this validator checks.\n \n @return The validator description\n"}], "constructors": []}