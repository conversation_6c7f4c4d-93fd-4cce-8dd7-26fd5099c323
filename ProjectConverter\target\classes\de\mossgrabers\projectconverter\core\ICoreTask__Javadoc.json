{"doc": "\n Base interface for source and destination formats providing some descriptive metadata.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getName", "paramTypes": [], "doc": "\n Get the name of the object.\r\n\r\n @return The name\r\n"}, {"name": "getEditPane", "paramTypes": [], "doc": "\n Get the pane with the edit widgets.\r\n\r\n @return The pane\r\n"}, {"name": "saveSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "\n Save the settings of the detector.\r\n\r\n @param config Where to store to\r\n"}, {"name": "loadSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "\n Load the settings of the detector.\r\n\r\n @param config Where to load from\r\n"}, {"name": "shutdown", "paramTypes": [], "doc": "\n Shutdown the task. Execute some necessary cleanup.\r\n"}, {"name": "cancel", "paramTypes": [], "doc": "\n Cancel the task.\r\n"}], "constructors": []}