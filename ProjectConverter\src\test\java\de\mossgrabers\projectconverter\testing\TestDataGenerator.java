package de.mossgrabers.projectconverter.testing;

import com.bitwig.dawproject.Project;
import com.bitwig.dawproject.device.Device;
import com.bitwig.dawproject.timeline.clips.Clip;
import com.bitwig.dawproject.timeline.clips.Clips;
import com.bitwig.dawproject.timeline.lanes.Lane;
import com.bitwig.dawproject.timeline.lanes.Lanes;
import com.bitwig.dawproject.timeline.lanes.track.Track;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Generator for test data including sample project files and DAW projects.
 * Provides various project configurations for testing different scenarios.
 * 
 * <AUTHOR> ProjectConverter
 */
public class TestDataGenerator
{
    private static final Random RANDOM = new Random(12345); // Fixed seed for reproducible tests
    
    private static final String[] TRACK_NAMES = {
        "Kick", "Snare", "Hi-<PERSON>", "<PERSON>", "Lead", "Pad", "Arp", "FX", "Vocal", "Guitar"
    };
    
    private static final String[] PLUGIN_NAMES = {
        "Massive", "Serum", "Sylenth1", "Omnisphere", "Kontakt", "Battery", "Reaktor", "Absynth"
    };
    
    private static final String[] VST_IDS = {
        "MaSv", "SerM", "SyLn", "OmNi", "KoNt", "BaTt", "ReAk", "AbSy"
    };
    
    /**
     * Generate a simple test project with basic structure.
     * 
     * @return Simple test project
     */
    public static Project generateSimpleProject()
    {
        final Project project = new Project();
        
        // Basic project info
        project.application = new com.bitwig.dawproject.Application();
        project.application.name = "Test DAW";
        project.application.version = "1.0.0";
        
        // Transport settings
        project.transport = new com.bitwig.dawproject.Transport();
        project.transport.tempo = new com.bitwig.dawproject.RealParameter();
        project.transport.tempo.value = 120.0;
        
        project.transport.timeSignature = new com.bitwig.dawproject.TimeSignature();
        project.transport.timeSignature.numerator = 4;
        project.transport.timeSignature.denominator = 4;
        
        // Create simple track structure
        project.structure = new ArrayList<>();
        
        // Add a few basic tracks
        for (int i = 0; i < 3; i++)
        {
            final Track track = createSimpleTrack(TRACK_NAMES[i % TRACK_NAMES.length], i);
            project.structure.add(track);
        }
        
        // Create arrangement
        project.arrangement = new com.bitwig.dawproject.Arrangement();
        project.arrangement.lanes = new Lanes();
        
        return project;
    }
    
    /**
     * Generate a complex test project with many tracks, devices, and clips.
     * 
     * @return Complex test project
     */
    public static Project generateComplexProject()
    {
        final Project project = generateSimpleProject();
        
        // Add more tracks
        for (int i = 3; i < 10; i++)
        {
            final Track track = createComplexTrack(TRACK_NAMES[i % TRACK_NAMES.length], i);
            project.structure.add(track);
        }
        
        // Add nested tracks (folder structure)
        final Track folderTrack = createFolderTrack("Drums", 10);
        for (int i = 0; i < 4; i++)
        {
            final Track subTrack = createSimpleTrack("Drum " + (i + 1), 11 + i);
            folderTrack.tracks.add(subTrack);
        }
        project.structure.add(folderTrack);
        
        // Add arrangement with clips
        createComplexArrangement(project);
        
        return project;
    }
    
    /**
     * Generate a project with problematic content for testing error handling.
     * 
     * @return Project with potential issues
     */
    public static Project generateProblematicProject()
    {
        final Project project = new Project();
        
        // Missing application info (should trigger warning)
        
        // Invalid tempo
        project.transport = new com.bitwig.dawproject.Transport();
        project.transport.tempo = new com.bitwig.dawproject.RealParameter();
        project.transport.tempo.value = 999.0; // Unusual tempo
        
        // Invalid time signature
        project.transport.timeSignature = new com.bitwig.dawproject.TimeSignature();
        project.transport.timeSignature.numerator = 17; // Unusual numerator
        project.transport.timeSignature.denominator = 7; // Not power of 2
        
        // Create tracks with issues
        project.structure = new ArrayList<>();
        
        // Track with no name
        final Track namelessTrack = new Track();
        namelessTrack.id = "track1";
        project.structure.add(namelessTrack);
        
        // Track with invalid channel count
        final Track invalidChannelTrack = createSimpleTrack("Invalid", 1);
        invalidChannelTrack.channel.audioChannels = -1; // Invalid
        project.structure.add(invalidChannelTrack);
        
        // Track with problematic device
        final Track deviceTrack = createSimpleTrack("Device Issues", 2);
        final Device problematicDevice = new Device();
        problematicDevice.deviceID = ""; // Empty ID
        problematicDevice.deviceName = null; // Null name
        deviceTrack.channel.devices.add(problematicDevice);
        project.structure.add(deviceTrack);
        
        return project;
    }
    
    /**
     * Generate a large project for performance testing.
     * 
     * @param trackCount Number of tracks to create
     * @param clipsPerTrack Number of clips per track
     * @return Large test project
     */
    public static Project generateLargeProject(final int trackCount, final int clipsPerTrack)
    {
        final Project project = generateSimpleProject();
        project.structure.clear();
        
        // Create many tracks
        for (int i = 0; i < trackCount; i++)
        {
            final Track track = createComplexTrack("Track " + (i + 1), i);
            
            // Add many clips to arrangement
            if (project.arrangement.lanes.size() <= i)
            {
                project.arrangement.lanes.add(new Lane());
            }
            
            final Lane lane = project.arrangement.lanes.get(i);
            if (lane.clips == null)
            {
                lane.clips = new Clips();
            }
            
            for (int j = 0; j < clipsPerTrack; j++)
            {
                final Clip clip = createTestClip("Clip " + (j + 1), j * 4.0, 4.0);
                lane.clips.add(clip);
            }
            
            project.structure.add(track);
        }
        
        return project;
    }
    
    /**
     * Generate a test Reaper project file content.
     * 
     * @param tempDir Temporary directory for file creation
     * @return File containing Reaper project content
     * @throws IOException If file creation fails
     */
    public static File generateReaperProjectFile(final Path tempDir) throws IOException
    {
        final StringBuilder content = new StringBuilder();
        
        // Reaper project header
        content.append("<REAPER_PROJECT 0.1 \"6.82/win64\" 1234567890\n");
        content.append("  RIPPLE 0\n");
        content.append("  GROUPOVERRIDE 0 0 0\n");
        content.append("  AUTOXFADE 1\n");
        content.append("  ENVATTACH 1\n");
        content.append("  POOLEDENVATTACH 0\n");
        content.append("  MIXERUIFLAGS 11 48\n");
        content.append("  PEAKGAIN 1\n");
        content.append("  FEEDBACK 0\n");
        content.append("  PANLAW 1\n");
        content.append("  PROJOFFS 0 0 0\n");
        content.append("  MAXPROJLEN 0 600\n");
        content.append("  GRID 3199 8 1 8 1 0 0 0\n");
        content.append("  TIMEMODE 1 5 -1 30 0 0 -1\n");
        content.append("  VIDEO_CONFIG 0 0 256\n");
        content.append("  PANMODE 3\n");
        content.append("  CURSOR 0\n");
        content.append("  ZOOM 100 0 0\n");
        content.append("  VZOOMEX 6 0\n");
        content.append("  USE_REC_CFG 0\n");
        content.append("  RECMODE 1\n");
        content.append("  SMPTESYNC 0 30 100 40 1000 300 0 0 1 0 0\n");
        content.append("  LOOP 0\n");
        content.append("  LOOPGRAN 0 4\n");
        content.append("  RECORD_PATH \"\" \"\"\n");
        content.append("  <RECORD_CFG\n");
        content.append("    ZXZhdxgAAQ==\n");
        content.append("  >\n");
        content.append("  <APPLYFX_CFG\n");
        content.append("  >\n");
        content.append("  RENDER_FILE \"\"\n");
        content.append("  RENDER_PATTERN \"\"\n");
        content.append("  RENDER_FMT 0 2 0\n");
        content.append("  RENDER_1X 0\n");
        content.append("  RENDER_RANGE 1 0 0 18 1000\n");
        content.append("  RENDER_RESAMPLE 3 0 1\n");
        content.append("  RENDER_ADDTOPROJ 0\n");
        content.append("  RENDER_STEMS 0\n");
        content.append("  RENDER_DITHER 0\n");
        content.append("  TIMELOCKMODE 1\n");
        content.append("  TEMPOENVLOCKMODE 1\n");
        content.append("  ITEMMIX 0\n");
        content.append("  DEFPITCHMODE 589824 0\n");
        content.append("  TAKELANE 1\n");
        content.append("  SAMPLERATE 44100 0 0\n");
        
        // Add some tracks
        for (int i = 0; i < 3; i++)
        {
            content.append("  <TRACK ").append(generateTrackGuid()).append("\n");
            content.append("    NAME \"").append(TRACK_NAMES[i]).append("\"\n");
            content.append("    PEAKCOL 16576\n");
            content.append("    BEAT -1\n");
            content.append("    AUTOMODE 0\n");
            content.append("    VOLPAN 1 0 -1 -1 1\n");
            content.append("    MUTESOLO 0 0 0\n");
            content.append("    IPHASE 0\n");
            content.append("    PLAYOFFS 0 1\n");
            content.append("    ISBUS 0 0\n");
            content.append("    BUSCOMP 0 0 0 0 0\n");
            content.append("    SHOWINMIX 1 0.6667 0.5 1 0.5 0 0 0\n");
            content.append("    FREEMODE 0\n");
            content.append("    REC 0 0 1 0 0 0 0\n");
            content.append("    VU 2\n");
            content.append("    TRACKHEIGHT 0 0 0 0 0 0\n");
            content.append("    INQ 0 0 0 0.5 100 0 0 100\n");
            content.append("    NCHAN 2\n");
            content.append("    FX 1\n");
            content.append("    TRACKID ").append(generateTrackGuid()).append("\n");
            content.append("    PERF 0\n");
            content.append("    MIDIOUT -1\n");
            content.append("    MAINSEND 1 0\n");
            content.append("  >\n");
        }
        
        content.append(">\n");
        
        final File file = tempDir.resolve("test_project.rpp").toFile();
        Files.write(file.toPath(), content.toString().getBytes());
        return file;
    }
    
    private static Track createSimpleTrack(final String name, final int index)
    {
        final Track track = new Track();
        track.id = "track" + index;
        track.name = name;
        
        track.channel = new com.bitwig.dawproject.timeline.lanes.track.Channel();
        track.channel.audioChannels = 2; // Stereo
        track.channel.devices = new ArrayList<>();
        
        return track;
    }
    
    private static Track createComplexTrack(final String name, final int index)
    {
        final Track track = createSimpleTrack(name, index);
        
        // Add some devices
        for (int i = 0; i < RANDOM.nextInt(3) + 1; i++)
        {
            final Device device = createTestDevice(i);
            track.channel.devices.add(device);
        }
        
        return track;
    }
    
    private static Track createFolderTrack(final String name, final int index)
    {
        final Track track = createSimpleTrack(name, index);
        track.tracks = new ArrayList<>();
        return track;
    }
    
    private static Device createTestDevice(final int index)
    {
        final Device device = new Device();
        device.deviceID = VST_IDS[index % VST_IDS.length];
        device.deviceName = PLUGIN_NAMES[index % PLUGIN_NAMES.length];
        device.deviceRole = "Instrument";
        device.deviceVendor = "Test Vendor";
        device.deviceVersion = "1.0.0";
        
        device.state = new com.bitwig.dawproject.device.DeviceState();
        device.state.path = "test_state_" + index + ".dat";
        
        return device;
    }
    
    private static Clip createTestClip(final String name, final double time, final double duration)
    {
        final Clip clip = new Clip();
        clip.name = name;
        clip.time = time;
        clip.duration = duration;
        clip.playStart = 0.0;
        clip.loopStart = 0.0;
        clip.loopEnd = duration;
        
        return clip;
    }
    
    private static void createComplexArrangement(final Project project)
    {
        if (project.arrangement == null)
        {
            project.arrangement = new com.bitwig.dawproject.Arrangement();
            project.arrangement.lanes = new Lanes();
        }
        
        // Create lanes for each track
        for (int i = 0; i < project.structure.size(); i++)
        {
            final Lane lane = new Lane();
            lane.clips = new Clips();
            
            // Add some clips
            for (int j = 0; j < RANDOM.nextInt(5) + 1; j++)
            {
                final double startTime = j * 8.0 + RANDOM.nextDouble() * 2.0;
                final double duration = 4.0 + RANDOM.nextDouble() * 4.0;
                final Clip clip = createTestClip("Clip " + (j + 1), startTime, duration);
                lane.clips.add(clip);
            }
            
            project.arrangement.lanes.add(lane);
        }
    }
    
    private static String generateTrackGuid()
    {
        return String.format("{%08X-%04X-%04X-%04X-%012X}", 
                           RANDOM.nextInt(), 
                           RANDOM.nextInt(0x10000), 
                           RANDOM.nextInt(0x10000), 
                           RANDOM.nextInt(0x10000), 
                           RANDOM.nextLong() & 0xFFFFFFFFFFFFL);
    }
    
    /**
     * Create a temporary directory for test files.
     * 
     * @return Temporary directory path
     * @throws IOException If directory creation fails
     */
    public static Path createTempDirectory() throws IOException
    {
        return Files.createTempDirectory("projectconverter_test_");
    }
    
    /**
     * Clean up temporary test files.
     * 
     * @param tempDir Temporary directory to clean up
     */
    public static void cleanupTempDirectory(final Path tempDir)
    {
        try
        {
            if (Files.exists(tempDir))
            {
                Files.walk(tempDir)
                     .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                     .forEach(path -> {
                         try
                         {
                             Files.delete(path);
                         }
                         catch (final IOException e)
                         {
                             // Ignore cleanup errors
                         }
                     });
            }
        }
        catch (final IOException e)
        {
            // Ignore cleanup errors
        }
    }
}
