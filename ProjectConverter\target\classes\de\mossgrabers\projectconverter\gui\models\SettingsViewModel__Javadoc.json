{"doc": " View model for application settings and preferences.\n Manages user preferences, theme settings, and application configuration.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "setupAutoSaveListeners", "paramTypes": [], "doc": " Set up auto-save listeners for all properties.\n"}, {"name": "loadSettings", "paramTypes": [], "doc": " Load settings from preferences.\n"}, {"name": "saveSettings", "paramTypes": [], "doc": " Save settings to preferences.\n"}, {"name": "resetToDefaults", "paramTypes": [], "doc": " Reset all settings to defaults.\n"}, {"name": "autoSave", "paramTypes": [], "doc": " Auto-save settings if enabled.\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}]}