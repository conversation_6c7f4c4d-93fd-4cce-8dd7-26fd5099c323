{"doc": "\n The data of a device chunk in the Reaper project file.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "chunkToFile", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.OutputStream"], "doc": "\n Parses the content of a device chunk and stores it to a preset file.\r\n\r\n @param chunk The chunk to parse\r\n @param out Where to write the output\r\n @throws IOException If an error occurs\r\n"}, {"name": "fileToChunk", "paramTypes": ["java.io.InputStream", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Creates a device chunk from a preset file.\r\n\r\n @param in Where to read the preset from\r\n @param chunk The chunk to create\r\n @throws IOException If an error occurs\r\n"}, {"name": "createLines", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "byte[]"], "doc": "\n Creates Base64 encoded text as child nodes of the given parent chunk.\r\n\r\n @param parentChunk Where to add the Base64 encoded lines\r\n @param lineData The data to encode\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Constructor.\r\n"}]}