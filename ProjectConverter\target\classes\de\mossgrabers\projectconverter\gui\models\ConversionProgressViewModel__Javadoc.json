{"doc": " View model for tracking conversion progress and logging.\n Manages progress indicators, status messages, and log entries.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "startConversion", "paramTypes": [], "doc": " Start a new conversion process.\n"}, {"name": "completeConversion", "paramTypes": ["boolean"], "doc": " Complete the conversion process.\n \n @param success True if conversion was successful\n"}, {"name": "cancelConversion", "paramTypes": [], "doc": " Cancel the conversion process.\n"}, {"name": "updateProgress", "paramTypes": ["double", "java.lang.String"], "doc": " Update the conversion progress.\n \n @param progress Progress value between 0.0 and 1.0\n @param step Description of current step\n"}, {"name": "addLogEntry", "paramTypes": ["de.mossgrabers.projectconverter.gui.models.ConversionProgressViewModel.LogLevel", "java.lang.String"], "doc": " Add a log entry.\n \n @param level The log level\n @param message The log message\n"}, {"name": "addInfo", "paramTypes": ["java.lang.String"], "doc": " Add an info log entry.\n \n @param message The log message\n"}, {"name": "addWarning", "paramTypes": ["java.lang.String"], "doc": " Add a warning log entry.\n \n @param message The log message\n"}, {"name": "addError", "paramTypes": ["java.lang.String"], "doc": " Add an error log entry.\n \n @param message The log message\n"}, {"name": "clearLog", "paramTypes": [], "doc": " Clear all log entries.\n"}, {"name": "updateElapsedTime", "paramTypes": [], "doc": " Update the elapsed time display.\n"}, {"name": "updateLogText", "paramTypes": [], "doc": " Update the log text property from log entries.\n"}, {"name": "setStartTime", "paramTypes": ["java.lang.String"], "doc": " Set the start time.\n \n @param startTime The start time string\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}]}