// Written by <PERSON><PERSON><PERSON> - mossgrabers.de
// (c) 2021-2024
// Licensed under LGPLv3 - http://www.gnu.org/licenses/lgpl-3.0.txt

/**
 * The project converter module.
 *
 * <AUTHOR>
 */
module de.mossgrabers.projectconverter
{
    requires java.desktop;
    requires java.logging;
    requires transitive java.prefs;
    requires transitive javafx.controls;
    requires transitive java.xml;
    requires transitive de.mossgrabers.uitools;
    requires com.github.trilarion.sound;

    requires javafx.graphics;
    requires transitive com.bitwig.dawproject;
    requires jakarta.xml.bind;

    // Enhanced dependencies
    requires org.slf4j;
    requires ch.qos.logback.classic;
    requires com.fasterxml.jackson.databind;
    requires com.fasterxml.jackson.datatype.jsr310;
    requires org.apache.commons.lang3;
    requires org.apache.commons.io;
    requires org.controlsfx.controls;
    requires org.hibernate.validator;
    requires jakarta.el;


    exports de.mossgrabers.projectconverter;
    exports de.mossgrabers.projectconverter.ui;
    exports de.mossgrabers.projectconverter.core;
    exports de.mossgrabers.projectconverter.format.reaper;
    exports de.mossgrabers.projectconverter.format.reaper.model;
    exports de.mossgrabers.projectconverter.format.dawproject;

    // New enhanced packages
    exports de.mossgrabers.projectconverter.analysis;
    exports de.mossgrabers.projectconverter.gui;
    exports de.mossgrabers.projectconverter.gui.components;
    exports de.mossgrabers.projectconverter.gui.controllers;
    exports de.mossgrabers.projectconverter.gui.models;
    exports de.mossgrabers.projectconverter.gui.themes;
    exports de.mossgrabers.projectconverter.services;
    exports de.mossgrabers.projectconverter.validation;


    opens de.mossgrabers.projectconverter.css;
    opens de.mossgrabers.projectconverter.images;
}