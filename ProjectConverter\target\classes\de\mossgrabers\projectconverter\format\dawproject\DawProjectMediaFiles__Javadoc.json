{"doc": "\n Access to additional dawproject media files.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "stream", "paramTypes": ["java.lang.String"], "doc": "{@inheritDoc} "}, {"name": "add", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "{@inheritDoc} "}, {"name": "getAll", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "close", "paramTypes": [], "doc": "{@inheritDoc} "}], "constructors": [{"name": "<init>", "paramTypes": ["java.io.File"], "doc": "\n Constructor.\r\n\r\n @param sourceFile The DAWproject source file\r\n @throws IOException Could not open/read the ZIP source file\r\n"}]}