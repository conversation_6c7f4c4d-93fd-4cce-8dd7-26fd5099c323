package de.mossgrabers.projectconverter.core;

/**
 * Base exception class for all ProjectConverter-specific exceptions.
 * Provides enhanced error handling with error codes and recovery suggestions.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ProjectConverterException extends Exception
{
    private static final long serialVersionUID = 1L;
    
    private final ErrorCode errorCode;
    private final String userMessage;
    private final String technicalDetails;
    private final boolean recoverable;
    
    /**
     * Constructor with error code.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param technicalDetails Technical details for debugging
     * @param recoverable Whether the error is recoverable
     */
    public ProjectConverterException(final ErrorCode errorCode, final String userMessage, 
                                   final String technicalDetails, final boolean recoverable)
    {
        super(userMessage);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.technicalDetails = technicalDetails;
        this.recoverable = recoverable;
    }
    
    /**
     * Constructor with error code and cause.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param technicalDetails Technical details for debugging
     * @param recoverable Whether the error is recoverable
     * @param cause The underlying cause
     */
    public ProjectConverterException(final ErrorCode errorCode, final String userMessage, 
                                   final String technicalDetails, final boolean recoverable, 
                                   final Throwable cause)
    {
        super(userMessage, cause);
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.technicalDetails = technicalDetails;
        this.recoverable = recoverable;
    }
    
    /**
     * Get the error code.
     * 
     * @return The error code
     */
    public ErrorCode getErrorCode()
    {
        return this.errorCode;
    }
    
    /**
     * Get the user-friendly message.
     * 
     * @return The user message
     */
    public String getUserMessage()
    {
        return this.userMessage;
    }
    
    /**
     * Get the technical details.
     * 
     * @return The technical details
     */
    public String getTechnicalDetails()
    {
        return this.technicalDetails;
    }
    
    /**
     * Check if the error is recoverable.
     * 
     * @return True if recoverable
     */
    public boolean isRecoverable()
    {
        return this.recoverable;
    }
    
    /**
     * Get a formatted error message for logging.
     * 
     * @return Formatted error message
     */
    public String getFormattedMessage()
    {
        final StringBuilder sb = new StringBuilder();
        sb.append("[").append(this.errorCode).append("] ");
        sb.append(this.userMessage);
        if (this.technicalDetails != null && !this.technicalDetails.isEmpty())
        {
            sb.append(" - Technical details: ").append(this.technicalDetails);
        }
        sb.append(" (Recoverable: ").append(this.recoverable).append(")");
        return sb.toString();
    }
}
