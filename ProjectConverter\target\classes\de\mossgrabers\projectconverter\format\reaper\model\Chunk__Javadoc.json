{"doc": "\n A chunk in a Reaper project.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "addChildNode", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node"], "doc": "\n Add a child node to the chunk.\r\n\r\n @param node The node to add\r\n"}, {"name": "getChildNodes", "paramTypes": [], "doc": "\n Get all child nodes of the chunk.\r\n\r\n @return The child notes\r\n"}, {"name": "getChildNode", "paramTypes": ["java.lang.String"], "doc": "\n Lookup a child node of the chunk with a certain name.\r\n\r\n @param name The name of the node to look up\r\n @return The first matching node or empty if not found\r\n"}, {"name": "getChildNodes", "paramTypes": ["java.lang.String"], "doc": "\n Lookup all child nodes of the chunk with a certain name.\r\n\r\n @param name The name of the node to look up\r\n @return The first matching node or empty if not found\r\n"}], "constructors": []}