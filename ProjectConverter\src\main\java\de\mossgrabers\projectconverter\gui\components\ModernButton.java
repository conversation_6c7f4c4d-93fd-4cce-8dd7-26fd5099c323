package de.mossgrabers.projectconverter.gui.components;

import de.mossgrabers.projectconverter.gui.accessibility.AccessibilityInfo;
import de.mossgrabers.projectconverter.gui.accessibility.AccessibilityManager;

import javafx.animation.FadeTransition;
import javafx.animation.ScaleTransition;
import javafx.animation.Timeline;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ContentDisplay;
import javafx.scene.control.Tooltip;
import javafx.scene.effect.DropShadow;
import javafx.scene.layout.StackPane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;
import javafx.util.Duration;

/**
 * Modern styled button component with animations, accessibility features, and Material Design styling.
 * Supports different button styles, loading states, and smooth animations.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ModernButton extends Button
{
    /**
     * Button style variants.
     */
    public enum Style
    {
        PRIMARY("primary"),
        SECONDARY("secondary"),
        OUTLINED("outlined"),
        TEXT("text"),
        FLOATING("floating");
        
        private final String styleClass;
        
        Style(final String styleClass)
        {
            this.styleClass = styleClass;
        }
        
        public String getStyleClass()
        {
            return this.styleClass;
        }
    }
    
    /**
     * Button size variants.
     */
    public enum Size
    {
        SMALL("small"),
        MEDIUM("medium"),
        LARGE("large");
        
        private final String styleClass;
        
        Size(final String styleClass)
        {
            this.styleClass = styleClass;
        }
        
        public String getStyleClass()
        {
            return this.styleClass;
        }
    }
    
    // Properties
    private final ObjectProperty<Style> styleProperty = new SimpleObjectProperty<>(Style.PRIMARY);
    private final ObjectProperty<Size> sizeProperty = new SimpleObjectProperty<>(Size.MEDIUM);
    private final BooleanProperty loadingProperty = new SimpleBooleanProperty(false);
    private final BooleanProperty animationsEnabledProperty = new SimpleBooleanProperty(true);
    private final StringProperty accessibleDescriptionProperty = new SimpleStringProperty();
    
    // Animation components
    private ScaleTransition pressAnimation;
    private FadeTransition hoverAnimation;
    private Timeline rippleAnimation;
    private Circle rippleCircle;
    
    /**
     * Constructor.
     */
    public ModernButton()
    {
        this("");
    }
    
    /**
     * Constructor with text.
     * 
     * @param text The button text
     */
    public ModernButton(final String text)
    {
        super(text);
        initialize();
    }
    
    /**
     * Constructor with text and style.
     * 
     * @param text The button text
     * @param style The button style
     */
    public ModernButton(final String text, final Style style)
    {
        super(text);
        setButtonStyle(style);
        initialize();
    }
    
    /**
     * Initialize the button.
     */
    private void initialize()
    {
        setupStyleClasses();
        setupAnimations();
        setupAccessibility();
        setupPropertyBindings();
    }
    
    /**
     * Set up style classes.
     */
    private void setupStyleClasses()
    {
        getStyleClass().addAll("modern-button", "button-" + getButtonStyle().getStyleClass(), 
                              "button-" + getButtonSize().getStyleClass());
    }
    
    /**
     * Set up animations.
     */
    private void setupAnimations()
    {
        // Press animation
        this.pressAnimation = new ScaleTransition(Duration.millis(100), this);
        this.pressAnimation.setFromX(1.0);
        this.pressAnimation.setFromY(1.0);
        this.pressAnimation.setToX(0.95);
        this.pressAnimation.setToY(0.95);
        this.pressAnimation.setAutoReverse(true);
        this.pressAnimation.setCycleCount(2);
        
        // Hover animation
        this.hoverAnimation = new FadeTransition(Duration.millis(200), this);
        
        // Set up event handlers for animations
        setOnMousePressed(e -> {
            if (isAnimationsEnabled())
            {
                this.pressAnimation.playFromStart();
            }
        });
        
        setOnMouseEntered(e -> {
            if (isAnimationsEnabled())
            {
                setEffect(new DropShadow(10, Color.rgba(0, 0, 0, 0.3)));
            }
        });
        
        setOnMouseExited(e -> {
            if (isAnimationsEnabled())
            {
                setEffect(new DropShadow(5, Color.rgba(0, 0, 0, 0.2)));
            }
        });
    }
    
    /**
     * Set up accessibility features.
     */
    private void setupAccessibility()
    {
        // Set initial accessibility info
        updateAccessibilityInfo();
        
        // Update accessibility when text changes
        textProperty().addListener((obs, oldText, newText) -> updateAccessibilityInfo());
        
        // Update accessibility when description changes
        this.accessibleDescriptionProperty.addListener((obs, oldDesc, newDesc) -> updateAccessibilityInfo());
    }
    
    /**
     * Set up property bindings.
     */
    private void setupPropertyBindings()
    {
        // Update style classes when style changes
        this.styleProperty.addListener((obs, oldStyle, newStyle) -> {
            if (oldStyle != null)
            {
                getStyleClass().remove("button-" + oldStyle.getStyleClass());
            }
            if (newStyle != null)
            {
                getStyleClass().add("button-" + newStyle.getStyleClass());
            }
        });
        
        // Update style classes when size changes
        this.sizeProperty.addListener((obs, oldSize, newSize) -> {
            if (oldSize != null)
            {
                getStyleClass().remove("button-" + oldSize.getStyleClass());
            }
            if (newSize != null)
            {
                getStyleClass().add("button-" + newSize.getStyleClass());
            }
        });
        
        // Update loading state
        this.loadingProperty.addListener((obs, oldLoading, newLoading) -> {
            if (newLoading)
            {
                getStyleClass().add("loading");
                setDisable(true);
                setText("Loading...");
            }
            else
            {
                getStyleClass().remove("loading");
                setDisable(false);
            }
        });
    }
    
    // Property getters and setters
    public ObjectProperty<Style> styleProperty() { return this.styleProperty; }
    public Style getButtonStyle() { return this.styleProperty.get(); }
    public void setButtonStyle(final Style style) { this.styleProperty.set(style); }
    
    public ObjectProperty<Size> sizeProperty() { return this.sizeProperty; }
    public Size getButtonSize() { return this.sizeProperty.get(); }
    public void setButtonSize(final Size size) { this.sizeProperty.set(size); }
    
    public BooleanProperty loadingProperty() { return this.loadingProperty; }
    public boolean isLoading() { return this.loadingProperty.get(); }
    public void setLoading(final boolean loading) { this.loadingProperty.set(loading); }
    
    public BooleanProperty animationsEnabledProperty() { return this.animationsEnabledProperty; }
    public boolean isAnimationsEnabled() { return this.animationsEnabledProperty.get(); }
    public void setAnimationsEnabled(final boolean enabled) { this.animationsEnabledProperty.set(enabled); }
    
    public StringProperty accessibleDescriptionProperty() { return this.accessibleDescriptionProperty; }
    public String getAccessibleDescription() { return this.accessibleDescriptionProperty.get(); }
    public void setAccessibleDescription(final String description) { this.accessibleDescriptionProperty.set(description); }
    
    /**
     * Set the button as primary style.
     * 
     * @return This button for method chaining
     */
    public ModernButton primary()
    {
        setButtonStyle(Style.PRIMARY);
        return this;
    }
    
    /**
     * Set the button as secondary style.
     * 
     * @return This button for method chaining
     */
    public ModernButton secondary()
    {
        setButtonStyle(Style.SECONDARY);
        return this;
    }
    
    /**
     * Set the button as outlined style.
     * 
     * @return This button for method chaining
     */
    public ModernButton outlined()
    {
        setButtonStyle(Style.OUTLINED);
        return this;
    }
    
    /**
     * Set the button as text style.
     * 
     * @return This button for method chaining
     */
    public ModernButton textStyle()
    {
        setButtonStyle(Style.TEXT);
        return this;
    }
    
    /**
     * Set the button as floating action button style.
     * 
     * @return This button for method chaining
     */
    public ModernButton floating()
    {
        setButtonStyle(Style.FLOATING);
        return this;
    }
    
    /**
     * Set the button size to small.
     * 
     * @return This button for method chaining
     */
    public ModernButton small()
    {
        setButtonSize(Size.SMALL);
        return this;
    }
    
    /**
     * Set the button size to medium.
     * 
     * @return This button for method chaining
     */
    public ModernButton medium()
    {
        setButtonSize(Size.MEDIUM);
        return this;
    }
    
    /**
     * Set the button size to large.
     * 
     * @return This button for method chaining
     */
    public ModernButton large()
    {
        setButtonSize(Size.LARGE);
        return this;
    }
    
    /**
     * Set a tooltip for the button.
     * 
     * @param text The tooltip text
     * @return This button for method chaining
     */
    public ModernButton withTooltip(final String text)
    {
        setTooltip(new Tooltip(text));
        return this;
    }
    
    /**
     * Set an accessible description for the button.
     * 
     * @param description The accessible description
     * @return This button for method chaining
     */
    public ModernButton withAccessibleDescription(final String description)
    {
        setAccessibleDescription(description);
        return this;
    }
    
    /**
     * Enable or disable animations for this button.
     * 
     * @param enabled True to enable animations
     * @return This button for method chaining
     */
    public ModernButton withAnimations(final boolean enabled)
    {
        setAnimationsEnabled(enabled);
        return this;
    }
    
    /**
     * Update accessibility information.
     */
    private void updateAccessibilityInfo()
    {
        final String text = getText();
        final String description = getAccessibleDescription();
        
        if (text != null && !text.trim().isEmpty())
        {
            final AccessibilityInfo accessibilityInfo = AccessibilityInfo.forButton(text, description);
            AccessibilityManager.getInstance().setAccessibilityInfo(this, accessibilityInfo);
        }
    }
    
    /**
     * Create a primary button.
     * 
     * @param text The button text
     * @return A primary button
     */
    public static ModernButton createPrimary(final String text)
    {
        return new ModernButton(text, Style.PRIMARY);
    }
    
    /**
     * Create a secondary button.
     * 
     * @param text The button text
     * @return A secondary button
     */
    public static ModernButton createSecondary(final String text)
    {
        return new ModernButton(text, Style.SECONDARY);
    }
    
    /**
     * Create an outlined button.
     * 
     * @param text The button text
     * @return An outlined button
     */
    public static ModernButton createOutlined(final String text)
    {
        return new ModernButton(text, Style.OUTLINED);
    }
    
    /**
     * Create a text button.
     * 
     * @param text The button text
     * @return A text button
     */
    public static ModernButton createText(final String text)
    {
        return new ModernButton(text, Style.TEXT);
    }
    
    /**
     * Create a floating action button.
     * 
     * @param text The button text
     * @return A floating action button
     */
    public static ModernButton createFloating(final String text)
    {
        return new ModernButton(text, Style.FLOATING);
    }
}
