{"doc": " Accessibility manager for enhancing keyboard navigation, screen reader support,\n and other accessibility features throughout the application.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getInstance", "paramTypes": [], "doc": " Get the singleton instance.\n \n @return The accessibility manager instance\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "registerScene", "paramTypes": ["javafx.scene.Scene"], "doc": " Register a scene for accessibility management.\n \n @param scene The scene to register\n"}, {"name": "unregisterScene", "paramTypes": ["javafx.scene.Scene"], "doc": " Unregister a scene from accessibility management.\n \n @param scene The scene to unregister\n"}, {"name": "setAccessibilityInfo", "paramTypes": ["javafx.scene.Node", "de.mossgrabers.projectconverter.gui.accessibility.AccessibilityInfo"], "doc": " Set accessibility information for a node.\n \n @param node The node\n @param accessibilityInfo The accessibility information\n"}, {"name": "setAccessibleText", "paramTypes": ["javafx.scene.Node", "java.lang.String"], "doc": " Set accessible text for a node.\n \n @param node The node\n @param accessibleText The accessible text\n"}, {"name": "setAccessibleHelp", "paramTypes": ["javafx.scene.Node", "java.lang.String"], "doc": " Set accessible help text for a node.\n \n @param node The node\n @param helpText The help text\n"}, {"name": "setAccessibleRole", "paramTypes": ["javafx.scene.Node", "javafx.scene.AccessibleRole"], "doc": " Set accessible role for a node.\n \n @param node The node\n @param role The accessible role\n"}, {"name": "addToFocusTraversal", "paramTypes": ["javafx.scene.Node"], "doc": " Add a node to the focus traversal order.\n \n @param node The node to add\n"}, {"name": "removeFromFocusTraversal", "paramTypes": ["javafx.scene.Node"], "doc": " Remove a node from the focus traversal order.\n \n @param node The node to remove\n"}, {"name": "focusNext", "paramTypes": [], "doc": " Move focus to the next node in traversal order.\n"}, {"name": "focusPrevious", "paramTypes": [], "doc": " Move focus to the previous node in traversal order.\n"}, {"name": "announceToScreenReader", "paramTypes": ["java.lang.String"], "doc": " Announce text to screen readers.\n \n @param text The text to announce\n"}, {"name": "addAccessibilityListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add an accessibility event listener.\n \n @param listener The listener to add\n"}, {"name": "removeAccessibilityListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove an accessibility event listener.\n \n @param listener The listener to remove\n"}, {"name": "setupSceneAccessibility", "paramTypes": ["javafx.scene.Scene"], "doc": " Set up accessibility for a scene.\n \n @param scene The scene to set up\n"}, {"name": "handleKeyboardNavigation", "paramTypes": ["javafx.scene.input.KeyEvent"], "doc": " Handle keyboard navigation events.\n \n @param event The keyboard event\n"}, {"name": "applyAccessibilityInfo", "paramTypes": ["javafx.scene.Node", "de.mossgrabers.projectconverter.gui.accessibility.AccessibilityInfo"], "doc": " Apply accessibility information to a node.\n \n @param node The node\n @param info The accessibility information\n"}, {"name": "updateScreenReaderSupport", "paramTypes": ["boolean"], "doc": " Update screen reader support.\n \n @param enabled True if enabled\n"}, {"name": "updateKeyboardNavigation", "paramTypes": ["boolean"], "doc": " Update keyboard navigation enhancement.\n \n @param enabled True if enabled\n"}, {"name": "updateHighContrastMode", "paramTypes": ["boolean"], "doc": " Update high contrast mode.\n \n @param enabled True if enabled\n"}, {"name": "updateFocusIndicatorSize", "paramTypes": ["double"], "doc": " Update focus indicator size.\n \n @param size The new size multiplier\n"}, {"name": "updateFontSizes", "paramTypes": ["double"], "doc": " Update font sizes.\n \n @param multiplier The font size multiplier\n"}, {"name": "enhanceSceneForScreenReaders", "paramTypes": ["javafx.scene.Scene"], "doc": " Enhance a scene for screen readers.\n \n @param scene The scene to enhance\n"}, {"name": "getNodeDescription", "paramTypes": ["javafx.scene.Node"], "doc": " Get a description of a node for screen readers.\n \n @param node The node\n @return A description of the node\n"}, {"name": "notifyAccessibilityListeners", "paramTypes": ["de.mossgrabers.projectconverter.gui.accessibility.AccessibilityEvent"], "doc": " Notify accessibility listeners.\n \n @param event The accessibility event\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Private constructor for singleton pattern.\n"}]}