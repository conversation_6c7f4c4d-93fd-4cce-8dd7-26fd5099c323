{"doc": "\n Interface for streaming media files between source to destination format. A media file can be an\r\n audio file or a device state.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "stream", "paramTypes": ["java.lang.String"], "doc": "\n Get an input stream to read a media file.\r\n\r\n @param id The ID of the media file\r\n @return The input stream to read the media file from\r\n @throws IOException An exception occurred during writing or the media file was not found\r\n"}, {"name": "add", "paramTypes": ["java.lang.String", "java.io.File"], "doc": "\n Add a media file.\r\n\r\n @param id The ID of the media file\r\n @param mediaFile The media file\r\n"}, {"name": "getAll", "paramTypes": [], "doc": "\n Get the IDs of all media files.\r\n\r\n @return All media files\r\n"}], "constructors": []}