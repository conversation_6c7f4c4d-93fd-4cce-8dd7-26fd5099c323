{"doc": " Validator for file system operations and file integrity.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getFileExtension", "paramTypes": ["java.io.File"], "doc": " Get the file extension without the dot.\n \n @param file The file\n @return The extension (empty string if no extension)\n"}, {"name": "forInputFile", "paramTypes": ["java.util.Set"], "doc": " Create a validator for input files.\n \n @param supportedExtensions Supported file extensions\n @return File validator for input files\n"}, {"name": "forOutputFile", "paramTypes": [], "doc": " Create a validator for output files.\n \n @return File validator for output files\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.util.Set", "boolean", "boolean"], "doc": " Constructor.\n \n @param supportedExtensions Set of supported file extensions (without dot)\n @param checkReadability Whether to check if file is readable\n @param checkWritability Whether to check if file is writable\n"}]}