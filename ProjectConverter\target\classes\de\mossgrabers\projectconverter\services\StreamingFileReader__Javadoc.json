{"doc": " Streaming file reader for efficiently processing large project files.\n Provides line-by-line reading with minimal memory footprint.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "readLine", "paramTypes": [], "doc": " Read the next line from the file.\n \n @return The next line, or null if end of file\n @throws ProjectConverterException If reading fails\n"}, {"name": "processLines", "paramTypes": ["java.util.function.Consumer"], "doc": " Process each line with a consumer function.\n \n @param lineProcessor The function to process each line\n @throws ProjectConverterException If processing fails\n"}, {"name": "processFilteredLines", "paramTypes": ["java.util.function.Predicate", "java.util.function.Consumer"], "doc": " Process lines that match a predicate.\n \n @param filter The predicate to filter lines\n @param lineProcessor The function to process matching lines\n @throws ProjectConverterException If processing fails\n"}, {"name": "skipLines", "paramTypes": ["long"], "doc": " Skip a number of lines.\n \n @param linesToSkip Number of lines to skip\n @return Number of lines actually skipped\n @throws ProjectConverterException If skipping fails\n"}, {"name": "getLinesRead", "paramTypes": [], "doc": " Get the number of lines read so far.\n \n @return Number of lines read\n"}, {"name": "getBytesRead", "paramTypes": [], "doc": " Get the approximate number of bytes read so far.\n \n @return Number of bytes read\n"}, {"name": "getFile", "paramTypes": [], "doc": " Get the file being read.\n \n @return The file\n"}, {"name": "getCharset", "paramTypes": [], "doc": " Get the charset being used.\n \n @return The charset\n"}, {"name": "isClosed", "paramTypes": [], "doc": " Check if the reader is closed.\n \n @return True if closed\n"}, {"name": "getProgress", "paramTypes": [], "doc": " Get reading progress as a ratio (0.0 to 1.0).\n \n @return Progress ratio\n"}, {"name": "getStatistics", "paramTypes": [], "doc": " Get reading statistics as a formatted string.\n \n @return Formatted statistics\n"}, {"name": "create", "paramTypes": ["java.io.File"], "doc": " Create a streaming reader with automatic charset detection.\n \n @param file The file to read\n @return Streaming file reader\n @throws ProjectConverterException If file cannot be opened\n"}, {"name": "createForLargeFile", "paramTypes": ["java.io.File"], "doc": " Create a streaming reader for large files with optimized settings.\n \n @param file The file to read\n @return Streaming file reader with large file optimizations\n @throws ProjectConverterException If file cannot be opened\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.io.File"], "doc": " Constructor with default settings.\n \n @param file The file to read\n @throws ProjectConverterException If file cannot be opened\n"}, {"name": "<init>", "paramTypes": ["java.io.File", "java.nio.charset.Charset"], "doc": " Constructor with custom charset.\n \n @param file The file to read\n @param charset The character encoding\n @throws ProjectConverterException If file cannot be opened\n"}, {"name": "<init>", "paramTypes": ["java.io.File", "java.nio.charset.Charset", "int"], "doc": " Constructor with full customization.\n \n @param file The file to read\n @param charset The character encoding\n @param bufferSize The buffer size for reading\n @throws ProjectConverterException If file cannot be opened\n"}]}