package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

import com.bitwig.dawproject.Project;
import com.bitwig.dawproject.device.Device;
import com.bitwig.dawproject.timeline.Timeline;
import com.bitwig.dawproject.timeline.clips.Clip;
import com.bitwig.dawproject.timeline.clips.Clips;
import com.bitwig.dawproject.timeline.lanes.Lane;
import com.bitwig.dawproject.timeline.lanes.Lanes;
import com.bitwig.dawproject.timeline.lanes.track.Track;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Validator for DAW project structure and content.
 * Validates project integrity, structure, and potential conversion issues.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ProjectValidator implements Validator<Project>
{
    private static final int MAX_TRACKS = 1000;
    private static final int MAX_CLIPS_PER_TRACK = 10000;
    private static final int MAX_NESTING_DEPTH = 10;
    private static final double MAX_TEMPO = 999.0;
    private static final double MIN_TEMPO = 20.0;
    
    private final boolean strictMode;
    private final Set<String> supportedPluginFormats;
    
    /**
     * Constructor.
     * 
     * @param strictMode Whether to apply strict validation rules
     * @param supportedPluginFormats Set of supported plugin formats (VST2, VST3, CLAP, etc.)
     */
    public ProjectValidator(final boolean strictMode, final Set<String> supportedPluginFormats)
    {
        this.strictMode = strictMode;
        this.supportedPluginFormats = supportedPluginFormats != null ? 
                                    supportedPluginFormats : new HashSet<>();
    }
    
    @Override
    public List<ValidationResult> validate(final Project project)
    {
        final List<ValidationResult> results = new ArrayList<>();
        
        if (project == null)
        {
            results.add(ValidationResult.error(ErrorCode.MISSING_REQUIRED_DATA, 
                "Project is null", "project", null));
            return results;
        }
        
        // Validate basic project information
        validateBasicInfo(project, results);
        
        // Validate transport settings
        validateTransport(project, results);
        
        // Validate project structure
        validateStructure(project, results);
        
        // Validate tracks and lanes
        validateTracks(project, results);
        
        // Validate timeline and clips
        validateTimeline(project, results);
        
        // Validate devices and plugins
        validateDevices(project, results);
        
        return results;
    }
    
    private void validateBasicInfo(final Project project, final List<ValidationResult> results)
    {
        // Check application info
        if (project.application == null)
        {
            results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                "Missing application information", "application", null,
                "Add application name and version for better compatibility"));
        }
        else
        {
            if (project.application.name == null || project.application.name.trim().isEmpty())
            {
                results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                    "Missing application name", "application.name", null,
                    "Specify the source application name"));
            }
        }
        
        // Check project structure
        if (project.structure == null || project.structure.isEmpty())
        {
            results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                "Project has no track structure", "structure", null,
                "Add tracks to make the project useful"));
        }
    }
    
    private void validateTransport(final Project project, final List<ValidationResult> results)
    {
        if (project.transport == null)
        {
            results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                "Missing transport information", "transport", null,
                "Add tempo and time signature information"));
            return;
        }
        
        // Validate tempo
        if (project.transport.tempo != null && project.transport.tempo.value != null)
        {
            final double tempo = project.transport.tempo.value.doubleValue();
            if (tempo < MIN_TEMPO || tempo > MAX_TEMPO)
            {
                results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                    String.format("Unusual tempo value: %.2f BPM", tempo), 
                    "transport.tempo", tempo,
                    String.format("Typical tempo range is %.0f-%.0f BPM", MIN_TEMPO, MAX_TEMPO)));
            }
        }
        
        // Validate time signature
        if (project.transport.timeSignature != null)
        {
            if (project.transport.timeSignature.numerator != null)
            {
                final int numerator = project.transport.timeSignature.numerator.intValue();
                if (numerator <= 0 || numerator > 32)
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Unusual time signature numerator: " + numerator, 
                        "transport.timeSignature.numerator", numerator,
                        "Typical values are 1-16"));
                }
            }
            
            if (project.transport.timeSignature.denominator != null)
            {
                final int denominator = project.transport.timeSignature.denominator.intValue();
                if (!isPowerOfTwo(denominator) || denominator < 1 || denominator > 32)
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Invalid time signature denominator: " + denominator, 
                        "transport.timeSignature.denominator", denominator,
                        "Must be a power of 2 (1, 2, 4, 8, 16, 32)"));
                }
            }
        }
    }
    
    private void validateStructure(final Project project, final List<ValidationResult> results)
    {
        if (project.structure == null)
            return;
            
        final int trackCount = project.structure.size();
        if (trackCount > MAX_TRACKS)
        {
            results.add(ValidationResult.warning(ErrorCode.PERFORMANCE_WARNING, 
                String.format("Very large number of tracks: %d", trackCount), 
                "structure.size", trackCount,
                "Consider reducing the number of tracks for better performance"));
        }
        
        // Check for duplicate track IDs
        final Set<String> trackIds = new HashSet<>();
        for (final Track track : project.structure)
        {
            if (track.id != null)
            {
                if (trackIds.contains(track.id))
                {
                    results.add(ValidationResult.error(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Duplicate track ID: " + track.id, "track.id", track.id));
                }
                trackIds.add(track.id);
            }
        }
    }
    
    private void validateTracks(final Project project, final List<ValidationResult> results)
    {
        if (project.structure == null)
            return;
            
        for (int i = 0; i < project.structure.size(); i++)
        {
            final Track track = project.structure.get(i);
            validateTrack(track, i, results, 0);
        }
    }
    
    private void validateTrack(final Track track, final int index, 
                             final List<ValidationResult> results, final int depth)
    {
        if (depth > MAX_NESTING_DEPTH)
        {
            results.add(ValidationResult.warning(ErrorCode.PERFORMANCE_WARNING, 
                String.format("Track nesting too deep at track %d (depth: %d)", index, depth), 
                "track.nesting", depth,
                "Consider flattening the track structure"));
            return;
        }
        
        final String trackContext = String.format("track[%d]", index);
        
        // Validate track name
        if (track.name == null || track.name.trim().isEmpty())
        {
            if (this.strictMode)
            {
                results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                    "Track has no name", trackContext + ".name", null,
                    "Add descriptive track names for better organization"));
            }
        }
        
        // Validate channel configuration
        if (track.channel != null)
        {
            if (track.channel.audioChannels != null)
            {
                final int channels = track.channel.audioChannels.intValue();
                if (channels < 0 || channels > 32)
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        String.format("Unusual channel count: %d", channels), 
                        trackContext + ".channel.audioChannels", channels,
                        "Typical values are 0 (MIDI), 1 (mono), 2 (stereo), or multiples of 2"));
                }
            }
        }
        
        // Validate nested tracks
        if (track.tracks != null)
        {
            for (int j = 0; j < track.tracks.size(); j++)
            {
                validateTrack(track.tracks.get(j), j, results, depth + 1);
            }
        }
    }
    
    private void validateTimeline(final Project project, final List<ValidationResult> results)
    {
        if (project.arrangement == null)
        {
            results.add(ValidationResult.info(ErrorCode.MISSING_REQUIRED_DATA, 
                "Project has no arrangement", "Consider adding clips or automation"));
            return;
        }
        
        validateLanes(project.arrangement.lanes, results);
    }
    
    private void validateLanes(final Lanes lanes, final List<ValidationResult> results)
    {
        if (lanes == null || lanes.isEmpty())
            return;
            
        for (int i = 0; i < lanes.size(); i++)
        {
            final Lane lane = lanes.get(i);
            validateLane(lane, i, results);
        }
    }
    
    private void validateLane(final Lane lane, final int index, final List<ValidationResult> results)
    {
        final String laneContext = String.format("lane[%d]", index);
        
        if (lane.clips != null)
        {
            validateClips(lane.clips, laneContext, results);
        }
    }
    
    private void validateClips(final Clips clips, final String context, 
                             final List<ValidationResult> results)
    {
        if (clips.isEmpty())
            return;
            
        if (clips.size() > MAX_CLIPS_PER_TRACK)
        {
            results.add(ValidationResult.warning(ErrorCode.PERFORMANCE_WARNING, 
                String.format("Very large number of clips in %s: %d", context, clips.size()), 
                context + ".clips.size", clips.size(),
                "Consider consolidating clips for better performance"));
        }
        
        for (int i = 0; i < clips.size(); i++)
        {
            final Clip clip = clips.get(i);
            validateClip(clip, String.format("%s.clip[%d]", context, i), results);
        }
    }
    
    private void validateClip(final Clip clip, final String context, 
                            final List<ValidationResult> results)
    {
        // Validate clip timing
        if (clip.time != null)
        {
            if (clip.time.doubleValue() < 0)
            {
                results.add(ValidationResult.error(ErrorCode.INVALID_PROJECT_FORMAT, 
                    "Clip has negative start time", context + ".time", clip.time));
            }
        }
        
        if (clip.duration != null)
        {
            if (clip.duration.doubleValue() <= 0)
            {
                results.add(ValidationResult.error(ErrorCode.INVALID_PROJECT_FORMAT, 
                    "Clip has zero or negative duration", context + ".duration", clip.duration));
            }
        }
        
        // Check for nested clips (potential complexity issue)
        if (clip.clips != null && !clip.clips.isEmpty())
        {
            results.add(ValidationResult.info(ErrorCode.UNSUPPORTED_FEATURE, 
                "Nested clips detected in " + context, 
                "Some DAWs may not support nested clips properly"));
            validateClips(clip.clips, context + ".nested", results);
        }
    }
    
    private void validateDevices(final Project project, final List<ValidationResult> results)
    {
        // This will be expanded when we implement the PluginValidator
        // For now, just basic validation
        if (project.structure != null)
        {
            for (int i = 0; i < project.structure.size(); i++)
            {
                final Track track = project.structure.get(i);
                validateTrackDevices(track, String.format("track[%d]", i), results);
            }
        }
    }
    
    private void validateTrackDevices(final Track track, final String context, 
                                    final List<ValidationResult> results)
    {
        if (track.channel != null && track.channel.devices != null)
        {
            for (int i = 0; i < track.channel.devices.size(); i++)
            {
                final Device device = track.channel.devices.get(i);
                validateDevice(device, String.format("%s.device[%d]", context, i), results);
            }
        }
        
        // Validate nested track devices
        if (track.tracks != null)
        {
            for (int i = 0; i < track.tracks.size(); i++)
            {
                validateTrackDevices(track.tracks.get(i), 
                                   String.format("%s.track[%d]", context, i), results);
            }
        }
    }
    
    private void validateDevice(final Device device, final String context, 
                              final List<ValidationResult> results)
    {
        if (device.deviceID == null || device.deviceID.trim().isEmpty())
        {
            results.add(ValidationResult.warning(ErrorCode.PLUGIN_NOT_FOUND, 
                "Device has no ID", context + ".deviceID", null,
                "Device may not load properly without an ID"));
        }
        
        if (device.deviceName == null || device.deviceName.trim().isEmpty())
        {
            results.add(ValidationResult.warning(ErrorCode.PLUGIN_NOT_FOUND, 
                "Device has no name", context + ".deviceName", null,
                "Add device name for better identification"));
        }
    }
    
    private boolean isPowerOfTwo(final int n)
    {
        return n > 0 && (n & (n - 1)) == 0;
    }
    
    @Override
    public String getName()
    {
        return "Project Validator";
    }
    
    @Override
    public String getDescription()
    {
        return "Validates DAW project structure, content, and potential conversion issues";
    }
    
    /**
     * Create a validator with default settings.
     * 
     * @return Default project validator
     */
    public static ProjectValidator createDefault()
    {
        final Set<String> supportedFormats = Set.of("VST2", "VST3", "CLAP");
        return new ProjectValidator(false, supportedFormats);
    }
    
    /**
     * Create a strict validator.
     * 
     * @return Strict project validator
     */
    public static ProjectValidator createStrict()
    {
        final Set<String> supportedFormats = Set.of("VST2", "VST3", "CLAP");
        return new ProjectValidator(true, supportedFormats);
    }
}
