/* Accessibility Enhancements for ProjectConverter */
/* Enhanced focus indicators, high contrast support, and screen reader optimizations */

/* Enhanced focus indicators */
.enhanced-focus *:focused {
    -fx-effect: dropshadow(gaussian, #0078d4, 4, 0.8, 0, 0);
    -fx-border-color: #0078d4;
    -fx-border-width: 3px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
}

.enhanced-focus .button:focused {
    -fx-effect: dropshadow(gaussian, #0078d4, 6, 1.0, 0, 0);
    -fx-border-width: 4px;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.enhanced-focus .text-field:focused,
.enhanced-focus .combo-box:focused {
    -fx-effect: dropshadow(gaussian, #0078d4, 4, 0.8, 0, 0);
    -fx-border-width: 3px;
    -fx-background-color: rgba(0, 120, 212, 0.1);
}

.enhanced-focus .check-box:focused .box {
    -fx-effect: dropshadow(gaussian, #0078d4, 4, 0.8, 0, 0);
    -fx-border-width: 4px;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

.enhanced-focus .tab:focused {
    -fx-effect: dropshadow(gaussian, #0078d4, 4, 0.8, 0, 0);
    -fx-border-width: 3px;
    -fx-background-color: rgba(0, 120, 212, 0.2);
}

/* Large focus indicators for better visibility */
.large-focus *:focused {
    -fx-effect: dropshadow(gaussian, #0078d4, 8, 1.0, 0, 0);
    -fx-border-width: 5px;
}

.large-focus .button:focused {
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

/* High contrast mode enhancements */
.high-contrast {
    -fx-base: #000000;
    -fx-background: #000000;
    -fx-control-inner-background: #000000;
    -fx-text-fill: #ffffff;
}

.high-contrast .button {
    -fx-background-color: #000000;
    -fx-text-fill: #ffffff;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
    -fx-font-weight: bold;
}

.high-contrast .button:hover {
    -fx-background-color: #ffffff;
    -fx-text-fill: #000000;
}

.high-contrast .button:focused {
    -fx-background-color: #ffff00;
    -fx-text-fill: #000000;
    -fx-border-color: #000000;
    -fx-border-width: 3px;
}

.high-contrast .button:pressed {
    -fx-background-color: #808080;
    -fx-text-fill: #ffffff;
}

.high-contrast .text-field,
.high-contrast .combo-box {
    -fx-background-color: #000000;
    -fx-text-fill: #ffffff;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
    -fx-font-weight: bold;
}

.high-contrast .text-field:focused,
.high-contrast .combo-box:focused {
    -fx-background-color: #ffff00;
    -fx-text-fill: #000000;
    -fx-border-color: #000000;
    -fx-border-width: 3px;
}

.high-contrast .check-box {
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
}

.high-contrast .check-box .box {
    -fx-background-color: #000000;
    -fx-border-color: #ffffff;
    -fx-border-width: 3px;
}

.high-contrast .check-box:selected .box {
    -fx-background-color: #ffffff;
}

.high-contrast .check-box:selected .mark {
    -fx-background-color: #000000;
}

.high-contrast .check-box:focused .box {
    -fx-background-color: #ffff00;
    -fx-border-color: #000000;
}

.high-contrast .tab-pane {
    -fx-background-color: #000000;
}

.high-contrast .tab {
    -fx-background-color: #000000;
    -fx-text-fill: #ffffff;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
    -fx-font-weight: bold;
}

.high-contrast .tab:selected {
    -fx-background-color: #ffffff;
    -fx-text-fill: #000000;
}

.high-contrast .tab:focused {
    -fx-background-color: #ffff00;
    -fx-text-fill: #000000;
    -fx-border-color: #000000;
    -fx-border-width: 3px;
}

.high-contrast .progress-bar {
    -fx-background-color: #000000;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
}

.high-contrast .progress-bar .bar {
    -fx-background-color: #ffffff;
}

.high-contrast .scroll-pane {
    -fx-background-color: #000000;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
}

.high-contrast .scroll-bar {
    -fx-background-color: #000000;
}

.high-contrast .scroll-bar .thumb {
    -fx-background-color: #ffffff;
    -fx-border-color: #000000;
    -fx-border-width: 1px;
}

.high-contrast .text-area {
    -fx-background-color: #000000;
    -fx-text-fill: #ffffff;
    -fx-border-color: #ffffff;
    -fx-border-width: 2px;
    -fx-font-weight: bold;
}

.high-contrast .text-area:focused {
    -fx-background-color: #ffff00;
    -fx-text-fill: #000000;
    -fx-border-color: #000000;
    -fx-border-width: 3px;
}

.high-contrast .label {
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
}

.high-contrast .separator {
    -fx-background-color: #ffffff;
}

.high-contrast .menu-bar {
    -fx-background-color: #000000;
    -fx-border-color: #ffffff;
    -fx-border-width: 0 0 2px 0;
}

.high-contrast .menu-item {
    -fx-background-color: #000000;
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
}

.high-contrast .menu-item:hover {
    -fx-background-color: #ffffff;
    -fx-text-fill: #000000;
}

.high-contrast .menu-item:focused {
    -fx-background-color: #ffff00;
    -fx-text-fill: #000000;
}

/* Screen reader optimizations */
.screen-reader-optimized .button,
.screen-reader-optimized .text-field,
.screen-reader-optimized .combo-box,
.screen-reader-optimized .check-box {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.screen-reader-optimized .label {
    -fx-font-size: 13px;
    -fx-font-weight: normal;
}

/* Touch-friendly sizing for accessibility */
.touch-friendly .button,
.touch-friendly .text-field,
.touch-friendly .combo-box,
.touch-friendly .check-box {
    -fx-min-height: 44px;
    -fx-min-width: 44px;
    -fx-padding: 12px;
}

.touch-friendly .tab {
    -fx-min-height: 48px;
    -fx-padding: 12px 16px;
}

/* Reduced motion for users with vestibular disorders */
.reduced-motion * {
    -fx-transition-duration: 0s;
}

.reduced-motion .button:hover,
.reduced-motion .button:pressed {
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
}

/* Enhanced text contrast */
.enhanced-contrast .label,
.enhanced-contrast .text {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.8), 1, 0, 1, 1);
}

.enhanced-contrast .button {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.5), 2, 0, 0, 1);
}

/* Keyboard navigation indicators */
.keyboard-navigation .button:focused,
.keyboard-navigation .text-field:focused,
.keyboard-navigation .combo-box:focused,
.keyboard-navigation .check-box:focused,
.keyboard-navigation .tab:focused {
    -fx-border-style: dashed;
    -fx-border-width: 2px;
    -fx-border-color: #0078d4;
}

/* Skip links for keyboard navigation */
.skip-link {
    -fx-background-color: #0078d4;
    -fx-text-fill: #ffffff;
    -fx-padding: 8px 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-translate-y: -100px;
    -fx-opacity: 0;
}

.skip-link:focused {
    -fx-translate-y: 0;
    -fx-opacity: 1;
}

/* Error and status announcements */
.error-announcement {
    -fx-background-color: #d32f2f;
    -fx-text-fill: #ffffff;
    -fx-padding: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 4, 0, 0, 2);
}

.warning-announcement {
    -fx-background-color: #f57c00;
    -fx-text-fill: #ffffff;
    -fx-padding: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 4, 0, 0, 2);
}

.success-announcement {
    -fx-background-color: #388e3c;
    -fx-text-fill: #ffffff;
    -fx-padding: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 4, 0, 0, 2);
}

.info-announcement {
    -fx-background-color: #1976d2;
    -fx-text-fill: #ffffff;
    -fx-padding: 12px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 4, 0, 0, 2);
}

/* Live region for screen reader announcements */
.live-region {
    -fx-opacity: 0;
    -fx-pref-width: 1px;
    -fx-pref-height: 1px;
    -fx-max-width: 1px;
    -fx-max-height: 1px;
    -fx-translate-x: -10000px;
}

/* Accessible tooltips */
.accessible-tooltip {
    -fx-background-color: rgba(0, 0, 0, 0.9);
    -fx-text-fill: #ffffff;
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.5), 4, 0, 0, 2);
}
