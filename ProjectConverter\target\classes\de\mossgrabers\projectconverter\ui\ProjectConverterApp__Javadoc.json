{"doc": "\n The project converter application.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "main", "paramTypes": ["java.lang.String[]"], "doc": "\n Main-method.\r\n\r\n @param args The startup arguments\r\n"}, {"name": "initialise", "paramTypes": ["javafx.stage.Stage", "java.util.Optional"], "doc": "{@inheritDoc} "}, {"name": "loadConfiguration", "paramTypes": [], "doc": "\n Load configuration settings.\r\n"}, {"name": "exit", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "execute", "paramTypes": [], "doc": "\n Execute the conversion.\r\n"}, {"name": "cancelExecution", "paramTypes": [], "doc": "\n Cancel button was pressed.\r\n"}, {"name": "isCancelled", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "verifyProjectFiles", "paramTypes": [], "doc": "\n Set and check source and destination project names.\r\n\r\n @return True if OK\r\n"}, {"name": "setDarkMode", "paramTypes": ["boolean"], "doc": "\n Turn the dark mode on or off.\r\n\r\n @param isSelected True to turn on dark mode\r\n"}, {"name": "selectSourceFile", "paramTypes": [], "doc": "\n Show a file selection dialog to select the source project.\r\n"}, {"name": "selectDestinationFolder", "paramTypes": [], "doc": "\n Show a folder selection dialog to select the destination folder for the converted project.\r\n"}, {"name": "log", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "{@inheritDoc} "}, {"name": "logError", "paramTypes": ["java.lang.String", "java.lang.String[]"], "doc": "{@inheritDoc} "}, {"name": "logError", "paramTypes": ["java.lang.String", "java.lang.Throwable"], "doc": "{@inheritDoc} "}, {"name": "logError", "paramTypes": ["java.lang.Throwable"], "doc": "{@inheritDoc} "}, {"name": "updateButtonStates", "paramTypes": ["boolean"], "doc": "{@inheritDoc} "}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Constructor.\r\n\r\n @throws EndApplicationException Startup crash\r\n"}]}