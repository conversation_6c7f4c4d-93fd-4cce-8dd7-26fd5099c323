{"doc": " Exception thrown when validation fails.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getFieldName", "paramTypes": [], "doc": " Get the field name that failed validation.\n \n @return The field name\n"}, {"name": "getInvalidValue", "paramTypes": [], "doc": " Get the invalid value.\n \n @return The invalid value\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " Constructor for validation exception.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param fieldName The name of the field that failed validation\n @param invalidValue The invalid value\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "java.lang.Object", "java.lang.Throwable"], "doc": " Constructor with cause.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param fieldName The name of the field that failed validation\n @param invalidValue The invalid value\n @param cause The underlying cause\n"}]}