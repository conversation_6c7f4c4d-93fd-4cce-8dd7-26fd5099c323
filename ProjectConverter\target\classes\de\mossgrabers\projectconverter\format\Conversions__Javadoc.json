{"doc": "\n Utility functions for converting values.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "dBToValue", "paramTypes": ["double", "double"], "doc": "\n Converts a dB value to linear value.\r\n\r\n @param valueDb The dB value to convert\r\n @param maxLevelDB The maximum possible dB value\r\n @return The linear value\r\n"}, {"name": "valueToDb", "paramTypes": ["double", "double"], "doc": "\n Converts a linear value to a dB value.\r\n\r\n @param linearValue The linear value to convert\r\n @param maxLevelDB The maximum possible dB value\r\n @return The dB value\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Private constructor since this is a utility class.\r\n"}]}