/* Modern UI Components Styling for ProjectConverter */
/* Material Design inspired component styling with animations and modern aesthetics */

/* Modern Button Styles */
.modern-button {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
    -fx-font-size: 14px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
    -fx-transition: all 0.2s ease;
}

.modern-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 4, 0, 0, 2);
}

.modern-button:pressed {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 1, 0, 0, 0);
}

/* Button Styles */
.modern-button.button-primary {
    -fx-background-color: var(--primary-color, #1976d2);
    -fx-text-fill: var(--on-primary, #ffffff);
    -fx-border-color: var(--primary-color, #1976d2);
    -fx-border-width: 0;
}

.modern-button.button-primary:hover {
    -fx-background-color: var(--primary-variant, #1565c0);
    -fx-border-color: var(--primary-variant, #1565c0);
}

.modern-button.button-secondary {
    -fx-background-color: var(--secondary-color, #03dac6);
    -fx-text-fill: var(--on-secondary, #000000);
    -fx-border-color: var(--secondary-color, #03dac6);
    -fx-border-width: 0;
}

.modern-button.button-secondary:hover {
    -fx-background-color: var(--secondary-variant, #018786);
    -fx-text-fill: var(--on-secondary, #ffffff);
}

.modern-button.button-outlined {
    -fx-background-color: transparent;
    -fx-text-fill: var(--primary-color, #1976d2);
    -fx-border-color: var(--primary-color, #1976d2);
    -fx-border-width: 2px;
}

.modern-button.button-outlined:hover {
    -fx-background-color: var(--primary-color, #1976d2);
    -fx-text-fill: var(--on-primary, #ffffff);
}

.modern-button.button-text {
    -fx-background-color: transparent;
    -fx-text-fill: var(--primary-color, #1976d2);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-effect: none;
}

.modern-button.button-text:hover {
    -fx-background-color: var(--hover, rgba(25, 118, 210, 0.04));
}

.modern-button.button-floating {
    -fx-background-color: var(--primary-color, #1976d2);
    -fx-text-fill: var(--on-primary, #ffffff);
    -fx-border-radius: 50%;
    -fx-background-radius: 50%;
    -fx-min-width: 56px;
    -fx-min-height: 56px;
    -fx-max-width: 56px;
    -fx-max-height: 56px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 6, 0, 0, 3);
}

.modern-button.button-floating:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 4);
}

/* Button Sizes */
.modern-button.button-small {
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
    -fx-min-height: 28px;
}

.modern-button.button-medium {
    -fx-padding: 8px 16px;
    -fx-font-size: 14px;
    -fx-min-height: 36px;
}

.modern-button.button-large {
    -fx-padding: 12px 24px;
    -fx-font-size: 16px;
    -fx-min-height: 44px;
}

/* Loading state */
.modern-button.loading {
    -fx-opacity: 0.7;
    -fx-cursor: wait;
}

/* Modern Progress Indicator Styles */
.modern-progress-indicator {
    -fx-alignment: center;
}

.modern-progress-indicator .modern-progress-bar {
    -fx-background-color: var(--surface-variant, #e0e0e0);
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-pref-height: 8px;
}

.modern-progress-indicator .modern-progress-bar .bar {
    -fx-background-color: var(--primary-color, #1976d2);
    -fx-background-radius: 4px;
    -fx-background-insets: 0;
}

.modern-progress-indicator .circular-progress-background {
    -fx-fill: var(--surface-variant, #e0e0e0);
    -fx-stroke: transparent;
}

.modern-progress-indicator .circular-progress-arc {
    -fx-fill: transparent;
    -fx-stroke: var(--primary-color, #1976d2);
    -fx-stroke-width: 6;
    -fx-stroke-line-cap: round;
}

.modern-progress-indicator .progress-label {
    -fx-text-fill: var(--on-surface, #212121);
    -fx-font-size: 14px;
    -fx-font-weight: 500;
}

/* Progress Indicator Styles */
.modern-progress-indicator.progress-linear {
    -fx-pref-width: 200px;
    -fx-pref-height: 8px;
}

.modern-progress-indicator.progress-circular {
    -fx-pref-width: 100px;
    -fx-pref-height: 100px;
}

.modern-progress-indicator.progress-circular_with_text {
    -fx-pref-width: 120px;
    -fx-pref-height: 120px;
}

.modern-progress-indicator.progress-minimal .modern-progress-bar {
    -fx-pref-height: 2px;
    -fx-background-color: transparent;
}

.modern-progress-indicator.progress-minimal .modern-progress-bar .bar {
    -fx-background-color: var(--primary-color, #1976d2);
}

/* Modern Card Styles */
.modern-card {
    -fx-background-color: var(--surface, #ffffff);
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-padding: 16px;
    -fx-spacing: 12px;
}

.modern-card.card-default {
    -fx-border-color: transparent;
    -fx-border-width: 0;
}

.modern-card.card-outlined {
    -fx-border-color: var(--outline, #cccccc);
    -fx-border-width: 1px;
}

.modern-card.card-filled {
    -fx-background-color: var(--surface-variant, #f5f5f5);
}

.modern-card.card-elevated {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 4, 0, 0, 2);
}

/* Card Elevations */
.modern-card.elevation-0 {
    -fx-effect: none;
}

.modern-card.elevation-2 {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
}

.modern-card.elevation-4 {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 4, 0, 0, 2);
}

.modern-card.elevation-8 {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 8, 0, 0, 4);
}

.modern-card.elevation-16 {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.25), 16, 0, 0, 8);
}

/* Interactive card */
.modern-card.interactive {
    -fx-cursor: hand;
}

.modern-card.interactive:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 8, 0, 0, 4);
}

/* Card Components */
.modern-card .card-title {
    -fx-text-fill: var(--on-surface, #212121);
    -fx-font-size: 20px;
    -fx-font-weight: 500;
}

.modern-card .card-subtitle {
    -fx-text-fill: var(--on-surface-variant, #666666);
    -fx-font-size: 14px;
    -fx-font-weight: normal;
}

.modern-card .card-header {
    -fx-spacing: 4px;
}

.modern-card .card-content {
    -fx-spacing: 8px;
}

.modern-card .card-actions {
    -fx-spacing: 8px;
    -fx-alignment: center-right;
}

/* Animations */
@keyframes ripple {
    0% {
        -fx-scale-x: 0;
        -fx-scale-y: 0;
        -fx-opacity: 1;
    }
    100% {
        -fx-scale-x: 1;
        -fx-scale-y: 1;
        -fx-opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        -fx-opacity: 0;
        -fx-translate-y: 20px;
    }
    to {
        -fx-opacity: 1;
        -fx-translate-y: 0;
    }
}

@keyframes slideIn {
    from {
        -fx-translate-x: -100%;
    }
    to {
        -fx-translate-x: 0;
    }
}

/* Animation classes */
.fade-in {
    -fx-animation: fadeIn 0.3s ease-out;
}

.slide-in {
    -fx-animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
.breakpoint-extra_small .modern-button,
.breakpoint-small .modern-button {
    -fx-min-height: 44px;
    -fx-padding: 12px 16px;
    -fx-font-size: 16px;
}

.breakpoint-extra_small .modern-card,
.breakpoint-small .modern-card {
    -fx-padding: 12px;
    -fx-spacing: 8px;
}

.breakpoint-extra_small .modern-card .card-title,
.breakpoint-small .modern-card .card-title {
    -fx-font-size: 18px;
}

/* Dark theme adjustments */
.root.dark .modern-button.button-outlined {
    -fx-text-fill: var(--primary-color, #90caf9);
    -fx-border-color: var(--primary-color, #90caf9);
}

.root.dark .modern-button.button-text {
    -fx-text-fill: var(--primary-color, #90caf9);
}

.root.dark .modern-card {
    -fx-background-color: var(--surface, #1e1e1e);
}

.root.dark .modern-card.card-filled {
    -fx-background-color: var(--surface-variant, #2d2d30);
}

/* High contrast adjustments */
.high-contrast .modern-button {
    -fx-border-width: 2px;
    -fx-font-weight: bold;
}

.high-contrast .modern-card {
    -fx-border-width: 2px;
    -fx-border-color: var(--outline, #ffffff);
}

/* Reduced motion */
.reduced-motion .modern-button,
.reduced-motion .modern-card,
.reduced-motion .modern-progress-indicator {
    -fx-transition: none;
}

.reduced-motion .modern-button:hover {
    -fx-scale-x: 1;
    -fx-scale-y: 1;
}

/* Focus indicators */
.modern-button:focused,
.modern-card:focused {
    -fx-border-color: var(--primary-color, #1976d2);
    -fx-border-width: 3px;
    -fx-effect: dropshadow(gaussian, var(--primary-color, #1976d2), 4, 0.5, 0, 0);
}

/* Disabled states */
.modern-button:disabled {
    -fx-opacity: 0.6;
    -fx-cursor: default;
}

.modern-button:disabled:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
}
