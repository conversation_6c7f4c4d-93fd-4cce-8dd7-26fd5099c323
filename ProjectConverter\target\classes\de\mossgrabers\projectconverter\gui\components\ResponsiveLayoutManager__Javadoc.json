{"doc": " Responsive layout manager that adapts layouts based on screen size and breakpoints.\n Provides flexible grid systems and automatic layout switching for different screen sizes.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "setupDefaultConfigurations", "paramTypes": [], "doc": " Set up default layout configurations for each breakpoint.\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings and listeners.\n"}, {"name": "setManagedContainer", "paramTypes": ["javafx.scene.layout.Pane"], "doc": " Set the managed container that will be responsive.\n \n @param container The container to manage\n"}, {"name": "widthProperty", "paramTypes": [], "doc": " Get the width property.\n \n @return The width property\n"}, {"name": "heightProperty", "paramTypes": [], "doc": " Get the height property.\n \n @return The height property\n"}, {"name": "getCurrentBreakpoint", "paramTypes": [], "doc": " Get the current breakpoint.\n \n @return The current breakpoint\n"}, {"name": "getCurrentLayoutConfig", "paramTypes": [], "doc": " Get the layout configuration for the current breakpoint.\n \n @return The current layout configuration\n"}, {"name": "setLayoutConfig", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.LayoutConfig"], "doc": " Set a custom layout configuration for a breakpoint.\n \n @param config The layout configuration\n"}, {"name": "addBreakpointChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add a breakpoint change listener.\n \n @param listener The listener to add\n"}, {"name": "removeBreakpointChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove a breakpoint change listener.\n \n @param listener The listener to remove\n"}, {"name": "createResponsiveGrid", "paramTypes": ["java.util.List"], "doc": " Create a responsive grid layout.\n \n @param nodes The nodes to arrange in the grid\n @return A responsive grid pane\n"}, {"name": "createResponsiveSplitPane", "paramTypes": ["javafx.scene.Node", "javafx.scene.Node"], "doc": " Create a responsive split pane.\n \n @param leftNode The left/top node\n @param rightNode The right/bottom node\n @return A responsive split pane\n"}, {"name": "createResponsiveContainer", "paramTypes": ["java.util.List"], "doc": " Create a responsive container that switches between horizontal and vertical layouts.\n \n @param nodes The nodes to arrange\n @return A responsive container\n"}, {"name": "shouldShowSidebar", "paramTypes": [], "doc": " Check if the sidebar should be shown for the current breakpoint.\n \n @return True if sidebar should be shown\n"}, {"name": "getCurrentSpacing", "paramTypes": [], "doc": " Get the recommended spacing for the current breakpoint.\n \n @return The spacing value\n"}, {"name": "updateBreakpoint", "paramTypes": ["double"], "doc": " Update the current breakpoint based on width.\n \n @param width The current width\n"}, {"name": "applyCurrentLayout", "paramTypes": [], "doc": " Apply the current layout configuration.\n"}, {"name": "arrangeNodesInGrid", "paramTypes": ["javafx.scene.layout.GridPane", "java.util.List", "de.mossgrabers.projectconverter.gui.components.ResponsiveLayoutManager.LayoutConfig"], "doc": " Arrange nodes in a grid based on configuration.\n \n @param grid The grid pane\n @param nodes The nodes to arrange\n @param config The layout configuration\n"}, {"name": "makeGridResponsive", "paramTypes": ["javafx.scene.layout.GridPane"], "doc": " Make a grid responsive by setting up column constraints.\n \n @param grid The grid to make responsive\n"}, {"name": "notifyBreakpointChangeListeners", "paramTypes": [], "doc": " Notify all breakpoint change listeners.\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}]}