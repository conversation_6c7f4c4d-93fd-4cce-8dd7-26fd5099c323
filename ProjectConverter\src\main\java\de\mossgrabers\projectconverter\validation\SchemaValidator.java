package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import javax.xml.XMLConstants;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

/**
 * Validator for DAWproject XML schema compliance.
 * Validates XML structure against the DAWproject schema definition.
 * 
 * <AUTHOR> ProjectConverter
 */
public class SchemaValidator implements Validator<String>
{
    private static final Logger LOGGER = LoggerFactory.getLogger(SchemaValidator.class);
    
    private final Schema schema;
    private final boolean strictMode;
    
    /**
     * Constructor with schema file.
     * 
     * @param schemaFile The XSD schema file
     * @param strictMode Whether to treat warnings as errors
     * @throws ValidationException If schema cannot be loaded
     */
    public SchemaValidator(final File schemaFile, final boolean strictMode) throws ValidationException
    {
        this.strictMode = strictMode;
        this.schema = loadSchema(schemaFile);
    }
    
    /**
     * Constructor with schema from classpath resource.
     * 
     * @param schemaResourcePath The path to schema resource
     * @param strictMode Whether to treat warnings as errors
     * @throws ValidationException If schema cannot be loaded
     */
    public SchemaValidator(final String schemaResourcePath, final boolean strictMode) throws ValidationException
    {
        this.strictMode = strictMode;
        this.schema = loadSchemaFromResource(schemaResourcePath);
    }
    
    /**
     * Constructor with pre-loaded schema.
     * 
     * @param schema The pre-loaded schema
     * @param strictMode Whether to treat warnings as errors
     */
    public SchemaValidator(final Schema schema, final boolean strictMode)
    {
        this.schema = schema;
        this.strictMode = strictMode;
    }
    
    @Override
    public List<ValidationResult> validate(final String xmlContent)
    {
        final List<ValidationResult> results = new ArrayList<>();
        
        if (xmlContent == null || xmlContent.trim().isEmpty())
        {
            results.add(ValidationResult.error(ErrorCode.MISSING_REQUIRED_DATA, 
                "XML content is null or empty", "xmlContent", xmlContent));
            return results;
        }
        
        if (this.schema == null)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Schema is not available", "schema", null));
            return results;
        }
        
        try
        {
            final javax.xml.validation.Validator validator = this.schema.newValidator();
            final ValidationErrorHandler errorHandler = new ValidationErrorHandler(this.strictMode);
            validator.setErrorHandler(errorHandler);
            
            // Validate the XML content
            validator.validate(new StreamSource(new StringReader(xmlContent)));
            
            // Collect validation results from error handler
            results.addAll(errorHandler.getValidationResults());
            
            if (results.isEmpty())
            {
                LOGGER.debug("XML validation successful");
            }
            else
            {
                LOGGER.debug("XML validation completed with {} issues", results.size());
            }
        }
        catch (final SAXException e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "XML parsing error: " + e.getMessage(), "xmlStructure", xmlContent));
            LOGGER.error("XML validation failed with SAX exception", e);
        }
        catch (final IOException e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "I/O error during validation: " + e.getMessage(), "xmlContent", xmlContent));
            LOGGER.error("XML validation failed with I/O exception", e);
        }
        catch (final Exception e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Unexpected error during validation: " + e.getMessage(), "validation", xmlContent));
            LOGGER.error("XML validation failed with unexpected exception", e);
        }
        
        return results;
    }
    
    /**
     * Validate an XML file.
     * 
     * @param xmlFile The XML file to validate
     * @return List of validation results
     */
    public List<ValidationResult> validateFile(final File xmlFile)
    {
        final List<ValidationResult> results = new ArrayList<>();
        
        if (xmlFile == null || !xmlFile.exists())
        {
            results.add(ValidationResult.error(ErrorCode.FILE_NOT_FOUND, 
                "XML file not found", "xmlFile", xmlFile));
            return results;
        }
        
        if (this.schema == null)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Schema is not available", "schema", null));
            return results;
        }
        
        try
        {
            final javax.xml.validation.Validator validator = this.schema.newValidator();
            final ValidationErrorHandler errorHandler = new ValidationErrorHandler(this.strictMode);
            validator.setErrorHandler(errorHandler);
            
            // Validate the XML file
            validator.validate(new StreamSource(xmlFile));
            
            // Collect validation results from error handler
            results.addAll(errorHandler.getValidationResults());
            
            if (results.isEmpty())
            {
                LOGGER.debug("XML file validation successful: {}", xmlFile.getName());
            }
            else
            {
                LOGGER.debug("XML file validation completed with {} issues: {}", 
                           results.size(), xmlFile.getName());
            }
        }
        catch (final SAXException e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "XML parsing error in file " + xmlFile.getName() + ": " + e.getMessage(), 
                "xmlFile", xmlFile));
            LOGGER.error("XML file validation failed with SAX exception: {}", xmlFile.getName(), e);
        }
        catch (final IOException e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "I/O error reading file " + xmlFile.getName() + ": " + e.getMessage(), 
                "xmlFile", xmlFile));
            LOGGER.error("XML file validation failed with I/O exception: {}", xmlFile.getName(), e);
        }
        catch (final Exception e)
        {
            results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Unexpected error validating file " + xmlFile.getName() + ": " + e.getMessage(), 
                "validation", xmlFile));
            LOGGER.error("XML file validation failed with unexpected exception: {}", xmlFile.getName(), e);
        }
        
        return results;
    }
    
    private Schema loadSchema(final File schemaFile) throws ValidationException
    {
        if (schemaFile == null || !schemaFile.exists())
        {
            throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Schema file not found", "schemaFile", schemaFile);
        }
        
        try
        {
            final SchemaFactory factory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            return factory.newSchema(schemaFile);
        }
        catch (final SAXException e)
        {
            throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Failed to load schema from file: " + e.getMessage(), 
                "schemaFile", schemaFile, e);
        }
    }
    
    private Schema loadSchemaFromResource(final String resourcePath) throws ValidationException
    {
        if (resourcePath == null || resourcePath.trim().isEmpty())
        {
            throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Schema resource path is null or empty", "resourcePath", resourcePath);
        }
        
        try (final InputStream schemaStream = getClass().getClassLoader().getResourceAsStream(resourcePath))
        {
            if (schemaStream == null)
            {
                throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                    "Schema resource not found: " + resourcePath, "resourcePath", resourcePath);
            }
            
            final SchemaFactory factory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            return factory.newSchema(new StreamSource(schemaStream));
        }
        catch (final SAXException e)
        {
            throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "Failed to load schema from resource: " + e.getMessage(), 
                "resourcePath", resourcePath, e);
        }
        catch (final IOException e)
        {
            throw new ValidationException(ErrorCode.SCHEMA_VALIDATION_FAILED, 
                "I/O error loading schema resource: " + e.getMessage(), 
                "resourcePath", resourcePath, e);
        }
    }
    
    @Override
    public String getName()
    {
        return "Schema Validator";
    }
    
    @Override
    public String getDescription()
    {
        return "Validates XML content against DAWproject schema definition";
    }
    
    /**
     * Custom error handler for XML validation.
     */
    private static class ValidationErrorHandler implements ErrorHandler
    {
        private final List<ValidationResult> results = new ArrayList<>();
        private final boolean strictMode;
        
        public ValidationErrorHandler(final boolean strictMode)
        {
            this.strictMode = strictMode;
        }
        
        @Override
        public void warning(final SAXParseException exception) throws SAXException
        {
            final ValidationSeverity severity = this.strictMode ? ValidationSeverity.ERROR : ValidationSeverity.WARNING;
            final ErrorCode errorCode = this.strictMode ? ErrorCode.SCHEMA_VALIDATION_FAILED : ErrorCode.UNSUPPORTED_VERSION;
            
            this.results.add(new ValidationResult(severity, errorCode,
                String.format("Line %d, Column %d: %s", 
                            exception.getLineNumber(), exception.getColumnNumber(), exception.getMessage()),
                "xmlValidation", null, 
                this.strictMode ? null : "This is a warning and may not prevent conversion"));
        }
        
        @Override
        public void error(final SAXParseException exception) throws SAXException
        {
            this.results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED,
                String.format("Line %d, Column %d: %s", 
                            exception.getLineNumber(), exception.getColumnNumber(), exception.getMessage()),
                "xmlValidation", null));
        }
        
        @Override
        public void fatalError(final SAXParseException exception) throws SAXException
        {
            this.results.add(ValidationResult.error(ErrorCode.SCHEMA_VALIDATION_FAILED,
                String.format("FATAL - Line %d, Column %d: %s", 
                            exception.getLineNumber(), exception.getColumnNumber(), exception.getMessage()),
                "xmlValidation", null));
            throw exception; // Re-throw fatal errors
        }
        
        public List<ValidationResult> getValidationResults()
        {
            return new ArrayList<>(this.results);
        }
    }
    
    /**
     * Create a default schema validator for DAWproject.
     * 
     * @return Default schema validator
     * @throws ValidationException If default schema cannot be loaded
     */
    public static SchemaValidator createDefault() throws ValidationException
    {
        // Try to load the DAWproject schema from resources
        return new SchemaValidator("dawproject.xsd", false);
    }
    
    /**
     * Create a strict schema validator for DAWproject.
     * 
     * @return Strict schema validator
     * @throws ValidationException If default schema cannot be loaded
     */
    public static SchemaValidator createStrict() throws ValidationException
    {
        return new SchemaValidator("dawproject.xsd", true);
    }
}
