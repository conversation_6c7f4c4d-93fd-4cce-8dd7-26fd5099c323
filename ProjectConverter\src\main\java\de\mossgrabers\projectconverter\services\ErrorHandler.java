package de.mossgrabers.projectconverter.services;

import de.mossgrabers.projectconverter.INotifier;
import de.mossgrabers.projectconverter.core.ErrorCode;
import de.mossgrabers.projectconverter.core.ErrorSeverity;
import de.mossgrabers.projectconverter.core.ProjectConverterException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Central error handling service with recovery strategies.
 * Provides consistent error handling, logging, and user notification.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ErrorHandler
{
    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorHandler.class);
    
    private final INotifier notifier;
    private final List<ErrorRecord> errorHistory;
    private final List<Consumer<ProjectConverterException>> errorListeners;
    
    /**
     * Constructor.
     * 
     * @param notifier The notifier for user feedback
     */
    public ErrorHandler(final INotifier notifier)
    {
        this.notifier = notifier;
        this.errorHistory = new ArrayList<>();
        this.errorListeners = new ArrayList<>();
    }
    
    /**
     * Handle an exception with automatic recovery attempt.
     * 
     * @param exception The exception to handle
     * @return True if recovery was successful
     */
    public boolean handleException(final ProjectConverterException exception)
    {
        // Log the error
        logError(exception);
        
        // Record the error
        recordError(exception);
        
        // Notify listeners
        notifyListeners(exception);
        
        // Attempt recovery if possible
        if (exception.isRecoverable())
        {
            return attemptRecovery(exception);
        }
        
        // Notify user of unrecoverable error
        notifyUser(exception);
        return false;
    }
    
    /**
     * Handle a generic throwable by wrapping it in a ProjectConverterException.
     * 
     * @param throwable The throwable to handle
     * @param context Additional context information
     * @return True if recovery was successful
     */
    public boolean handleThrowable(final Throwable throwable, final String context)
    {
        final ErrorCode errorCode = determineErrorCode(throwable);
        final String userMessage = createUserMessage(throwable, context);
        final String technicalDetails = String.format("Context: %s, Exception: %s", 
                                                     context, throwable.getMessage());
        
        final ProjectConverterException exception = new ProjectConverterException(
            errorCode, userMessage, technicalDetails, isRecoverable(throwable), throwable);
        
        return handleException(exception);
    }
    
    /**
     * Add an error listener.
     * 
     * @param listener The listener to add
     */
    public void addErrorListener(final Consumer<ProjectConverterException> listener)
    {
        this.errorListeners.add(listener);
    }
    
    /**
     * Remove an error listener.
     * 
     * @param listener The listener to remove
     */
    public void removeErrorListener(final Consumer<ProjectConverterException> listener)
    {
        this.errorListeners.remove(listener);
    }
    
    /**
     * Get the error history.
     * 
     * @return List of error records
     */
    public List<ErrorRecord> getErrorHistory()
    {
        return new ArrayList<>(this.errorHistory);
    }
    
    /**
     * Clear the error history.
     */
    public void clearErrorHistory()
    {
        this.errorHistory.clear();
    }
    
    private void logError(final ProjectConverterException exception)
    {
        switch (exception.getErrorCode().getSeverity())
        {
            case INFO:
                LOGGER.info(exception.getFormattedMessage(), exception);
                break;
            case WARNING:
                LOGGER.warn(exception.getFormattedMessage(), exception);
                break;
            case ERROR:
                LOGGER.error(exception.getFormattedMessage(), exception);
                break;
            case CRITICAL:
                LOGGER.error("CRITICAL: " + exception.getFormattedMessage(), exception);
                break;
        }
    }
    
    private void recordError(final ProjectConverterException exception)
    {
        final ErrorRecord record = new ErrorRecord(
            System.currentTimeMillis(),
            exception.getErrorCode(),
            exception.getUserMessage(),
            exception.getTechnicalDetails(),
            exception.isRecoverable()
        );
        
        this.errorHistory.add(record);
        
        // Keep only the last 1000 errors to prevent memory issues
        if (this.errorHistory.size() > 1000)
        {
            this.errorHistory.remove(0);
        }
    }
    
    private void notifyListeners(final ProjectConverterException exception)
    {
        for (final Consumer<ProjectConverterException> listener : this.errorListeners)
        {
            try
            {
                listener.accept(exception);
            }
            catch (final Exception e)
            {
                LOGGER.warn("Error listener threw exception", e);
            }
        }
    }
    
    private boolean attemptRecovery(final ProjectConverterException exception)
    {
        // Implement recovery strategies based on error code
        return switch (exception.getErrorCode())
        {
            case FILE_NOT_FOUND -> false; // Cannot recover from missing files
            case INSUFFICIENT_DISK_SPACE -> suggestCleanup();
            case OUT_OF_MEMORY -> attemptMemoryRecovery();
            case INVALID_CONFIGURATION -> resetToDefaults();
            default -> false;
        };
    }
    
    private void notifyUser(final ProjectConverterException exception)
    {
        if (exception.getErrorCode().getSeverity() == ErrorSeverity.ERROR ||
            exception.getErrorCode().getSeverity() == ErrorSeverity.CRITICAL)
        {
            this.notifier.logError(exception.getUserMessage());
        }
        else
        {
            this.notifier.log(exception.getUserMessage());
        }
    }
    
    private ErrorCode determineErrorCode(final Throwable throwable)
    {
        // Determine appropriate error code based on exception type
        if (throwable instanceof java.io.FileNotFoundException)
            return ErrorCode.FILE_NOT_FOUND;
        if (throwable instanceof java.io.IOException)
            return ErrorCode.FILE_ACCESS_DENIED;
        if (throwable instanceof OutOfMemoryError)
            return ErrorCode.OUT_OF_MEMORY;
        if (throwable instanceof IllegalArgumentException)
            return ErrorCode.INVALID_CONFIGURATION;
        
        return ErrorCode.UNKNOWN_ERROR;
    }
    
    private String createUserMessage(final Throwable throwable, final String context)
    {
        final String baseMessage = throwable.getMessage() != null ? 
                                 throwable.getMessage() : throwable.getClass().getSimpleName();
        return String.format("An error occurred %s: %s", context, baseMessage);
    }
    
    private boolean isRecoverable(final Throwable throwable)
    {
        // Determine if the error is potentially recoverable
        return !(throwable instanceof OutOfMemoryError ||
                throwable instanceof java.io.FileNotFoundException ||
                throwable instanceof SecurityException);
    }
    
    private boolean suggestCleanup()
    {
        this.notifier.log("Insufficient disk space. Please free up disk space and try again.");
        return false;
    }
    
    private boolean attemptMemoryRecovery()
    {
        System.gc(); // Suggest garbage collection
        this.notifier.log("Memory issue detected. Attempting to free memory...");
        return true; // Optimistic recovery
    }
    
    private boolean resetToDefaults()
    {
        this.notifier.log("Configuration issue detected. Resetting to default settings...");
        return true;
    }
}
