{"doc": " Record of an error occurrence for tracking and analysis.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getTimestamp", "paramTypes": [], "doc": " Get the timestamp.\n \n @return The timestamp\n"}, {"name": "getDateTime", "paramTypes": [], "doc": " Get the timestamp as LocalDateTime.\n \n @return The timestamp as LocalDateTime\n"}, {"name": "getErrorCode", "paramTypes": [], "doc": " Get the error code.\n \n @return The error code\n"}, {"name": "getUserMessage", "paramTypes": [], "doc": " Get the user message.\n \n @return The user message\n"}, {"name": "getTechnicalDetails", "paramTypes": [], "doc": " Get the technical details.\n \n @return The technical details\n"}, {"name": "isRecoverable", "paramTypes": [], "doc": " Check if the error was recoverable.\n \n @return True if recoverable\n"}], "constructors": [{"name": "<init>", "paramTypes": ["long", "de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "boolean"], "doc": " Constructor.\n \n @param timestamp The timestamp when the error occurred\n @param errorCode The error code\n @param userMessage The user-friendly message\n @param technicalDetails Technical details\n @param recoverable Whether the error was recoverable\n"}]}