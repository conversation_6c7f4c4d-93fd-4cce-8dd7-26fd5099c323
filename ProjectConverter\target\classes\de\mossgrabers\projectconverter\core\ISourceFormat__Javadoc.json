{"doc": "\n The interface to a project source.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "read", "paramTypes": ["java.io.File"], "doc": "\n Read and convert the source file project into a dawproject.\r\n\r\n @param sourceFile The source project to load\r\n @return The read, parsed and converted dawproject\r\n @throws IOException Could not read the file\r\n @throws ParseException Could not parse Reaper project file\r\n"}, {"name": "getExtensionFilter", "paramTypes": [], "doc": "\n Get the extension filter to use for selecting matching files.\r\n\r\n @return The extension filter\r\n"}], "constructors": []}