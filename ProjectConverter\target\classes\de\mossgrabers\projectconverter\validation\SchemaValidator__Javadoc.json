{"doc": " Validator for DAWproject XML schema compliance.\n Validates XML structure against the DAWproject schema definition.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateFile", "paramTypes": ["java.io.File"], "doc": " Validate an XML file.\n \n @param xmlFile The XML file to validate\n @return List of validation results\n"}, {"name": "createDefault", "paramTypes": [], "doc": " Create a default schema validator for DAWproject.\n \n @return Default schema validator\n @throws ValidationException If default schema cannot be loaded\n"}, {"name": "createStrict", "paramTypes": [], "doc": " Create a strict schema validator for DAWproject.\n \n @return Strict schema validator\n @throws ValidationException If default schema cannot be loaded\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.io.File", "boolean"], "doc": " Constructor with schema file.\n \n @param schemaFile The XSD schema file\n @param strictMode Whether to treat warnings as errors\n @throws ValidationException If schema cannot be loaded\n"}, {"name": "<init>", "paramTypes": ["java.lang.String", "boolean"], "doc": " Constructor with schema from classpath resource.\n \n @param schemaResourcePath The path to schema resource\n @param strictMode Whether to treat warnings as errors\n @throws ValidationException If schema cannot be loaded\n"}, {"name": "<init>", "paramTypes": ["javax.xml.validation.Schema", "boolean"], "doc": " Constructor with pre-loaded schema.\n \n @param schema The pre-loaded schema\n @param strictMode Whether to treat warnings as errors\n"}]}