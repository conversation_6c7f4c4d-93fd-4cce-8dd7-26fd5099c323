{"doc": "\n Base class for creator and detector classes.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getName", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "getEditPane", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "loadSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "saveSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "shutdown", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "cancel", "paramTypes": [], "doc": "{@inheritDoc} "}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String", "de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param name The name of the object.\r\n @param notifier The notifier\r\n"}]}