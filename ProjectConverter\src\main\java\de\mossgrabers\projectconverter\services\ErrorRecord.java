package de.mossgrabers.projectconverter.services;

import de.mossgrabers.projectconverter.core.ErrorCode;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * Record of an error occurrence for tracking and analysis.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ErrorRecord
{
    private final long timestamp;
    private final ErrorCode errorCode;
    private final String userMessage;
    private final String technicalDetails;
    private final boolean recoverable;
    
    /**
     * Constructor.
     * 
     * @param timestamp The timestamp when the error occurred
     * @param errorCode The error code
     * @param userMessage The user-friendly message
     * @param technicalDetails Technical details
     * @param recoverable Whether the error was recoverable
     */
    public ErrorRecord(final long timestamp, final ErrorCode errorCode, 
                      final String userMessage, final String technicalDetails, 
                      final boolean recoverable)
    {
        this.timestamp = timestamp;
        this.errorCode = errorCode;
        this.userMessage = userMessage;
        this.technicalDetails = technicalDetails;
        this.recoverable = recoverable;
    }
    
    /**
     * Get the timestamp.
     * 
     * @return The timestamp
     */
    public long getTimestamp()
    {
        return this.timestamp;
    }
    
    /**
     * Get the timestamp as LocalDateTime.
     * 
     * @return The timestamp as LocalDateTime
     */
    public LocalDateTime getDateTime()
    {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(this.timestamp), ZoneId.systemDefault());
    }
    
    /**
     * Get the error code.
     * 
     * @return The error code
     */
    public ErrorCode getErrorCode()
    {
        return this.errorCode;
    }
    
    /**
     * Get the user message.
     * 
     * @return The user message
     */
    public String getUserMessage()
    {
        return this.userMessage;
    }
    
    /**
     * Get the technical details.
     * 
     * @return The technical details
     */
    public String getTechnicalDetails()
    {
        return this.technicalDetails;
    }
    
    /**
     * Check if the error was recoverable.
     * 
     * @return True if recoverable
     */
    public boolean isRecoverable()
    {
        return this.recoverable;
    }
    
    @Override
    public String toString()
    {
        return String.format("[%s] %s: %s (Recoverable: %s)", 
                           getDateTime(), this.errorCode, this.userMessage, this.recoverable);
    }
}
