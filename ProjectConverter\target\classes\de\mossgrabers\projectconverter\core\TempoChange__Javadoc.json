{"doc": "\n Helper class to convert between seconds and beats on a tempo timeline.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getTime", "paramTypes": [], "doc": "\n Get the position of the tempo change.\r\n \r\n @return The position\r\n"}, {"name": "getTempo", "paramTypes": [], "doc": "\n Get the tempo starting at the position.\r\n \r\n @return The tempo\r\n"}, {"name": "isLinear", "paramTypes": [], "doc": "\n Should the tempo change happen immediately or linear till the next tempo event?\r\n \r\n @return True if linear change should happen\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["double", "double", "boolean"], "doc": "\n Constructor.\r\n \r\n @param time The position of the tempo change event in seconds\r\n @param tempo The new tempo starting at this position\r\n @param isLinear If true, a linear change till the next tempo event will happen\r\n"}]}