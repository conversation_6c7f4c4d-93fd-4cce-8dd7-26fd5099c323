package de.mossgrabers.projectconverter.gui.models;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Base class for all view models in the MVVM architecture.
 * Provides common functionality for property binding and change notifications.
 * 
 * <AUTHOR> ProjectConverter
 */
public abstract class BaseViewModel
{
    private final List<Consumer<String>> propertyChangeListeners = new ArrayList<>();
    private final BooleanProperty busyProperty = new SimpleBooleanProperty(false);
    private final StringProperty statusMessageProperty = new SimpleStringProperty("");
    
    /**
     * Get the busy property indicating if the view model is performing operations.
     * 
     * @return The busy property
     */
    public BooleanProperty busyProperty()
    {
        return this.busyProperty;
    }
    
    /**
     * Check if the view model is busy.
     * 
     * @return True if busy
     */
    public boolean isBusy()
    {
        return this.busyProperty.get();
    }
    
    /**
     * Set the busy state.
     * 
     * @param busy True if busy
     */
    public void setBusy(final boolean busy)
    {
        this.busyProperty.set(busy);
    }
    
    /**
     * Get the status message property.
     * 
     * @return The status message property
     */
    public StringProperty statusMessageProperty()
    {
        return this.statusMessageProperty;
    }
    
    /**
     * Get the current status message.
     * 
     * @return The status message
     */
    public String getStatusMessage()
    {
        return this.statusMessageProperty.get();
    }
    
    /**
     * Set the status message.
     * 
     * @param message The status message
     */
    public void setStatusMessage(final String message)
    {
        this.statusMessageProperty.set(message);
    }
    
    /**
     * Add a property change listener.
     * 
     * @param listener The listener to add
     */
    public void addPropertyChangeListener(final Consumer<String> listener)
    {
        this.propertyChangeListeners.add(listener);
    }
    
    /**
     * Remove a property change listener.
     * 
     * @param listener The listener to remove
     */
    public void removePropertyChangeListener(final Consumer<String> listener)
    {
        this.propertyChangeListeners.remove(listener);
    }
    
    /**
     * Notify all listeners of a property change.
     * 
     * @param propertyName The name of the changed property
     */
    protected void notifyPropertyChanged(final String propertyName)
    {
        for (final Consumer<String> listener : this.propertyChangeListeners)
        {
            try
            {
                listener.accept(propertyName);
            }
            catch (final Exception e)
            {
                // Log error but don't fail the notification
                System.err.println("Error in property change listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Initialize the view model.
     * Called after construction to set up initial state.
     */
    public void initialize()
    {
        // Override in subclasses for initialization logic
    }
    
    /**
     * Cleanup resources when the view model is no longer needed.
     */
    public void cleanup()
    {
        this.propertyChangeListeners.clear();
    }
    
    /**
     * Validate the current state of the view model.
     * 
     * @return True if the state is valid
     */
    public boolean isValid()
    {
        // Override in subclasses for validation logic
        return true;
    }
    
    /**
     * Get validation errors for the current state.
     * 
     * @return List of validation error messages
     */
    public List<String> getValidationErrors()
    {
        // Override in subclasses to provide specific validation errors
        return new ArrayList<>();
    }
    
    /**
     * Reset the view model to its initial state.
     */
    public void reset()
    {
        setBusy(false);
        setStatusMessage("");
        // Override in subclasses for additional reset logic
    }
}
