{"doc": " Memory monitoring service for tracking memory usage and detecting potential issues.\n Provides real-time memory monitoring and alerts for memory pressure.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "startMonitoring", "paramTypes": [], "doc": " Start memory monitoring.\n"}, {"name": "stopMonitoring", "paramTypes": [], "doc": " Stop memory monitoring.\n"}, {"name": "shutdown", "paramTypes": [], "doc": " Shutdown the memory monitor and cleanup resources.\n"}, {"name": "getCurrentStatus", "paramTypes": [], "doc": " Get current memory status.\n \n @return Current memory status\n"}, {"name": "addListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add a memory status listener.\n \n @param listener The listener to add\n"}, {"name": "removeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove a memory status listener.\n \n @param listener The listener to remove\n"}, {"name": "forceGarbageCollection", "paramTypes": [], "doc": " Force garbage collection and return memory status.\n \n @return Memory status after garbage collection\n"}, {"name": "isMemoryUsageHigh", "paramTypes": [], "doc": " Check if memory usage is above warning threshold.\n \n @return True if memory usage is high\n"}, {"name": "isMemoryUsageCritical", "paramTypes": [], "doc": " Check if memory usage is critical.\n \n @return True if memory usage is critical\n"}, {"name": "getMemoryStatistics", "paramTypes": [], "doc": " Get memory usage statistics as a formatted string.\n \n @return Formatted memory statistics\n"}, {"name": "setMonitoringInterval", "paramTypes": ["long"], "doc": " Set the monitoring interval.\n \n @param intervalMs Monitoring interval in milliseconds\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor with default monitoring interval.\n"}, {"name": "<init>", "paramTypes": ["long"], "doc": " Constructor with custom monitoring interval.\n \n @param monitoringInterval Monitoring interval in milliseconds\n"}]}