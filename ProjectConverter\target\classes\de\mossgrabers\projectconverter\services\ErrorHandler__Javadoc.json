{"doc": " Central error handling service with recovery strategies.\n Provides consistent error handling, logging, and user notification.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleException", "paramTypes": ["de.mossgrabers.projectconverter.core.ProjectConverterException"], "doc": " Handle an exception with automatic recovery attempt.\n \n @param exception The exception to handle\n @return True if recovery was successful\n"}, {"name": "handleThrowable", "paramTypes": ["java.lang.Throwable", "java.lang.String"], "doc": " Handle a generic throwable by wrapping it in a ProjectConverterException.\n \n @param throwable The throwable to handle\n @param context Additional context information\n @return True if recovery was successful\n"}, {"name": "addErrorListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add an error listener.\n \n @param listener The listener to add\n"}, {"name": "removeErrorListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove an error listener.\n \n @param listener The listener to remove\n"}, {"name": "getErrorHistory", "paramTypes": [], "doc": " Get the error history.\n \n @return List of error records\n"}, {"name": "clearErrorHistory", "paramTypes": [], "doc": " Clear the error history.\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": " Constructor.\n \n @param notifier The notifier for user feedback\n"}]}