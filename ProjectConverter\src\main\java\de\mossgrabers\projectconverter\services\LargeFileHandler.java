package de.mossgrabers.projectconverter.services;

import de.mossgrabers.projectconverter.INotifier;
import de.mossgrabers.projectconverter.core.ErrorCode;
import de.mossgrabers.projectconverter.core.ProjectConverterException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

/**
 * Handler for processing large files with chunked processing capabilities.
 * Provides memory-efficient processing of large project files by breaking them into manageable chunks.
 * 
 * <AUTHOR> ProjectConverter
 */
public class LargeFileHandler
{
    private static final Logger LOGGER = LoggerFactory.getLogger(LargeFileHandler.class);
    
    private static final long DEFAULT_CHUNK_SIZE = 1000; // lines per chunk
    private static final long LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB
    private static final int DEFAULT_THREAD_POOL_SIZE = 2;
    
    private final INotifier notifier;
    private final MemoryMonitor memoryMonitor;
    private final ExecutorService executorService;
    private final long chunkSize;
    private final boolean parallelProcessing;
    
    /**
     * Constructor with default settings.
     * 
     * @param notifier The notifier for progress updates
     */
    public LargeFileHandler(final INotifier notifier)
    {
        this(notifier, null, DEFAULT_CHUNK_SIZE, false);
    }
    
    /**
     * Constructor with custom settings.
     * 
     * @param notifier The notifier for progress updates
     * @param memoryMonitor Optional memory monitor
     * @param chunkSize Number of lines per chunk
     * @param parallelProcessing Whether to enable parallel processing
     */
    public LargeFileHandler(final INotifier notifier, final MemoryMonitor memoryMonitor, 
                          final long chunkSize, final boolean parallelProcessing)
    {
        this.notifier = notifier;
        this.memoryMonitor = memoryMonitor;
        this.chunkSize = chunkSize > 0 ? chunkSize : DEFAULT_CHUNK_SIZE;
        this.parallelProcessing = parallelProcessing;
        
        if (parallelProcessing)
        {
            this.executorService = Executors.newFixedThreadPool(DEFAULT_THREAD_POOL_SIZE,
                r -> {
                    final Thread t = new Thread(r, "LargeFileProcessor");
                    t.setDaemon(true);
                    return t;
                });
        }
        else
        {
            this.executorService = null;
        }
    }
    
    /**
     * Process a large file with chunked processing.
     * 
     * @param <T> The type of result from processing
     * @param file The file to process
     * @param chunkProcessor Function to process each chunk
     * @param resultCombiner Function to combine chunk results
     * @return The combined result
     * @throws ProjectConverterException If processing fails
     */
    public <T> T processFile(final File file, 
                           final Function<FileChunk, T> chunkProcessor,
                           final Function<List<T>, T> resultCombiner) throws ProjectConverterException
    {
        if (file == null)
        {
            throw new ProjectConverterException(ErrorCode.FILE_NOT_FOUND, 
                "File is null", "File parameter cannot be null", false);
        }
        
        if (!shouldUseChunkedProcessing(file))
        {
            // Process as single chunk for small files
            return processSingleChunk(file, chunkProcessor);
        }
        
        this.notifier.log("Processing large file with chunked approach: " + file.getName());
        
        final List<FileChunk> chunks = createChunks(file);
        final List<T> chunkResults = new ArrayList<>();
        final AtomicLong processedChunks = new AtomicLong(0);
        
        if (this.parallelProcessing && this.executorService != null)
        {
            // Parallel processing
            final List<CompletableFuture<T>> futures = new ArrayList<>();
            
            for (final FileChunk chunk : chunks)
            {
                final CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
                    try
                    {
                        checkMemoryPressure();
                        final T result = chunkProcessor.apply(chunk);
                        updateProgress(processedChunks.incrementAndGet(), chunks.size());
                        return result;
                    }
                    catch (final Exception e)
                    {
                        throw new RuntimeException("Error processing chunk " + chunk.getChunkNumber(), e);
                    }
                }, this.executorService);
                
                futures.add(future);
            }
            
            // Collect results
            for (final CompletableFuture<T> future : futures)
            {
                try
                {
                    chunkResults.add(future.get());
                }
                catch (final Exception e)
                {
                    throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                        "Error in parallel chunk processing", 
                        "File: " + file.getAbsolutePath(), false, e);
                }
            }
        }
        else
        {
            // Sequential processing
            for (final FileChunk chunk : chunks)
            {
                try
                {
                    checkMemoryPressure();
                    final T result = chunkProcessor.apply(chunk);
                    chunkResults.add(result);
                    updateProgress(processedChunks.incrementAndGet(), chunks.size());
                }
                catch (final Exception e)
                {
                    throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                        "Error processing chunk " + chunk.getChunkNumber(), 
                        "File: " + file.getAbsolutePath(), false, e);
                }
            }
        }
        
        // Combine results
        try
        {
            final T finalResult = resultCombiner.apply(chunkResults);
            this.notifier.log("Large file processing completed: " + file.getName());
            return finalResult;
        }
        catch (final Exception e)
        {
            throw new ProjectConverterException(ErrorCode.INTERNAL_ERROR, 
                "Error combining chunk results", 
                "File: " + file.getAbsolutePath(), false, e);
        }
    }
    
    /**
     * Process a file as lines with chunked processing.
     * 
     * @param file The file to process
     * @param lineProcessor Function to process lines in each chunk
     * @return List of results from each chunk
     * @throws ProjectConverterException If processing fails
     */
    public List<String> processFileAsLines(final File file, 
                                         final Function<List<String>, String> lineProcessor) 
            throws ProjectConverterException
    {
        return processFile(file, 
            chunk -> {
                try (final StreamingFileReader reader = new StreamingFileReader(chunk.getFile()))
                {
                    final List<String> lines = new ArrayList<>();
                    reader.skipLines(chunk.getStartLine());
                    
                    for (long i = 0; i < chunk.getLineCount() && !reader.isClosed(); i++)
                    {
                        final String line = reader.readLine();
                        if (line == null) break;
                        lines.add(line);
                    }
                    
                    return lineProcessor.apply(lines);
                }
                catch (final Exception e)
                {
                    throw new RuntimeException("Error processing chunk lines", e);
                }
            },
            results -> results
        );
    }
    
    /**
     * Shutdown the large file handler and cleanup resources.
     */
    public void shutdown()
    {
        if (this.executorService != null)
        {
            this.executorService.shutdown();
            LOGGER.info("Large file handler shutdown complete");
        }
    }
    
    private boolean shouldUseChunkedProcessing(final File file)
    {
        return file.length() > LARGE_FILE_THRESHOLD;
    }
    
    private <T> T processSingleChunk(final File file, final Function<FileChunk, T> chunkProcessor) 
            throws ProjectConverterException
    {
        final FileChunk singleChunk = new FileChunk(file, 0, 0, -1); // -1 means entire file
        return chunkProcessor.apply(singleChunk);
    }
    
    private List<FileChunk> createChunks(final File file) throws ProjectConverterException
    {
        final List<FileChunk> chunks = new ArrayList<>();
        
        // Estimate number of lines (rough approximation)
        final long estimatedLines = estimateLineCount(file);
        final long numChunks = (estimatedLines + this.chunkSize - 1) / this.chunkSize;
        
        for (long i = 0; i < numChunks; i++)
        {
            final long startLine = i * this.chunkSize;
            final long lineCount = Math.min(this.chunkSize, estimatedLines - startLine);
            chunks.add(new FileChunk(file, (int) i, startLine, lineCount));
        }
        
        LOGGER.debug("Created {} chunks for file: {} (estimated {} lines)", 
                    chunks.size(), file.getName(), estimatedLines);
        
        return chunks;
    }
    
    private long estimateLineCount(final File file)
    {
        // Rough estimation: assume average line length of 80 characters
        final long fileSize = file.length();
        return Math.max(1, fileSize / 80);
    }
    
    private void checkMemoryPressure() throws ProjectConverterException
    {
        if (this.memoryMonitor != null && this.memoryMonitor.isMemoryUsageCritical())
        {
            // Force garbage collection
            this.memoryMonitor.forceGarbageCollection();
            
            // Check again after GC
            if (this.memoryMonitor.isMemoryUsageCritical())
            {
                throw new ProjectConverterException(ErrorCode.OUT_OF_MEMORY, 
                    "Critical memory usage detected during large file processing", 
                    "Consider increasing heap size or processing smaller files", true);
            }
        }
    }
    
    private void updateProgress(final long processedChunks, final long totalChunks)
    {
        final double progress = (double) processedChunks / totalChunks;
        this.notifier.log(String.format("Processing progress: %.1f%% (%d/%d chunks)", 
                                       progress * 100, processedChunks, totalChunks));
    }
    
    /**
     * Represents a chunk of a file for processing.
     */
    public static class FileChunk
    {
        private final File file;
        private final int chunkNumber;
        private final long startLine;
        private final long lineCount;
        
        public FileChunk(final File file, final int chunkNumber, final long startLine, final long lineCount)
        {
            this.file = file;
            this.chunkNumber = chunkNumber;
            this.startLine = startLine;
            this.lineCount = lineCount;
        }
        
        public File getFile() { return this.file; }
        public int getChunkNumber() { return this.chunkNumber; }
        public long getStartLine() { return this.startLine; }
        public long getLineCount() { return this.lineCount; }
        
        public boolean isEntireFile()
        {
            return this.lineCount == -1;
        }
        
        @Override
        public String toString()
        {
            return String.format("Chunk %d: lines %d-%d (%d lines)", 
                               this.chunkNumber, this.startLine, 
                               this.startLine + this.lineCount - 1, this.lineCount);
        }
    }
    
    /**
     * Create a large file handler with default settings.
     * 
     * @param notifier The notifier for progress updates
     * @return Large file handler
     */
    public static LargeFileHandler createDefault(final INotifier notifier)
    {
        return new LargeFileHandler(notifier);
    }
    
    /**
     * Create a large file handler with memory monitoring.
     * 
     * @param notifier The notifier for progress updates
     * @param memoryMonitor The memory monitor
     * @return Large file handler with memory monitoring
     */
    public static LargeFileHandler createWithMemoryMonitoring(final INotifier notifier, 
                                                             final MemoryMonitor memoryMonitor)
    {
        return new LargeFileHandler(notifier, memoryMonitor, DEFAULT_CHUNK_SIZE, false);
    }
    
    /**
     * Create a large file handler with parallel processing.
     * 
     * @param notifier The notifier for progress updates
     * @param memoryMonitor The memory monitor
     * @return Large file handler with parallel processing
     */
    public static LargeFileHandler createParallel(final INotifier notifier, 
                                                 final MemoryMonitor memoryMonitor)
    {
        return new LargeFileHandler(notifier, memoryMonitor, DEFAULT_CHUNK_SIZE, true);
    }
}
