package de.mossgrabers.projectconverter.integration;

import de.mossgrabers.projectconverter.INotifier;
import de.mossgrabers.projectconverter.core.ConversionTask;
import de.mossgrabers.projectconverter.format.dawproject.DawProjectCreator;
import de.mossgrabers.projectconverter.format.dawproject.DawProjectDetector;
import de.mossgrabers.projectconverter.format.reaper.ReaperCreator;
import de.mossgrabers.projectconverter.format.reaper.ReaperDetector;
import de.mossgrabers.projectconverter.testing.TestDataGenerator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Integration tests for the complete conversion process.
 * Tests end-to-end conversion scenarios between different formats.
 * 
 * <AUTHOR> ProjectConverter
 */
class ConversionIntegrationTest
{
    @TempDir
    Path tempDir;
    
    @Mock
    private INotifier mockNotifier;
    
    private ReaperDetector reaperDetector;
    private DawProjectDetector dawProjectDetector;
    private ReaperCreator reaperCreator;
    private DawProjectCreator dawProjectCreator;
    
    private final List<String> logMessages = new ArrayList<>();
    private final List<String> errorMessages = new ArrayList<>();
    
    @BeforeEach
    void setUp()
    {
        MockitoAnnotations.openMocks(this);
        
        // Setup mock notifier to capture messages
        doAnswer(invocation -> {
            this.logMessages.add(invocation.getArgument(0));
            return null;
        }).when(this.mockNotifier).log(anyString());
        
        doAnswer(invocation -> {
            this.errorMessages.add(invocation.getArgument(0));
            return null;
        }).when(this.mockNotifier).logError(anyString());
        
        // Initialize format handlers
        this.reaperDetector = new ReaperDetector(this.mockNotifier);
        this.dawProjectDetector = new DawProjectDetector(this.mockNotifier);
        this.reaperCreator = new ReaperCreator(this.mockNotifier);
        this.dawProjectCreator = new DawProjectCreator(this.mockNotifier);
    }
    
    @Test
    void testReaperToDawProjectConversion() throws Exception
    {
        // Given
        final File reaperFile = TestDataGenerator.generateReaperProjectFile(this.tempDir);
        final File outputDir = this.tempDir.resolve("output").toFile();
        outputDir.mkdirs();
        
        // When
        final ConversionTask task = new ConversionTask(
            reaperFile, outputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Successful conversion should return null");
        
        // Verify output file was created
        final File[] outputFiles = outputDir.listFiles((dir, name) -> name.endsWith(".dawproject"));
        assertNotNull(outputFiles);
        assertTrue(outputFiles.length > 0, "DAWproject file should be created");
        
        // Verify conversion messages
        assertTrue(this.logMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_WRITING_FILE")));
        assertTrue(this.logMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_CONVERSION_FINISHED")));
        
        // Should have no error messages for successful conversion
        assertTrue(this.errorMessages.isEmpty(), "Should have no error messages: " + this.errorMessages);
    }
    
    @Test
    void testDawProjectToReaperConversion() throws Exception
    {
        // Given - First create a DAWproject file
        final File dawProjectFile = this.tempDir.resolve("test.dawproject").toFile();
        final File tempOutputDir = this.tempDir.resolve("temp_output").toFile();
        tempOutputDir.mkdirs();
        
        // Create a simple DAWproject file using the creator
        try (final var dawProject = new com.bitwig.dawproject.DawProjectContainer("test", null))
        {
            dawProject.getProject().application = new com.bitwig.dawproject.Application();
            dawProject.getProject().application.name = "Test DAW";
            dawProject.getProject().application.version = "1.0.0";
            
            this.dawProjectCreator.write(dawProject, tempOutputDir);
        }
        
        // Find the created DAWproject file
        final File[] dawFiles = tempOutputDir.listFiles((dir, name) -> name.endsWith(".dawproject"));
        assertNotNull(dawFiles);
        assertTrue(dawFiles.length > 0);
        final File createdDawFile = dawFiles[0];
        
        // Now convert back to Reaper
        final File outputDir = this.tempDir.resolve("reaper_output").toFile();
        outputDir.mkdirs();
        
        // When
        final ConversionTask task = new ConversionTask(
            createdDawFile, outputDir, this.dawProjectDetector, this.reaperCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Successful conversion should return null");
        
        // Verify output file was created
        final File[] outputFiles = outputDir.listFiles((dir, name) -> name.endsWith(".rpp"));
        assertNotNull(outputFiles);
        assertTrue(outputFiles.length > 0, "Reaper project file should be created");
        
        // Verify conversion completed
        assertTrue(this.logMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_CONVERSION_FINISHED")));
    }
    
    @Test
    void testConversionWithInvalidSourceFile() throws Exception
    {
        // Given
        final File invalidFile = this.tempDir.resolve("invalid.txt").toFile();
        invalidFile.createNewFile(); // Create empty file with wrong extension
        
        final File outputDir = this.tempDir.resolve("output").toFile();
        outputDir.mkdirs();
        
        // When
        final ConversionTask task = new ConversionTask(
            invalidFile, outputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Failed conversion should return null");
        
        // Verify error was logged
        assertFalse(this.errorMessages.isEmpty(), "Should have error messages for invalid file");
        assertTrue(this.errorMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_COULD_NOT_READ")));
    }
    
    @Test
    void testConversionWithNonExistentSourceFile() throws Exception
    {
        // Given
        final File nonExistentFile = this.tempDir.resolve("nonexistent.rpp").toFile();
        final File outputDir = this.tempDir.resolve("output").toFile();
        outputDir.mkdirs();
        
        // When
        final ConversionTask task = new ConversionTask(
            nonExistentFile, outputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Failed conversion should return null");
        
        // Verify error was logged
        assertFalse(this.errorMessages.isEmpty(), "Should have error messages for non-existent file");
    }
    
    @Test
    void testConversionWithInvalidOutputDirectory() throws Exception
    {
        // Given
        final File reaperFile = TestDataGenerator.generateReaperProjectFile(this.tempDir);
        final File invalidOutputDir = this.tempDir.resolve("nonexistent/nested/path").toFile();
        // Don't create the directory
        
        // When
        final ConversionTask task = new ConversionTask(
            reaperFile, invalidOutputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Failed conversion should return null");
        
        // Verify error was logged
        assertFalse(this.errorMessages.isEmpty(), "Should have error messages for invalid output directory");
    }
    
    @Test
    void testConversionCancellation() throws Exception
    {
        // Given
        final File reaperFile = TestDataGenerator.generateReaperProjectFile(this.tempDir);
        final File outputDir = this.tempDir.resolve("output").toFile();
        outputDir.mkdirs();
        
        // Mock notifier to simulate cancellation
        when(this.mockNotifier.isCancelled()).thenReturn(true);
        
        // When
        final ConversionTask task = new ConversionTask(
            reaperFile, outputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        final Object result = task.call();
        
        // Then
        assertNull(result, "Cancelled conversion should return null");
        
        // Verify cancellation was logged
        assertTrue(this.logMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_CANCELED")));
    }
    
    @Test
    void testRoundTripConversion() throws Exception
    {
        // Given
        final File originalReaperFile = TestDataGenerator.generateReaperProjectFile(this.tempDir);
        
        // Step 1: Convert Reaper to DAWproject
        final File dawOutputDir = this.tempDir.resolve("daw_output").toFile();
        dawOutputDir.mkdirs();
        
        final ConversionTask reaperToDaw = new ConversionTask(
            originalReaperFile, dawOutputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        reaperToDaw.call();
        
        // Find the created DAWproject file
        final File[] dawFiles = dawOutputDir.listFiles((dir, name) -> name.endsWith(".dawproject"));
        assertNotNull(dawFiles);
        assertTrue(dawFiles.length > 0);
        final File dawFile = dawFiles[0];
        
        // Step 2: Convert DAWproject back to Reaper
        final File reaperOutputDir = this.tempDir.resolve("reaper_output").toFile();
        reaperOutputDir.mkdirs();
        
        final ConversionTask dawToReaper = new ConversionTask(
            dawFile, reaperOutputDir, this.dawProjectDetector, this.reaperCreator, this.mockNotifier);
        
        final Object result = dawToReaper.call();
        
        // Then
        assertNull(result, "Round-trip conversion should succeed");
        
        // Verify final output file was created
        final File[] finalFiles = reaperOutputDir.listFiles((dir, name) -> name.endsWith(".rpp"));
        assertNotNull(finalFiles);
        assertTrue(finalFiles.length > 0, "Final Reaper project file should be created");
        
        // Verify both conversions completed successfully
        final long conversionFinishedCount = this.logMessages.stream()
            .filter(msg -> msg.contains("NOTIFY_CONVERSION_FINISHED"))
            .count();
        assertEquals(2, conversionFinishedCount, "Both conversions should complete successfully");
    }
    
    @Test
    void testConversionWithValidation() throws Exception
    {
        // Given
        final File reaperFile = TestDataGenerator.generateReaperProjectFile(this.tempDir);
        final File outputDir = this.tempDir.resolve("output").toFile();
        outputDir.mkdirs();
        
        // When
        final ConversionTask task = new ConversionTask(
            reaperFile, outputDir, this.reaperDetector, this.dawProjectCreator, this.mockNotifier);
        
        task.call();
        
        // Then
        // Verify validation was attempted
        assertTrue(this.logMessages.stream().anyMatch(msg -> msg.contains("NOTIFY_VALIDATING_FILE")));
    }
}
