{"doc": "\n The data of a CLAP chunk in the Reaper project file.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "chunkToFile", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.OutputStream"], "doc": "{@inheritDoc} "}, {"name": "fileToChunk", "paramTypes": ["java.io.InputStream", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "{@inheritDoc} "}], "constructors": []}