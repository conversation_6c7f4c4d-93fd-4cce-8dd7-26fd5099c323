/* Responsive Design Utilities for ProjectConverter */
/* Breakpoint-based styling and adaptive layouts */

/* Breakpoint-specific styles */
.breakpoint-extra_small {
    -fx-padding: 8px;
    -fx-spacing: 8px;
}

.breakpoint-small {
    -fx-padding: 12px;
    -fx-spacing: 12px;
}

.breakpoint-medium {
    -fx-padding: 16px;
    -fx-spacing: 16px;
}

.breakpoint-large {
    -fx-padding: 20px;
    -fx-spacing: 20px;
}

.breakpoint-extra_large {
    -fx-padding: 24px;
    -fx-spacing: 24px;
}

/* Responsive grid system */
.responsive-grid {
    -fx-alignment: center;
}

.responsive-grid.breakpoint-extra_small {
    -fx-hgap: 8px;
    -fx-vgap: 8px;
}

.responsive-grid.breakpoint-small {
    -fx-hgap: 12px;
    -fx-vgap: 12px;
}

.responsive-grid.breakpoint-medium {
    -fx-hgap: 16px;
    -fx-vgap: 16px;
}

.responsive-grid.breakpoint-large {
    -fx-hgap: 20px;
    -fx-vgap: 20px;
}

.responsive-grid.breakpoint-extra_large {
    -fx-hgap: 24px;
    -fx-vgap: 24px;
}

/* Responsive containers */
.responsive-container {
    -fx-alignment: center;
}

.responsive-container.breakpoint-extra_small,
.responsive-container.breakpoint-small {
    -fx-orientation: vertical;
    -fx-spacing: 12px;
}

.responsive-container.breakpoint-medium,
.responsive-container.breakpoint-large,
.responsive-container.breakpoint-extra_large {
    -fx-orientation: horizontal;
    -fx-spacing: 16px;
}

/* Responsive split panes */
.responsive-split-pane.breakpoint-extra_small .split-pane-divider,
.responsive-split-pane.breakpoint-small .split-pane-divider {
    -fx-padding: 2px;
}

.responsive-split-pane.breakpoint-medium .split-pane-divider,
.responsive-split-pane.breakpoint-large .split-pane-divider,
.responsive-split-pane.breakpoint-extra_large .split-pane-divider {
    -fx-padding: 4px;
}

/* Responsive buttons */
.responsive-button {
    -fx-cursor: hand;
}

.responsive-button.breakpoint-extra_small {
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-min-height: 44px; /* Touch-friendly size */
}

.responsive-button.breakpoint-small {
    -fx-padding: 10px 14px;
    -fx-font-size: 13px;
    -fx-min-height: 40px;
}

.responsive-button.breakpoint-medium,
.responsive-button.breakpoint-large,
.responsive-button.breakpoint-extra_large {
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
    -fx-min-height: 32px;
}

/* Responsive text fields */
.responsive-text-field.breakpoint-extra_small {
    -fx-padding: 12px;
    -fx-font-size: 16px; /* Prevent zoom on mobile */
    -fx-min-height: 44px;
}

.responsive-text-field.breakpoint-small {
    -fx-padding: 10px;
    -fx-font-size: 14px;
    -fx-min-height: 40px;
}

.responsive-text-field.breakpoint-medium,
.responsive-text-field.breakpoint-large,
.responsive-text-field.breakpoint-extra_large {
    -fx-padding: 8px;
    -fx-font-size: 13px;
    -fx-min-height: 32px;
}

/* Responsive tabs */
.responsive-tab-pane .tab.breakpoint-extra_small,
.responsive-tab-pane .tab.breakpoint-small {
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-min-height: 44px;
}

.responsive-tab-pane .tab.breakpoint-medium,
.responsive-tab-pane .tab.breakpoint-large,
.responsive-tab-pane .tab.breakpoint-extra_large {
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
    -fx-min-height: 32px;
}

/* Responsive cards */
.responsive-card {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 4, 0, 0, 2);
}

.responsive-card.breakpoint-extra_small {
    -fx-padding: 16px;
    -fx-spacing: 12px;
}

.responsive-card.breakpoint-small {
    -fx-padding: 18px;
    -fx-spacing: 14px;
}

.responsive-card.breakpoint-medium {
    -fx-padding: 20px;
    -fx-spacing: 16px;
}

.responsive-card.breakpoint-large,
.responsive-card.breakpoint-extra_large {
    -fx-padding: 24px;
    -fx-spacing: 20px;
}

/* Responsive typography */
.responsive-title {
    -fx-font-weight: bold;
}

.responsive-title.breakpoint-extra_small {
    -fx-font-size: 20px;
}

.responsive-title.breakpoint-small {
    -fx-font-size: 22px;
}

.responsive-title.breakpoint-medium {
    -fx-font-size: 24px;
}

.responsive-title.breakpoint-large,
.responsive-title.breakpoint-extra_large {
    -fx-font-size: 28px;
}

.responsive-subtitle {
    -fx-font-weight: normal;
}

.responsive-subtitle.breakpoint-extra_small {
    -fx-font-size: 14px;
}

.responsive-subtitle.breakpoint-small {
    -fx-font-size: 15px;
}

.responsive-subtitle.breakpoint-medium,
.responsive-subtitle.breakpoint-large,
.responsive-subtitle.breakpoint-extra_large {
    -fx-font-size: 16px;
}

/* Responsive sidebar */
.responsive-sidebar {
    -fx-background-color: var(--surface, #f5f5f5);
    -fx-border-color: var(--outline, #cccccc);
}

.responsive-sidebar.breakpoint-extra_small,
.responsive-sidebar.breakpoint-small {
    -fx-pref-width: 0;
    -fx-min-width: 0;
    -fx-max-width: 0;
    -fx-opacity: 0;
    -fx-border-width: 0;
}

.responsive-sidebar.breakpoint-medium {
    -fx-pref-width: 200px;
    -fx-min-width: 150px;
    -fx-max-width: 250px;
    -fx-opacity: 1;
    -fx-border-width: 0 1px 0 0;
}

.responsive-sidebar.breakpoint-large,
.responsive-sidebar.breakpoint-extra_large {
    -fx-pref-width: 250px;
    -fx-min-width: 200px;
    -fx-max-width: 300px;
    -fx-opacity: 1;
    -fx-border-width: 0 1px 0 0;
}

/* Responsive main content */
.responsive-main-content {
    -fx-padding: 16px;
}

.responsive-main-content.breakpoint-extra_small {
    -fx-padding: 12px;
}

.responsive-main-content.breakpoint-small {
    -fx-padding: 14px;
}

.responsive-main-content.breakpoint-medium {
    -fx-padding: 16px;
}

.responsive-main-content.breakpoint-large,
.responsive-main-content.breakpoint-extra_large {
    -fx-padding: 20px;
}

/* Responsive progress indicators */
.responsive-progress-bar.breakpoint-extra_small {
    -fx-pref-height: 8px;
}

.responsive-progress-bar.breakpoint-small {
    -fx-pref-height: 6px;
}

.responsive-progress-bar.breakpoint-medium,
.responsive-progress-bar.breakpoint-large,
.responsive-progress-bar.breakpoint-extra_large {
    -fx-pref-height: 4px;
}

/* Responsive scroll bars */
.responsive-scroll-bar.breakpoint-extra_small .thumb {
    -fx-pref-width: 12px;
}

.responsive-scroll-bar.breakpoint-small .thumb {
    -fx-pref-width: 10px;
}

.responsive-scroll-bar.breakpoint-medium,
.responsive-scroll-bar.breakpoint-large,
.responsive-scroll-bar.breakpoint-extra_large .thumb {
    -fx-pref-width: 8px;
}

/* Touch-friendly styles for small screens */
.breakpoint-extra_small .button,
.breakpoint-extra_small .text-field,
.breakpoint-extra_small .combo-box,
.breakpoint-extra_small .check-box {
    -fx-min-height: 44px; /* iOS/Android touch target minimum */
}

.breakpoint-small .button,
.breakpoint-small .text-field,
.breakpoint-small .combo-box,
.breakpoint-small .check-box {
    -fx-min-height: 40px;
}

/* Responsive focus indicators */
.breakpoint-extra_small *:focused,
.breakpoint-small *:focused {
    -fx-effect: dropshadow(gaussian, var(--primary-color, #1976d2), 4, 0.8, 0, 0);
}

.breakpoint-medium *:focused,
.breakpoint-large *:focused,
.breakpoint-extra_large *:focused {
    -fx-effect: dropshadow(gaussian, var(--primary-color, #1976d2), 2, 0.5, 0, 0);
}

/* Responsive animations - reduced on smaller screens for performance */
.breakpoint-extra_small,
.breakpoint-small {
    -fx-transition-duration: 0.1s;
}

.breakpoint-medium,
.breakpoint-large,
.breakpoint-extra_large {
    -fx-transition-duration: 0.2s;
}

/* Utility classes for responsive visibility */
.hide-on-mobile {
    -fx-opacity: 1;
    -fx-pref-width: -1;
    -fx-pref-height: -1;
}

.breakpoint-extra_small .hide-on-mobile,
.breakpoint-small .hide-on-mobile {
    -fx-opacity: 0;
    -fx-pref-width: 0;
    -fx-pref-height: 0;
    -fx-min-width: 0;
    -fx-min-height: 0;
    -fx-max-width: 0;
    -fx-max-height: 0;
}

.show-on-mobile {
    -fx-opacity: 0;
    -fx-pref-width: 0;
    -fx-pref-height: 0;
    -fx-min-width: 0;
    -fx-min-height: 0;
    -fx-max-width: 0;
    -fx-max-height: 0;
}

.breakpoint-extra_small .show-on-mobile,
.breakpoint-small .show-on-mobile {
    -fx-opacity: 1;
    -fx-pref-width: -1;
    -fx-pref-height: -1;
    -fx-min-width: -1;
    -fx-min-height: -1;
    -fx-max-width: -1;
    -fx-max-height: -1;
}
