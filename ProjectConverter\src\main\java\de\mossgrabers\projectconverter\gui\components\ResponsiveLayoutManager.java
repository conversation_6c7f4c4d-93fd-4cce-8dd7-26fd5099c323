package de.mossgrabers.projectconverter.gui.components;

import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.geometry.Orientation;
import javafx.scene.Node;
import javafx.scene.control.SplitPane;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Responsive layout manager that adapts layouts based on screen size and breakpoints.
 * Provides flexible grid systems and automatic layout switching for different screen sizes.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ResponsiveLayoutManager
{
    /**
     * Breakpoint definitions for responsive design.
     */
    public enum Breakpoint
    {
        EXTRA_SMALL(0, 576),    // Mobile phones
        SMALL(576, 768),        // Small tablets
        MEDIUM(768, 992),       // Tablets
        LARGE(992, 1200),       // Desktops
        EXTRA_LARGE(1200, Double.MAX_VALUE); // Large desktops
        
        private final double minWidth;
        private final double maxWidth;
        
        Breakpoint(final double minWidth, final double maxWidth)
        {
            this.minWidth = minWidth;
            this.maxWidth = maxWidth;
        }
        
        public double getMinWidth() { return this.minWidth; }
        public double getMaxWidth() { return this.maxWidth; }
        
        public boolean matches(final double width)
        {
            return width >= this.minWidth && width < this.maxWidth;
        }
    }
    
    /**
     * Layout configuration for different breakpoints.
     */
    public static class LayoutConfig
    {
        private final Breakpoint breakpoint;
        private final int columns;
        private final Orientation orientation;
        private final boolean showSidebar;
        private final double spacing;
        
        public LayoutConfig(final Breakpoint breakpoint, final int columns, 
                          final Orientation orientation, final boolean showSidebar, 
                          final double spacing)
        {
            this.breakpoint = breakpoint;
            this.columns = columns;
            this.orientation = orientation;
            this.showSidebar = showSidebar;
            this.spacing = spacing;
        }
        
        public Breakpoint getBreakpoint() { return this.breakpoint; }
        public int getColumns() { return this.columns; }
        public Orientation getOrientation() { return this.orientation; }
        public boolean isShowSidebar() { return this.showSidebar; }
        public double getSpacing() { return this.spacing; }
    }
    
    private final DoubleProperty widthProperty = new SimpleDoubleProperty();
    private final DoubleProperty heightProperty = new SimpleDoubleProperty();
    private final Map<Breakpoint, LayoutConfig> layoutConfigs = new HashMap<>();
    private final List<Consumer<Breakpoint>> breakpointChangeListeners = new ArrayList<>();
    
    private Breakpoint currentBreakpoint = Breakpoint.LARGE;
    private Pane managedContainer;
    
    /**
     * Constructor.
     */
    public ResponsiveLayoutManager()
    {
        setupDefaultConfigurations();
        setupPropertyBindings();
    }
    
    /**
     * Set up default layout configurations for each breakpoint.
     */
    private void setupDefaultConfigurations()
    {
        // Extra small screens (mobile) - single column, vertical layout
        this.layoutConfigs.put(Breakpoint.EXTRA_SMALL, 
            new LayoutConfig(Breakpoint.EXTRA_SMALL, 1, Orientation.VERTICAL, false, 8));
        
        // Small screens (small tablets) - single column, vertical layout
        this.layoutConfigs.put(Breakpoint.SMALL, 
            new LayoutConfig(Breakpoint.SMALL, 1, Orientation.VERTICAL, false, 12));
        
        // Medium screens (tablets) - two columns, vertical layout
        this.layoutConfigs.put(Breakpoint.MEDIUM, 
            new LayoutConfig(Breakpoint.MEDIUM, 2, Orientation.VERTICAL, true, 16));
        
        // Large screens (desktops) - three columns, horizontal layout
        this.layoutConfigs.put(Breakpoint.LARGE, 
            new LayoutConfig(Breakpoint.LARGE, 3, Orientation.HORIZONTAL, true, 20));
        
        // Extra large screens - three columns, horizontal layout with more spacing
        this.layoutConfigs.put(Breakpoint.EXTRA_LARGE, 
            new LayoutConfig(Breakpoint.EXTRA_LARGE, 3, Orientation.HORIZONTAL, true, 24));
    }
    
    /**
     * Set up property bindings and listeners.
     */
    private void setupPropertyBindings()
    {
        this.widthProperty.addListener((obs, oldWidth, newWidth) -> {
            updateBreakpoint(newWidth.doubleValue());
        });
    }
    
    /**
     * Set the managed container that will be responsive.
     * 
     * @param container The container to manage
     */
    public void setManagedContainer(final Pane container)
    {
        this.managedContainer = container;
        
        // Bind to container size changes
        if (container != null)
        {
            this.widthProperty.bind(container.widthProperty());
            this.heightProperty.bind(container.heightProperty());
        }
    }
    
    /**
     * Get the width property.
     * 
     * @return The width property
     */
    public DoubleProperty widthProperty()
    {
        return this.widthProperty;
    }
    
    /**
     * Get the height property.
     * 
     * @return The height property
     */
    public DoubleProperty heightProperty()
    {
        return this.heightProperty;
    }
    
    /**
     * Get the current breakpoint.
     * 
     * @return The current breakpoint
     */
    public Breakpoint getCurrentBreakpoint()
    {
        return this.currentBreakpoint;
    }
    
    /**
     * Get the layout configuration for the current breakpoint.
     * 
     * @return The current layout configuration
     */
    public LayoutConfig getCurrentLayoutConfig()
    {
        return this.layoutConfigs.get(this.currentBreakpoint);
    }
    
    /**
     * Set a custom layout configuration for a breakpoint.
     * 
     * @param config The layout configuration
     */
    public void setLayoutConfig(final LayoutConfig config)
    {
        this.layoutConfigs.put(config.getBreakpoint(), config);
        
        // Update layout if this is the current breakpoint
        if (config.getBreakpoint() == this.currentBreakpoint)
        {
            applyCurrentLayout();
        }
    }
    
    /**
     * Add a breakpoint change listener.
     * 
     * @param listener The listener to add
     */
    public void addBreakpointChangeListener(final Consumer<Breakpoint> listener)
    {
        this.breakpointChangeListeners.add(listener);
    }
    
    /**
     * Remove a breakpoint change listener.
     * 
     * @param listener The listener to remove
     */
    public void removeBreakpointChangeListener(final Consumer<Breakpoint> listener)
    {
        this.breakpointChangeListeners.remove(listener);
    }
    
    /**
     * Create a responsive grid layout.
     * 
     * @param nodes The nodes to arrange in the grid
     * @return A responsive grid pane
     */
    public GridPane createResponsiveGrid(final List<Node> nodes)
    {
        final GridPane grid = new GridPane();
        final LayoutConfig config = getCurrentLayoutConfig();
        
        grid.setHgap(config.getSpacing());
        grid.setVgap(config.getSpacing());
        
        // Arrange nodes in grid based on current configuration
        arrangeNodesInGrid(grid, nodes, config);
        
        // Make grid responsive
        makeGridResponsive(grid);
        
        return grid;
    }
    
    /**
     * Create a responsive split pane.
     * 
     * @param leftNode The left/top node
     * @param rightNode The right/bottom node
     * @return A responsive split pane
     */
    public SplitPane createResponsiveSplitPane(final Node leftNode, final Node rightNode)
    {
        final SplitPane splitPane = new SplitPane();
        final LayoutConfig config = getCurrentLayoutConfig();
        
        splitPane.setOrientation(config.getOrientation());
        splitPane.getItems().addAll(leftNode, rightNode);
        
        // Set divider positions based on breakpoint
        if (config.getBreakpoint() == Breakpoint.EXTRA_SMALL || 
            config.getBreakpoint() == Breakpoint.SMALL)
        {
            // On small screens, give more space to the main content
            splitPane.setDividerPositions(0.3);
        }
        else
        {
            // On larger screens, more balanced split
            splitPane.setDividerPositions(0.4);
        }
        
        return splitPane;
    }
    
    /**
     * Create a responsive container that switches between horizontal and vertical layouts.
     * 
     * @param nodes The nodes to arrange
     * @return A responsive container
     */
    public Pane createResponsiveContainer(final List<Node> nodes)
    {
        final LayoutConfig config = getCurrentLayoutConfig();
        
        if (config.getOrientation() == Orientation.HORIZONTAL)
        {
            final HBox hbox = new HBox(config.getSpacing());
            hbox.getChildren().addAll(nodes);
            
            // Make children grow to fill available space
            for (final Node node : nodes)
            {
                if (node instanceof Region)
                {
                    HBox.setHgrow(node, Priority.ALWAYS);
                }
            }
            
            return hbox;
        }
        else
        {
            final VBox vbox = new VBox(config.getSpacing());
            vbox.getChildren().addAll(nodes);
            
            // Make children grow to fill available space
            for (final Node node : nodes)
            {
                if (node instanceof Region)
                {
                    VBox.setVgrow(node, Priority.ALWAYS);
                }
            }
            
            return vbox;
        }
    }
    
    /**
     * Check if the sidebar should be shown for the current breakpoint.
     * 
     * @return True if sidebar should be shown
     */
    public boolean shouldShowSidebar()
    {
        return getCurrentLayoutConfig().isShowSidebar();
    }
    
    /**
     * Get the recommended spacing for the current breakpoint.
     * 
     * @return The spacing value
     */
    public double getCurrentSpacing()
    {
        return getCurrentLayoutConfig().getSpacing();
    }
    
    /**
     * Update the current breakpoint based on width.
     * 
     * @param width The current width
     */
    private void updateBreakpoint(final double width)
    {
        Breakpoint newBreakpoint = null;
        
        for (final Breakpoint breakpoint : Breakpoint.values())
        {
            if (breakpoint.matches(width))
            {
                newBreakpoint = breakpoint;
                break;
            }
        }
        
        if (newBreakpoint != null && newBreakpoint != this.currentBreakpoint)
        {
            this.currentBreakpoint = newBreakpoint;
            applyCurrentLayout();
            notifyBreakpointChangeListeners();
        }
    }
    
    /**
     * Apply the current layout configuration.
     */
    private void applyCurrentLayout()
    {
        if (this.managedContainer != null)
        {
            // Apply layout-specific styling
            final LayoutConfig config = getCurrentLayoutConfig();
            this.managedContainer.getStyleClass().removeIf(style -> style.startsWith("breakpoint-"));
            this.managedContainer.getStyleClass().add("breakpoint-" + config.getBreakpoint().name().toLowerCase());
        }
    }
    
    /**
     * Arrange nodes in a grid based on configuration.
     * 
     * @param grid The grid pane
     * @param nodes The nodes to arrange
     * @param config The layout configuration
     */
    private void arrangeNodesInGrid(final GridPane grid, final List<Node> nodes, final LayoutConfig config)
    {
        final int columns = config.getColumns();
        
        for (int i = 0; i < nodes.size(); i++)
        {
            final int row = i / columns;
            final int col = i % columns;
            
            grid.add(nodes.get(i), col, row);
        }
    }
    
    /**
     * Make a grid responsive by setting up column constraints.
     * 
     * @param grid The grid to make responsive
     */
    private void makeGridResponsive(final GridPane grid)
    {
        final LayoutConfig config = getCurrentLayoutConfig();
        final int columns = config.getColumns();
        
        // Clear existing column constraints
        grid.getColumnConstraints().clear();
        
        // Add responsive column constraints
        for (int i = 0; i < columns; i++)
        {
            final javafx.scene.layout.ColumnConstraints columnConstraints = 
                new javafx.scene.layout.ColumnConstraints();
            columnConstraints.setPercentWidth(100.0 / columns);
            columnConstraints.setHgrow(Priority.ALWAYS);
            grid.getColumnConstraints().add(columnConstraints);
        }
    }
    
    /**
     * Notify all breakpoint change listeners.
     */
    private void notifyBreakpointChangeListeners()
    {
        for (final Consumer<Breakpoint> listener : this.breakpointChangeListeners)
        {
            try
            {
                listener.accept(this.currentBreakpoint);
            }
            catch (final Exception e)
            {
                // Log error but don't fail the notification
                System.err.println("Error in breakpoint change listener: " + e.getMessage());
            }
        }
    }
}
