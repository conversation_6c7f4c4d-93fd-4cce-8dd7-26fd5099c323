package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;
import de.mossgrabers.projectconverter.core.ProjectConverterException;

/**
 * Exception thrown when validation fails.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ValidationException extends ProjectConverterException
{
    private static final long serialVersionUID = 1L;
    
    private final String fieldName;
    private final Object invalidValue;
    
    /**
     * Constructor for validation exception.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param fieldName The name of the field that failed validation
     * @param invalidValue The invalid value
     */
    public ValidationException(final ErrorCode errorCode, final String userMessage, 
                             final String fieldName, final Object invalidValue)
    {
        super(errorCode, userMessage, 
              String.format("Field '%s' has invalid value: %s", fieldName, invalidValue), 
              true);
        this.fieldName = fieldName;
        this.invalidValue = invalidValue;
    }
    
    /**
     * Constructor with cause.
     * 
     * @param errorCode The error code
     * @param userMessage User-friendly error message
     * @param fieldName The name of the field that failed validation
     * @param invalidValue The invalid value
     * @param cause The underlying cause
     */
    public ValidationException(final ErrorCode errorCode, final String userMessage, 
                             final String fieldName, final Object invalidValue, 
                             final Throwable cause)
    {
        super(errorCode, userMessage, 
              String.format("Field '%s' has invalid value: %s", fieldName, invalidValue), 
              true, cause);
        this.fieldName = fieldName;
        this.invalidValue = invalidValue;
    }
    
    /**
     * Get the field name that failed validation.
     * 
     * @return The field name
     */
    public String getFieldName()
    {
        return this.fieldName;
    }
    
    /**
     * Get the invalid value.
     * 
     * @return The invalid value
     */
    public Object getInvalidValue()
    {
        return this.invalidValue;
    }
}
