name: Build and publish jpackage result to GitHub

on:
  push:
    branches: [ master ]
  # Necessary to manually run the workflow
  workflow_dispatch:

jobs:

  createmanual:
    runs-on: ubuntu-latest
    steps:
      - name: Configure
        uses: actions/checkout@v4
        
      - name: Create output folder
        run: |
          mkdir output  # create output dir
      
      - name: Convert MD files to PDF
        uses: docker://pandoc/latex:2.9
        with:
          args: --standalone --toc -V geometry:margin=2.5cm --number-sections --output=output/ProjectConverter-Manual.pdf README.md
      
      - name: Publish result
        uses: actions/upload-artifact@v4
        with:
          name: ProjectConverter-Installers-Manual
          path: output/ProjectConverter-Manual.pdf

  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            artifact_name: projectconverter*.deb
            build_name: linux
          - os: windows-latest
            artifact_name: ProjectConverter*.msi
            build_name: win
          # This is ARM64
          - os: macos-14
            artifact_name: ProjectConverter*.dmg
            build_name: mac-arm
          # This is x64
          - os: macos-13
            artifact_name: ProjectConverter*.dmg
            build_name: mac-x64
    
    name: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: 21
    - name: Build and jpackage with Maven
      run: mvn -B package jpackage::jpackage@${{ matrix.build_name }} --file pom.xml
    - name: Publish jpackage result
      uses: actions/upload-artifact@v4
      with:
        name: ProjectConverter-Installers-${{ matrix.os }}
        path: target/release/${{ matrix.artifact_name }}

  merge:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Merge Artifacts
      uses: actions/upload-artifact/merge@v4
      with:
        name: ProjectConverter-Installers
        pattern: ProjectConverter-Installers-*
        separate-directories: true
