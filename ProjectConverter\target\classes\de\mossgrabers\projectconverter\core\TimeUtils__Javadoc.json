{"doc": "\n Utility functions for handling time units in DAWproject.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getArrangementTimeUnit", "paramTypes": ["com.bitwig.dawproject.Arrangement"], "doc": "\n Check for which time unit is set for the arrangement if any.\r\n\r\n @param arrangement The arrangement to check\r\n @return True if the time unit is in beats otherwise seconds. True if no setting is found\r\n"}, {"name": "updateIsBeats", "paramTypes": ["com.bitwig.dawproject.timeline.Timeline", "boolean"], "doc": "\n Checks if the given timeline object has a time unit setting, if not the current time unit is\r\n returned.\r\n\r\n @param timeline The timeline to check\r\n @param isBeats The current setting, true if set to Beats\r\n @return True if beats otherwise seconds\r\n"}, {"name": "updateWarpsTimeIsBeats", "paramTypes": ["com.bitwig.dawproject.timeline.Warps", "boolean"], "doc": "\n Checks if the given warps object has a time unit setting, if not the current time unit is\r\n returned.\r\n\r\n @param warps The warps object to check\r\n @param isBeats The current setting, true if set to Beats\r\n @return True if beats otherwise seconds\r\n"}, {"name": "updateClipContentTimeIsBeats", "paramTypes": ["com.bitwig.dawproject.timeline.Clip", "boolean"], "doc": "\n Checks if the given clip object has a time unit setting, if not the current time unit is\r\n returned.\r\n\r\n @param clip The clip object to check\r\n @param isBeats The current setting, true if set to Beats\r\n @return True if beats otherwise seconds\r\n"}, {"name": "setTimeUnit", "paramTypes": ["com.bitwig.dawproject.timeline.Timeline", "boolean"], "doc": "\n Set the time unit for a timeline.\r\n\r\n @param timeline The timeline for which to set the time unit\r\n @param isBeats Set to beats if true otherwise seconds\r\n"}, {"name": "getDuration", "paramTypes": ["com.bitwig.dawproject.timeline.Clip"], "doc": "\n Get the duration of the clip.\r\n\r\n @param clip The clip\r\n @return The duration or -1 if not set\r\n"}, {"name": "getPlayRange", "paramTypes": ["com.bitwig.dawproject.timeline.Clip"], "doc": "\n Get the length of the play start to end of the clip.\r\n\r\n @param clip The clip\r\n @return The duration of the play range\r\n"}, {"name": "getMaxDuration", "paramTypes": ["java.util.List"], "doc": "\n Find the clip with the longest duration of all the given clips in the clip slots and returns\r\n it's duration.\r\n\r\n @param clipSlots The slots to check\r\n @return The longest duration\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Private constructor since this is a utility class.\r\n"}]}