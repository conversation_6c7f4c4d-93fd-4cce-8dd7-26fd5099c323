package de.mossgrabers.projectconverter.gui.accessibility;

import javafx.scene.AccessibleRole;

/**
 * Container for accessibility information about a UI node.
 * Stores ARIA-like attributes and accessibility metadata.
 * 
 * <AUTHOR> ProjectConverter
 */
public class AccessibilityInfo
{
    private final String accessibleText;
    private final String accessibleHelp;
    private final AccessibleRole accessibleRole;
    private final boolean focusTraversable;
    private final String ariaLabel;
    private final String ariaDescription;
    private final String ariaRole;
    private final boolean ariaExpanded;
    private final boolean ariaSelected;
    private final boolean ariaChecked;
    private final String ariaValueText;
    private final Double ariaValueNow;
    private final Double ariaValueMin;
    private final Double ariaValueMax;
    
    /**
     * Builder for AccessibilityInfo.
     */
    public static class Builder
    {
        private String accessibleText;
        private String accessibleHelp;
        private AccessibleRole accessibleRole;
        private boolean focusTraversable = true;
        private String ariaLabel;
        private String ariaDescription;
        private String ariaRole;
        private boolean ariaExpanded = false;
        private boolean ariaSelected = false;
        private boolean ariaChecked = false;
        private String ariaValueText;
        private Double ariaValueNow;
        private Double ariaValueMin;
        private Double ariaValueMax;
        
        public Builder accessibleText(final String text) { this.accessibleText = text; return this; }
        public Builder accessibleHelp(final String help) { this.accessibleHelp = help; return this; }
        public Builder accessibleRole(final AccessibleRole role) { this.accessibleRole = role; return this; }
        public Builder focusTraversable(final boolean traversable) { this.focusTraversable = traversable; return this; }
        public Builder ariaLabel(final String label) { this.ariaLabel = label; return this; }
        public Builder ariaDescription(final String description) { this.ariaDescription = description; return this; }
        public Builder ariaRole(final String role) { this.ariaRole = role; return this; }
        public Builder ariaExpanded(final boolean expanded) { this.ariaExpanded = expanded; return this; }
        public Builder ariaSelected(final boolean selected) { this.ariaSelected = selected; return this; }
        public Builder ariaChecked(final boolean checked) { this.ariaChecked = checked; return this; }
        public Builder ariaValueText(final String valueText) { this.ariaValueText = valueText; return this; }
        public Builder ariaValueNow(final Double valueNow) { this.ariaValueNow = valueNow; return this; }
        public Builder ariaValueMin(final Double valueMin) { this.ariaValueMin = valueMin; return this; }
        public Builder ariaValueMax(final Double valueMax) { this.ariaValueMax = valueMax; return this; }
        
        public AccessibilityInfo build()
        {
            return new AccessibilityInfo(this);
        }
    }
    
    /**
     * Private constructor for builder pattern.
     * 
     * @param builder The builder
     */
    private AccessibilityInfo(final Builder builder)
    {
        this.accessibleText = builder.accessibleText;
        this.accessibleHelp = builder.accessibleHelp;
        this.accessibleRole = builder.accessibleRole;
        this.focusTraversable = builder.focusTraversable;
        this.ariaLabel = builder.ariaLabel;
        this.ariaDescription = builder.ariaDescription;
        this.ariaRole = builder.ariaRole;
        this.ariaExpanded = builder.ariaExpanded;
        this.ariaSelected = builder.ariaSelected;
        this.ariaChecked = builder.ariaChecked;
        this.ariaValueText = builder.ariaValueText;
        this.ariaValueNow = builder.ariaValueNow;
        this.ariaValueMin = builder.ariaValueMin;
        this.ariaValueMax = builder.ariaValueMax;
    }
    
    // Getters
    public String getAccessibleText() { return this.accessibleText; }
    public String getAccessibleHelp() { return this.accessibleHelp; }
    public AccessibleRole getAccessibleRole() { return this.accessibleRole; }
    public boolean isFocusTraversable() { return this.focusTraversable; }
    public String getAriaLabel() { return this.ariaLabel; }
    public String getAriaDescription() { return this.ariaDescription; }
    public String getAriaRole() { return this.ariaRole; }
    public boolean isAriaExpanded() { return this.ariaExpanded; }
    public boolean isAriaSelected() { return this.ariaSelected; }
    public boolean isAriaChecked() { return this.ariaChecked; }
    public String getAriaValueText() { return this.ariaValueText; }
    public Double getAriaValueNow() { return this.ariaValueNow; }
    public Double getAriaValueMin() { return this.ariaValueMin; }
    public Double getAriaValueMax() { return this.ariaValueMax; }
    
    /**
     * Create a builder for AccessibilityInfo.
     * 
     * @return A new builder
     */
    public static Builder builder()
    {
        return new Builder();
    }
    
    /**
     * Create accessibility info for a button.
     * 
     * @param text The button text
     * @param description Optional description
     * @return AccessibilityInfo for a button
     */
    public static AccessibilityInfo forButton(final String text, final String description)
    {
        return builder()
            .accessibleText("Button: " + text)
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.BUTTON)
            .ariaRole("button")
            .ariaLabel(text)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a text field.
     * 
     * @param label The field label
     * @param description Optional description
     * @return AccessibilityInfo for a text field
     */
    public static AccessibilityInfo forTextField(final String label, final String description)
    {
        return builder()
            .accessibleText("Text field: " + label)
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.TEXT_FIELD)
            .ariaRole("textbox")
            .ariaLabel(label)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a checkbox.
     * 
     * @param label The checkbox label
     * @param checked Whether the checkbox is checked
     * @param description Optional description
     * @return AccessibilityInfo for a checkbox
     */
    public static AccessibilityInfo forCheckBox(final String label, final boolean checked, final String description)
    {
        return builder()
            .accessibleText("Checkbox: " + label + (checked ? " (checked)" : " (unchecked)"))
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.CHECK_BOX)
            .ariaRole("checkbox")
            .ariaLabel(label)
            .ariaChecked(checked)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a combo box.
     * 
     * @param label The combo box label
     * @param selectedValue The currently selected value
     * @param description Optional description
     * @return AccessibilityInfo for a combo box
     */
    public static AccessibilityInfo forComboBox(final String label, final String selectedValue, final String description)
    {
        return builder()
            .accessibleText("Combo box: " + label + (selectedValue != null ? " (selected: " + selectedValue + ")" : ""))
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.COMBO_BOX)
            .ariaRole("combobox")
            .ariaLabel(label)
            .ariaValueText(selectedValue)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a progress bar.
     * 
     * @param label The progress bar label
     * @param value Current value
     * @param min Minimum value
     * @param max Maximum value
     * @param description Optional description
     * @return AccessibilityInfo for a progress bar
     */
    public static AccessibilityInfo forProgressBar(final String label, final double value, 
                                                  final double min, final double max, final String description)
    {
        final double percentage = ((value - min) / (max - min)) * 100;
        return builder()
            .accessibleText("Progress bar: " + label + " (" + String.format("%.0f", percentage) + "%)")
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.PROGRESS_INDICATOR)
            .ariaRole("progressbar")
            .ariaLabel(label)
            .ariaValueNow(value)
            .ariaValueMin(min)
            .ariaValueMax(max)
            .ariaValueText(String.format("%.0f%%", percentage))
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a tab.
     * 
     * @param label The tab label
     * @param selected Whether the tab is selected
     * @param description Optional description
     * @return AccessibilityInfo for a tab
     */
    public static AccessibilityInfo forTab(final String label, final boolean selected, final String description)
    {
        return builder()
            .accessibleText("Tab: " + label + (selected ? " (selected)" : ""))
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.TAB)
            .ariaRole("tab")
            .ariaLabel(label)
            .ariaSelected(selected)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a menu item.
     * 
     * @param label The menu item label
     * @param description Optional description
     * @return AccessibilityInfo for a menu item
     */
    public static AccessibilityInfo forMenuItem(final String label, final String description)
    {
        return builder()
            .accessibleText("Menu item: " + label)
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.MENU_ITEM)
            .ariaRole("menuitem")
            .ariaLabel(label)
            .ariaDescription(description)
            .build();
    }
    
    /**
     * Create accessibility info for a label.
     * 
     * @param text The label text
     * @param description Optional description
     * @return AccessibilityInfo for a label
     */
    public static AccessibilityInfo forLabel(final String text, final String description)
    {
        return builder()
            .accessibleText(text)
            .accessibleHelp(description)
            .accessibleRole(AccessibleRole.TEXT)
            .ariaRole("text")
            .ariaLabel(text)
            .ariaDescription(description)
            .focusTraversable(false)
            .build();
    }
    
    @Override
    public String toString()
    {
        return String.format("AccessibilityInfo{text='%s', role=%s, focusTraversable=%s}", 
                           this.accessibleText, this.accessibleRole, this.focusTraversable);
    }
}
