{"doc": "\n Converts a Reaper project file (the already loaded chunks to be more specific) into a dawproject\r\n structure. Needs to be state-less.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getExtensionFilter", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "getEditPane", "paramTypes": [], "doc": "{@inheritDoc} "}, {"name": "loadSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "saveSettings", "paramTypes": ["de.mossgrabers.tools.ui.BasicConfig"], "doc": "{@inheritDoc} "}, {"name": "read", "paramTypes": ["java.io.File"], "doc": "{@inheritDoc} "}, {"name": "convertMetadata", "paramTypes": ["com.bitwig.dawproject.MetaData", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Fills the metadata description file.\r\n\r\n @param metadata The metadata to fill\r\n @param rootChunk The project root chunk\r\n"}, {"name": "handleMetadataTag", "paramTypes": ["com.bitwig.dawproject.MetaData", "de.mossgrabers.projectconverter.format.reaper.model.Node"], "doc": "\n Check if some useful metadata can be extracted from a render chunk.\r\n\r\n @param metadata The metadata to fill\r\n @param tagNode A sub node of a render chunk\r\n"}, {"name": "convertArrangement", "paramTypes": ["com.bitwig.dawproject.Project", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill the arrangement structure.\r\n\r\n @param project The project to fill\r\n @param rootChunk The root chunk\r\n @param beatsAndTime The beats and/or time conversion information\r\n"}, {"name": "convertMarkers", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Create all markers.\r\n\r\n @param dawProject The DAWproject container\r\n @param rootChunk The root chunk\r\n @param beatsAndTime The beats and/or time conversion information\r\n"}, {"name": "convertTransport", "paramTypes": ["com.bitwig.dawproject.Project", "de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Fill the transport structure.\r\n\r\n @param project The project\r\n @param rootChunk The root chunk\r\n"}, {"name": "convertMaster", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperDetector.FolderStructure", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill the master track structure.\r\n\r\n @param dawProject The DAWproject container\r\n @param mediaFilesMap Map to collect media files\r\n @param rootChunk The root chunk\r\n @param folderStructure The folder structure\r\n @param beatsAndTime The beats and/or time conversion information\r\n @return The lilst with all tempo changes\r\n @throws ParseException Could not parse the master\r\n"}, {"name": "convertTracks", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.File", "de.mossgrabers.projectconverter.format.reaper.ReaperDetector.FolderStructure", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill the track structure.\r\n\r\n @param dawProject The DAWproject container\r\n @param mediaFilesMap Map to collect media files\r\n @param rootChunk The root chunk\r\n @param sourcePath The path of the source project file\r\n @param folderStructure The folder structure\r\n @param beatsAndTime The beats and/or time conversion information\r\n @throws ParseException Could not parse the tracks\r\n"}, {"name": "convertTrack", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.File", "de.mossgrabers.projectconverter.format.reaper.ReaperDetector.FolderStructure", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill the track structure.\r\n\r\n @param dawProject The DAWproject container\r\n @param mediaFilesMap Map to collect media files\r\n @param trackChunk The track chunk\r\n @param sourcePath The path of the source project file\r\n @param folderStructure The folder structure\r\n @param beatsAndTime The beats and/or time conversion information\r\n @return The created track\r\n @throws ParseException Could not parse the track info\r\n"}, {"name": "convertAutomation", "paramTypes": ["com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "de.mossgrabers.projectconverter.format.reaper.ReaperDetector.FolderStructure", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill the envelope structure.\r\n\r\n @param track The track to add the media item clips\r\n @param trackChunk The track chunk\r\n @param folderStructure The folder structure\r\n @param beatsAndTime The beats and/or time conversion information\r\n"}, {"name": "convertDevices", "paramTypes": ["java.util.Map", "com.bitwig.dawproject.Track", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.lang.String", "de.mossgrabers.projectconverter.format.reaper.ReaperDetector.FolderStructure"], "doc": "\n Fill the devices structure.\r\n\r\n @param mediaFilesMap Map to collect media files\r\n @param track The track\r\n @param trackChunk The track chunk\r\n @param chunkName The name of the FX list chunk\r\n @return The list with the parsed devices\r\n @param folderStructure The folder structure\r\n @throws ParseException Could not parse the track info\r\n"}, {"name": "convertDevice", "paramTypes": ["java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "boolean", "boolean"], "doc": "\n Analyze one FX device chunk.\r\n\r\n @param mediaFilesMap Map to collect media files\r\n @param chunk The device chunk\r\n @param offline True if the FX device is offline\r\n @param bypass True if the FX device is bypassed\r\n @return The created device\r\n @throws ParseException Error during parsing\r\n"}, {"name": "convertClips", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.util.Map", "com.bitwig.dawproject.timeline.Lanes", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.File", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Add the media item clips to the track structure.\r\n\r\n @param dawProject The DAWproject container\r\n @param mediaFilesMap Map to collect media files\r\n @param trackLanes The lanes of the track to add the media item clips\r\n @param trackChunk The track chunk\r\n @param sourcePath The path of the source project file\r\n @param beatsAndTime The beats and/or time conversion information\r\n @return The type of converted clips\r\n @throws ParseException Could not parse the track info\r\n"}, {"name": "convertClip", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.File", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime", "java.util.Set"], "doc": "\n Parse one item clip.\r\n\r\n @param dawProject The DAWproject container\r\n @param mediaFilesMap Map to collect media files\r\n @param itemChunk The item chunk to parse\r\n @param sourcePath The path of the source project file\r\n @param beatsAndTime The beats and/or time conversion information\r\n @param contentTypes The content types of the clips\r\n @return The clip\r\n @throws ParseException Could not parse a clip\r\n"}, {"name": "convertMIDI", "paramTypes": ["de.mossgrabers.projectconverter.core.DawProjectContainer", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "com.bitwig.dawproject.timeline.Lanes", "de.mossgrabers.projectconverter.format.reaper.BeatsAndTime"], "doc": "\n Fill a MIDI clip.\r\n\r\n @param dawProject The DAWproject container\r\n @param sourceChunk The source chunk which contains the clip data\r\n @param lanes The lanes where to add the MIDI events\r\n @param beatsAndTime The beats and/or time conversion information\r\n @return The end of the MIDI events\r\n @throws ParseException Could not parse the notes\r\n"}, {"name": "convertAudio", "paramTypes": ["java.util.Map", "de.mossgrabers.projectconverter.format.reaper.model.Chunk", "java.io.File"], "doc": "\n Fill an audio clip.\r\n\r\n @param mediaFilesMap Map to collect media files\r\n @param sourceChunk The audio source chunk\r\n @param sourcePath The path of the source project file\r\n @return The created Audio clip object\r\n @throws ParseException Could not retrieve audio file format\r\n"}, {"name": "readTicksPerQuarterNote", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Chunk"], "doc": "\n Get the resolution of the note times (ticks per quarter note)\r\n\r\n @param sourceChunk The source chunk from which to read the information\r\n @return The resolution or -1 if it could not be read\r\n"}, {"name": "findNoteStart", "paramTypes": ["java.util.List", "de.mossgrabers.projectconverter.format.reaper.model.ReaperMidiEvent"], "doc": "\n Find the matching note start event for a note end event.\r\n\r\n @param noteStarts All note start events\r\n @param midiEvent The note end event for which to find the start\r\n @return The event or null if not found\r\n"}, {"name": "getPara<PERSON>", "paramTypes": ["java.util.Optional", "java.lang.String"], "doc": "\n Get the first parameter value of a node as a string.\r\n\r\n @param optionalNode The node from which to get the parameter value\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "getPara<PERSON>", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "int", "java.lang.String"], "doc": "\n Get the first parameter value of a node as a string.\r\n\r\n @param node The node from which to get the parameter value\r\n @param position The index of the parameter\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "getIntParam", "paramTypes": ["java.util.Optional", "int"], "doc": "\n Get the first parameter value of a node as an integer.\r\n\r\n @param optionalNode The node from which to get the parameter value\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "getIntParams", "paramTypes": ["java.util.Optional", "int"], "doc": "\n Get all parameter values of a node as integers.\r\n\r\n @param optionalNode The node from which to get the parameter values\r\n @param defaultValue The value to return if there is no value present\r\n @return The read values of the default value\r\n"}, {"name": "getIntParams", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "int"], "doc": "\n Get all parameter values of a node as integers.\r\n\r\n @param node The node from which to get the parameter values\r\n @param defaultValue The value to return if there is no value present\r\n @return The read values of the default value\r\n"}, {"name": "getIntParam", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "int", "int"], "doc": "\n Get the parameter value at the given position of a node as an integer.\r\n\r\n @param node The node from which to get the parameter value\r\n @param position The index of the parameter\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "getDoubleParam", "paramTypes": ["java.util.Optional", "double"], "doc": "\n Get the first parameter value of a node as a double.\r\n\r\n @param optionalNode The node from which to get the parameter value\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "getDoubleParams", "paramTypes": ["java.util.Optional", "double"], "doc": "\n Get all parameter values of a node as doubles.\r\n\r\n @param optionalNode The node from which to get the parameter values\r\n @param defaultValue The value to return if there is no value present\r\n @return The read values of the default value\r\n"}, {"name": "getDoubleParam", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.model.Node", "int", "double"], "doc": "\n Get the parameter value at the given position of a node as a double.\r\n\r\n @param node The node from which to get the parameter value\r\n @param position The index of the parameter\r\n @param defaultValue The value to return if there is no value present\r\n @return The read value of the default value\r\n"}, {"name": "toHexColor", "paramTypes": ["int"], "doc": "\n Format the ARGB color as a hex string.\r\n\r\n @param color The color to format\r\n @return The formatted color\r\n"}, {"name": "getDuration", "paramTypes": ["javax.sound.sampled.AudioFileFormat"], "doc": "\n Get the duration of the audio file from the format object.\r\n\r\n @param audioFileFormat The audio format\r\n @return The duration in seconds\r\n"}, {"name": "handleTime", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.BeatsAndTime", "double", "boolean"], "doc": "\n Converts time to beats, vice versa or not at all depending on the source and destination time\r\n base.\r\n\r\n @param beatsAndTime The beats and/or time conversion information\r\n @param time The value to convert\r\n @param isEnvelope True if the data is read from an envelope, which might have a different\r\n            time base in Reaper\r\n @return The value matching the destination time base\r\n"}, {"name": "handleMIDITime", "paramTypes": ["de.mossgrabers.projectconverter.format.reaper.BeatsAndTime", "double"], "doc": "\n Converts beats (MIDI timing is always in beats) to time if necessary depending on the\r\n destination time base.\r\n\r\n @param beatsAndTime The beats and/or time conversion information\r\n @param time The value to convert\r\n @return The value matching the destination time base\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": "\n Constructor.\r\n\r\n @param notifier The notifier\r\n"}]}