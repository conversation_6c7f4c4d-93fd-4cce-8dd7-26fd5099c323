{"doc": " Modern card component with Material Design styling, hover effects, and flexible content layout.\n Supports different elevation levels, interactive states, and responsive design.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": [], "doc": " Initialize the card.\n"}, {"name": "setupComponents", "paramTypes": [], "doc": " Set up UI components.\n"}, {"name": "setupAnimations", "paramTypes": [], "doc": " Set up animations.\n"}, {"name": "setupPropertyBindings", "paramTypes": [], "doc": " Set up property bindings.\n"}, {"name": "setupStyleClasses", "paramTypes": [], "doc": " Set up style classes.\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["javafx.scene.Node"], "doc": " Set the card content.\n \n @param content The content node\n"}, {"name": "addContent", "paramTypes": ["javafx.scene.Node"], "doc": " Add content to the card.\n \n @param content The content node to add\n"}, {"name": "addAction", "paramTypes": ["javafx.scene.Node"], "doc": " Add an action button to the card.\n \n @param action The action node (typically a button)\n"}, {"name": "clearActions", "paramTypes": [], "doc": " Clear all actions from the card.\n"}, {"name": "getContentContainer", "paramTypes": [], "doc": " Get the content container for direct manipulation.\n \n @return The content container\n"}, {"name": "getActionContainer", "paramTypes": [], "doc": " Get the action container for direct manipulation.\n \n @return The action container\n"}, {"name": "updateTitle", "paramTypes": ["java.lang.String"], "doc": " Update the title display.\n \n @param title The new title\n"}, {"name": "updateSubtitle", "paramTypes": ["java.lang.String"], "doc": " Update the subtitle display.\n \n @param subtitle The new subtitle\n"}, {"name": "updateElevation", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ModernCard.Elevation"], "doc": " Update the card elevation.\n \n @param elevation The new elevation\n"}, {"name": "updateStyle", "paramTypes": ["de.mossgrabers.projectconverter.gui.components.ModernCard.Style", "de.mossgrabers.projectconverter.gui.components.ModernCard.Style"], "doc": " Update the card style.\n \n @param oldStyle The old style\n @param newStyle The new style\n"}, {"name": "updateInteractiveState", "paramTypes": ["boolean"], "doc": " Update the interactive state.\n \n @param interactive True if interactive\n"}, {"name": "createSimple", "paramTypes": ["java.lang.String", "javafx.scene.Node"], "doc": " Create a simple card with title and content.\n \n @param title The card title\n @param content The card content\n @return A simple card\n"}, {"name": "createElevated", "paramTypes": ["java.lang.String"], "doc": " Create an elevated card.\n \n @param title The card title\n @return An elevated card\n"}, {"name": "createInteractive", "paramTypes": ["java.lang.String"], "doc": " Create an interactive card.\n \n @param title The card title\n @return An interactive card\n"}, {"name": "createOutlined", "paramTypes": ["java.lang.String"], "doc": " Create an outlined card.\n \n @param title The card title\n @return An outlined card\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " Constructor.\n"}, {"name": "<init>", "paramTypes": ["java.lang.String"], "doc": " Con<PERSON><PERSON>ctor with title.\n \n @param title The card title\n"}, {"name": "<init>", "paramTypes": ["java.lang.String", "javafx.scene.Node"], "doc": " Constructor with title and content.\n \n @param title The card title\n @param content The card content\n"}]}