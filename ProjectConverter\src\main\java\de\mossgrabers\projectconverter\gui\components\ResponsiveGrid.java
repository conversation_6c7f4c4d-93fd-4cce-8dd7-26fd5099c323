package de.mossgrabers.projectconverter.gui.components;

import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.layout.ColumnConstraints;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;

import java.util.HashMap;
import java.util.Map;

/**
 * Responsive grid component that automatically adjusts column count and layout
 * based on screen size and breakpoints.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ResponsiveGrid extends GridPane
{
    private final ResponsiveLayoutManager layoutManager;
    private final IntegerProperty columnsProperty = new SimpleIntegerProperty(3);
    private final Map<ResponsiveLayoutManager.Breakpoint, Integer> columnConfig = new HashMap<>();
    
    /**
     * Constructor.
     */
    public ResponsiveGrid()
    {
        this.layoutManager = new ResponsiveLayoutManager();
        setupDefaultColumnConfiguration();
        setupResponsiveBehavior();
        setupStyleClasses();
    }
    
    /**
     * Constructor with layout manager.
     * 
     * @param layoutManager The responsive layout manager
     */
    public ResponsiveGrid(final ResponsiveLayoutManager layoutManager)
    {
        this.layoutManager = layoutManager;
        setupDefaultColumnConfiguration();
        setupResponsiveBehavior();
        setupStyleClasses();
    }
    
    /**
     * Set up default column configuration for different breakpoints.
     */
    private void setupDefaultColumnConfiguration()
    {
        this.columnConfig.put(ResponsiveLayoutManager.Breakpoint.EXTRA_SMALL, 1);
        this.columnConfig.put(ResponsiveLayoutManager.Breakpoint.SMALL, 1);
        this.columnConfig.put(ResponsiveLayoutManager.Breakpoint.MEDIUM, 2);
        this.columnConfig.put(ResponsiveLayoutManager.Breakpoint.LARGE, 3);
        this.columnConfig.put(ResponsiveLayoutManager.Breakpoint.EXTRA_LARGE, 3);
    }
    
    /**
     * Set up responsive behavior.
     */
    private void setupResponsiveBehavior()
    {
        // Listen for breakpoint changes
        this.layoutManager.addBreakpointChangeListener(this::onBreakpointChanged);
        
        // Listen for children changes to re-layout
        getChildren().addListener((ListChangeListener<Node>) change -> {
            while (change.next())
            {
                if (change.wasAdded() || change.wasRemoved())
                {
                    relayoutChildren();
                }
            }
        });
        
        // Listen for column count changes
        this.columnsProperty.addListener((obs, oldColumns, newColumns) -> {
            updateColumnConstraints();
            relayoutChildren();
        });
        
        // Set initial layout manager container
        this.layoutManager.setManagedContainer(this);
    }
    
    /**
     * Set up style classes.
     */
    private void setupStyleClasses()
    {
        getStyleClass().add("responsive-grid");
    }
    
    /**
     * Get the columns property.
     * 
     * @return The columns property
     */
    public IntegerProperty columnsProperty()
    {
        return this.columnsProperty;
    }
    
    /**
     * Get the number of columns.
     * 
     * @return The number of columns
     */
    public int getColumns()
    {
        return this.columnsProperty.get();
    }
    
    /**
     * Set the number of columns.
     * 
     * @param columns The number of columns
     */
    public void setColumns(final int columns)
    {
        this.columnsProperty.set(Math.max(1, columns));
    }
    
    /**
     * Set the column configuration for a specific breakpoint.
     * 
     * @param breakpoint The breakpoint
     * @param columns The number of columns for this breakpoint
     */
    public void setColumnsForBreakpoint(final ResponsiveLayoutManager.Breakpoint breakpoint, final int columns)
    {
        this.columnConfig.put(breakpoint, Math.max(1, columns));
        
        // Update current layout if this is the current breakpoint
        if (breakpoint == this.layoutManager.getCurrentBreakpoint())
        {
            setColumns(columns);
        }
    }
    
    /**
     * Get the column configuration for a breakpoint.
     * 
     * @param breakpoint The breakpoint
     * @return The number of columns for this breakpoint
     */
    public int getColumnsForBreakpoint(final ResponsiveLayoutManager.Breakpoint breakpoint)
    {
        return this.columnConfig.getOrDefault(breakpoint, 3);
    }
    
    /**
     * Add a child node to the grid.
     * The node will be automatically positioned based on the current layout.
     * 
     * @param child The child node to add
     */
    public void addChild(final Node child)
    {
        getChildren().add(child);
    }
    
    /**
     * Add a child node at a specific grid position.
     * 
     * @param child The child node to add
     * @param columnIndex The column index
     * @param rowIndex The row index
     */
    public void addChild(final Node child, final int columnIndex, final int rowIndex)
    {
        add(child, columnIndex, rowIndex);
    }
    
    /**
     * Add a child node with column and row span.
     * 
     * @param child The child node to add
     * @param columnIndex The column index
     * @param rowIndex The row index
     * @param columnSpan The column span
     * @param rowSpan The row span
     */
    public void addChild(final Node child, final int columnIndex, final int rowIndex, 
                        final int columnSpan, final int rowSpan)
    {
        add(child, columnIndex, rowIndex, columnSpan, rowSpan);
    }
    
    /**
     * Remove a child node from the grid.
     * 
     * @param child The child node to remove
     */
    public void removeChild(final Node child)
    {
        getChildren().remove(child);
    }
    
    /**
     * Clear all children from the grid.
     */
    public void clearChildren()
    {
        getChildren().clear();
    }
    
    /**
     * Get the responsive layout manager.
     * 
     * @return The layout manager
     */
    public ResponsiveLayoutManager getLayoutManager()
    {
        return this.layoutManager;
    }
    
    /**
     * Handle breakpoint changes.
     * 
     * @param newBreakpoint The new breakpoint
     */
    private void onBreakpointChanged(final ResponsiveLayoutManager.Breakpoint newBreakpoint)
    {
        // Update column count based on new breakpoint
        final int newColumns = getColumnsForBreakpoint(newBreakpoint);
        setColumns(newColumns);
        
        // Update spacing based on layout configuration
        final ResponsiveLayoutManager.LayoutConfig config = this.layoutManager.getCurrentLayoutConfig();
        setHgap(config.getSpacing());
        setVgap(config.getSpacing());
        
        // Update style classes
        updateBreakpointStyleClasses(newBreakpoint);
    }
    
    /**
     * Update style classes based on current breakpoint.
     * 
     * @param breakpoint The current breakpoint
     */
    private void updateBreakpointStyleClasses(final ResponsiveLayoutManager.Breakpoint breakpoint)
    {
        // Remove existing breakpoint classes
        getStyleClass().removeIf(styleClass -> styleClass.startsWith("breakpoint-"));
        
        // Add new breakpoint class
        getStyleClass().add("breakpoint-" + breakpoint.name().toLowerCase());
    }
    
    /**
     * Update column constraints based on current column count.
     */
    private void updateColumnConstraints()
    {
        final ObservableList<ColumnConstraints> constraints = getColumnConstraints();
        constraints.clear();
        
        final int columns = getColumns();
        final double percentWidth = 100.0 / columns;
        
        for (int i = 0; i < columns; i++)
        {
            final ColumnConstraints columnConstraints = new ColumnConstraints();
            columnConstraints.setPercentWidth(percentWidth);
            columnConstraints.setHgrow(Priority.ALWAYS);
            columnConstraints.setFillWidth(true);
            constraints.add(columnConstraints);
        }
    }
    
    /**
     * Re-layout all children in the grid.
     */
    private void relayoutChildren()
    {
        final ObservableList<Node> children = getChildren();
        if (children.isEmpty())
        {
            return;
        }
        
        // Clear current grid positions
        for (final Node child : children)
        {
            GridPane.clearConstraints(child);
        }
        
        // Re-position children based on current column count
        final int columns = getColumns();
        for (int i = 0; i < children.size(); i++)
        {
            final int row = i / columns;
            final int col = i % columns;
            
            final Node child = children.get(i);
            GridPane.setColumnIndex(child, col);
            GridPane.setRowIndex(child, row);
        }
    }
    
    /**
     * Set equal column widths.
     * 
     * @param equal True to set equal column widths
     */
    public void setEqualColumnWidths(final boolean equal)
    {
        if (equal)
        {
            updateColumnConstraints();
        }
        else
        {
            getColumnConstraints().clear();
        }
    }
    
    /**
     * Set the gap between grid cells.
     * 
     * @param gap The gap size
     */
    public void setGap(final double gap)
    {
        setHgap(gap);
        setVgap(gap);
    }
    
    /**
     * Enable or disable responsive behavior.
     * 
     * @param responsive True to enable responsive behavior
     */
    public void setResponsive(final boolean responsive)
    {
        if (responsive)
        {
            this.layoutManager.setManagedContainer(this);
        }
        else
        {
            this.layoutManager.setManagedContainer(null);
        }
    }
}
