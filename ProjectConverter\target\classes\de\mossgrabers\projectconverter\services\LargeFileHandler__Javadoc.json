{"doc": " Handler for processing large files with chunked processing capabilities.\n Provides memory-efficient processing of large project files by breaking them into manageable chunks.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "processFile", "paramTypes": ["java.io.File", "java.util.function.Function", "java.util.function.Function"], "doc": " Process a large file with chunked processing.\n \n @param <T> The type of result from processing\n @param file The file to process\n @param chunkProcessor Function to process each chunk\n @param resultCombiner Function to combine chunk results\n @return The combined result\n @throws ProjectConverterException If processing fails\n"}, {"name": "processFileAsLines", "paramTypes": ["java.io.File", "java.util.function.Function"], "doc": " Process a file as lines with chunked processing.\n \n @param file The file to process\n @param lineProcessor Function to process lines in each chunk\n @return List of results from each chunk\n @throws ProjectConverterException If processing fails\n"}, {"name": "shutdown", "paramTypes": [], "doc": " Shutdown the large file handler and cleanup resources.\n"}, {"name": "createDefault", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": " Create a large file handler with default settings.\n \n @param notifier The notifier for progress updates\n @return Large file handler\n"}, {"name": "createWithMemoryMonitoring", "paramTypes": ["de.mossgrabers.projectconverter.INotifier", "de.mossgrabers.projectconverter.services.MemoryMonitor"], "doc": " Create a large file handler with memory monitoring.\n \n @param notifier The notifier for progress updates\n @param memoryMonitor The memory monitor\n @return Large file handler with memory monitoring\n"}, {"name": "createParallel", "paramTypes": ["de.mossgrabers.projectconverter.INotifier", "de.mossgrabers.projectconverter.services.MemoryMonitor"], "doc": " Create a large file handler with parallel processing.\n \n @param notifier The notifier for progress updates\n @param memoryMonitor The memory monitor\n @return Large file handler with parallel processing\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier"], "doc": " Constructor with default settings.\n \n @param notifier The notifier for progress updates\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.INotifier", "de.mossgrabers.projectconverter.services.MemoryMonitor", "long", "boolean"], "doc": " Constructor with custom settings.\n \n @param notifier The notifier for progress updates\n @param memoryMonitor Optional memory monitor\n @param chunkSize Number of lines per chunk\n @param parallelProcessing Whether to enable parallel processing\n"}]}