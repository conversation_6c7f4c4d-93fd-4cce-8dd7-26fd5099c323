{"doc": " Container for accessibility information about a UI node.\n Stores ARIA-like attributes and accessibility metadata.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "builder", "paramTypes": [], "doc": " Create a builder for AccessibilityInfo.\n \n @return A new builder\n"}, {"name": "forButton", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create accessibility info for a button.\n \n @param text The button text\n @param description Optional description\n @return AccessibilityInfo for a button\n"}, {"name": "forTextField", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create accessibility info for a text field.\n \n @param label The field label\n @param description Optional description\n @return AccessibilityInfo for a text field\n"}, {"name": "forCheckBox", "paramTypes": ["java.lang.String", "boolean", "java.lang.String"], "doc": " Create accessibility info for a checkbox.\n \n @param label The checkbox label\n @param checked Whether the checkbox is checked\n @param description Optional description\n @return AccessibilityInfo for a checkbox\n"}, {"name": "forComboBox", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " Create accessibility info for a combo box.\n \n @param label The combo box label\n @param selectedValue The currently selected value\n @param description Optional description\n @return AccessibilityInfo for a combo box\n"}, {"name": "forProgressBar", "paramTypes": ["java.lang.String", "double", "double", "double", "java.lang.String"], "doc": " Create accessibility info for a progress bar.\n \n @param label The progress bar label\n @param value Current value\n @param min Minimum value\n @param max Maximum value\n @param description Optional description\n @return AccessibilityInfo for a progress bar\n"}, {"name": "forTab", "paramTypes": ["java.lang.String", "boolean", "java.lang.String"], "doc": " Create accessibility info for a tab.\n \n @param label The tab label\n @param selected Whether the tab is selected\n @param description Optional description\n @return AccessibilityInfo for a tab\n"}, {"name": "forMenuItem", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create accessibility info for a menu item.\n \n @param label The menu item label\n @param description Optional description\n @return AccessibilityInfo for a menu item\n"}, {"name": "forLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create accessibility info for a label.\n \n @param text The label text\n @param description Optional description\n @return AccessibilityInfo for a label\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.gui.accessibility.AccessibilityInfo.Builder"], "doc": " Private constructor for builder pattern.\n \n @param builder The builder\n"}]}