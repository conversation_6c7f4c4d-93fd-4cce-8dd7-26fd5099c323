{"doc": " Generator for test data including sample project files and DAW projects.\n Provides various project configurations for testing different scenarios.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateSimpleProject", "paramTypes": [], "doc": " Generate a simple test project with basic structure.\n \n @return Simple test project\n"}, {"name": "generateComplexProject", "paramTypes": [], "doc": " Generate a complex test project with many tracks, devices, and clips.\n \n @return Complex test project\n"}, {"name": "generateProblematicProject", "paramTypes": [], "doc": " Generate a project with problematic content for testing error handling.\n \n @return Project with potential issues\n"}, {"name": "generateLargeProject", "paramTypes": ["int", "int"], "doc": " Generate a large project for performance testing.\n \n @param trackCount Number of tracks to create\n @param clipsPerTrack Number of clips per track\n @return Large test project\n"}, {"name": "generateReaperProjectFile", "paramTypes": ["java.nio.file.Path"], "doc": " Generate a test Reaper project file content.\n \n @param tempDir Temporary directory for file creation\n @return File containing Reaper project content\n @throws IOException If file creation fails\n"}, {"name": "createTempDirectory", "paramTypes": [], "doc": " Create a temporary directory for test files.\n \n @return Temporary directory path\n @throws IOException If directory creation fails\n"}, {"name": "cleanupTempDirectory", "paramTypes": ["java.nio.file.Path"], "doc": " Clean up temporary test files.\n \n @param tempDir Temporary directory to clean up\n"}], "constructors": []}