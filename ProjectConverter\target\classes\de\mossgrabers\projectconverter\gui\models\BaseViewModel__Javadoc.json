{"doc": " Base class for all view models in the MVVM architecture.\n Provides common functionality for property binding and change notifications.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "busyProperty", "paramTypes": [], "doc": " Get the busy property indicating if the view model is performing operations.\n \n @return The busy property\n"}, {"name": "isBusy", "paramTypes": [], "doc": " Check if the view model is busy.\n \n @return True if busy\n"}, {"name": "setBusy", "paramTypes": ["boolean"], "doc": " Set the busy state.\n \n @param busy True if busy\n"}, {"name": "statusMessageProperty", "paramTypes": [], "doc": " Get the status message property.\n \n @return The status message property\n"}, {"name": "getStatusMessage", "paramTypes": [], "doc": " Get the current status message.\n \n @return The status message\n"}, {"name": "setStatusMessage", "paramTypes": ["java.lang.String"], "doc": " Set the status message.\n \n @param message The status message\n"}, {"name": "addPropertyChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Add a property change listener.\n \n @param listener The listener to add\n"}, {"name": "removePropertyChangeListener", "paramTypes": ["java.util.function.Consumer"], "doc": " Remove a property change listener.\n \n @param listener The listener to remove\n"}, {"name": "notify<PERSON>roperty<PERSON><PERSON>ed", "paramTypes": ["java.lang.String"], "doc": " Notify all listeners of a property change.\n \n @param propertyName The name of the changed property\n"}, {"name": "initialize", "paramTypes": [], "doc": " Initialize the view model.\n Called after construction to set up initial state.\n"}, {"name": "cleanup", "paramTypes": [], "doc": " Cleanup resources when the view model is no longer needed.\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": " Validate the current state of the view model.\n \n @return True if the state is valid\n"}, {"name": "getValidationErrors", "paramTypes": [], "doc": " Get validation errors for the current state.\n \n @return List of validation error messages\n"}, {"name": "reset", "paramTypes": [], "doc": " Reset the view model to its initial state.\n"}], "constructors": []}