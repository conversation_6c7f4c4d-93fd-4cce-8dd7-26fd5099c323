package de.mossgrabers.projectconverter.core;

/**
 * Enumeration of error codes for the ProjectConverter application.
 * Each error code includes a category, severity, and description.
 * 
 * <AUTHOR> ProjectConverter
 */
public enum ErrorCode
{
    // File I/O Errors (1000-1999)
    FILE_NOT_FOUND(1001, ErrorSeverity.ERROR, "File not found"),
    FILE_ACCESS_DENIED(1002, ErrorSeverity.ERROR, "File access denied"),
    FILE_CORRUPTED(1003, ErrorSeverity.ERROR, "File is corrupted or unreadable"),
    FILE_TOO_LARGE(1004, ErrorSeverity.WARNING, "File is very large and may cause performance issues"),
    DIRECTORY_NOT_FOUND(1005, ErrorSeverity.ERROR, "Directory not found"),
    INSUFFICIENT_DISK_SPACE(1006, ErrorSeverity.ERROR, "Insufficient disk space"),
    
    // Validation Errors (2000-2999)
    INVALID_PROJECT_FORMAT(2001, ErrorSeverity.ERROR, "Invalid project file format"),
    UNSUPPORTED_VERSION(2002, ErrorSeverity.WARNING, "Unsupported project version"),
    MISSING_REQUIRED_DATA(2003, ErrorSeverity.ERROR, "Missing required project data"),
    INVALID_AUDIO_FORMAT(2004, ErrorSeverity.WARNING, "Invalid or unsupported audio format"),
    PLUGIN_NOT_FOUND(2005, ErrorSeverity.WARNING, "Plugin not found or not supported"),
    SCHEMA_VALIDATION_FAILED(2006, ErrorSeverity.ERROR, "Schema validation failed"),
    
    // Conversion Errors (3000-3999)
    CONVERSION_FAILED(3001, ErrorSeverity.ERROR, "Conversion process failed"),
    PARTIAL_CONVERSION(3002, ErrorSeverity.WARNING, "Conversion completed with some data loss"),
    UNSUPPORTED_FEATURE(3003, ErrorSeverity.INFO, "Feature not supported in target format"),
    TEMPO_CONVERSION_ERROR(3004, ErrorSeverity.WARNING, "Error converting tempo information"),
    AUTOMATION_CONVERSION_ERROR(3005, ErrorSeverity.WARNING, "Error converting automation data"),
    
    // Memory and Performance Errors (4000-4999)
    OUT_OF_MEMORY(4001, ErrorSeverity.ERROR, "Out of memory"),
    MEMORY_WARNING(4002, ErrorSeverity.WARNING, "High memory usage detected"),
    PERFORMANCE_WARNING(4003, ErrorSeverity.INFO, "Operation may take a long time"),
    RESOURCE_LEAK(4004, ErrorSeverity.WARNING, "Potential resource leak detected"),
    
    // Configuration Errors (5000-5999)
    INVALID_CONFIGURATION(5001, ErrorSeverity.ERROR, "Invalid configuration"),
    MISSING_CONFIGURATION(5002, ErrorSeverity.WARNING, "Missing configuration, using defaults"),
    CONFIGURATION_SAVE_FAILED(5003, ErrorSeverity.WARNING, "Failed to save configuration"),
    
    // Network and External Errors (6000-6999)
    NETWORK_ERROR(6001, ErrorSeverity.ERROR, "Network error"),
    EXTERNAL_TOOL_ERROR(6002, ErrorSeverity.ERROR, "External tool error"),
    
    // Unknown Errors (9000-9999)
    UNKNOWN_ERROR(9001, ErrorSeverity.ERROR, "Unknown error occurred"),
    INTERNAL_ERROR(9002, ErrorSeverity.ERROR, "Internal application error");
    
    private final int code;
    private final ErrorSeverity severity;
    private final String description;
    
    /**
     * Constructor.
     * 
     * @param code The numeric error code
     * @param severity The error severity
     * @param description The error description
     */
    ErrorCode(final int code, final ErrorSeverity severity, final String description)
    {
        this.code = code;
        this.severity = severity;
        this.description = description;
    }
    
    /**
     * Get the numeric error code.
     * 
     * @return The error code
     */
    public int getCode()
    {
        return this.code;
    }
    
    /**
     * Get the error severity.
     * 
     * @return The severity
     */
    public ErrorSeverity getSeverity()
    {
        return this.severity;
    }
    
    /**
     * Get the error description.
     * 
     * @return The description
     */
    public String getDescription()
    {
        return this.description;
    }
    
    /**
     * Get the error category based on the code range.
     * 
     * @return The error category
     */
    public String getCategory()
    {
        final int range = this.code / 1000;
        return switch (range)
        {
            case 1 -> "File I/O";
            case 2 -> "Validation";
            case 3 -> "Conversion";
            case 4 -> "Memory/Performance";
            case 5 -> "Configuration";
            case 6 -> "Network/External";
            case 9 -> "Internal";
            default -> "Unknown";
        };
    }
    
    @Override
    public String toString()
    {
        return String.format("%s-%d: %s [%s]", getCategory(), this.code, this.description, this.severity);
    }
}
