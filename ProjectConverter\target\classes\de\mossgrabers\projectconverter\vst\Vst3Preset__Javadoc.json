{"doc": "\n Supports reading and writing of VST 3 preset files.\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "HEADER_SIZE", "doc": "The size of the VST 3 preset header. "}, {"name": "CHUNK_IDS", "doc": "The IDs of the two default chunks. "}], "enumConstants": [], "methods": [{"name": "getVersion", "paramTypes": [], "doc": "\n Get the version.\r\n\r\n @return The version\r\n"}, {"name": "getClassID", "paramTypes": [], "doc": "\n Get the unique class ID of the plugin to which the preset belongs.\r\n\r\n @return The ID\r\n"}, {"name": "getData", "paramTypes": [], "doc": "\n Get the data block.\r\n\r\n @return The data\r\n"}, {"name": "getChunkInfos", "paramTypes": [], "doc": "\n Get the chunk information which describes the structure of the data block.\r\n\r\n @return The chunk information\r\n"}, {"name": "read", "paramTypes": ["java.io.InputStream"], "doc": "\n Read a preset from an input stream.\r\n\r\n @param input The input stream to read from\r\n @throws IOException Error during read\r\n"}, {"name": "write", "paramTypes": ["java.io.OutputStream", "java.lang.String", "java.util.List"], "doc": "\n Writes a VST 3 preset with 1 or 2 default chunks.\r\n\r\n @param output Where to write the preset to\r\n @param classID The unique class ID of the plugin to which the preset belongs\r\n @param chunks The content of the chunks\r\n @throws IOException Could not write\r\n"}], "constructors": []}