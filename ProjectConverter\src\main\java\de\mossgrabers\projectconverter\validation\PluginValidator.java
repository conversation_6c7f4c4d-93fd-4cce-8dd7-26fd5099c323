package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

import com.bitwig.dawproject.device.Device;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Validator for plugin compatibility and availability.
 * Checks plugin formats, IDs, and potential compatibility issues.
 * 
 * <AUTHOR> ProjectConverter
 */
public class PluginValidator implements Validator<Device>
{
    private static final Pattern VST2_ID_PATTERN = Pattern.compile("^[A-Za-z0-9]{4}$");
    private static final Pattern VST3_ID_PATTERN = Pattern.compile("^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$");
    private static final Pattern CLAP_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");
    
    private final Set<String> supportedFormats;
    private final Map<String, PluginInfo> knownPlugins;
    private final boolean checkAvailability;
    
    /**
     * Constructor.
     * 
     * @param supportedFormats Set of supported plugin formats
     * @param knownPlugins Map of known plugin information
     * @param checkAvailability Whether to check plugin availability
     */
    public PluginValidator(final Set<String> supportedFormats, 
                          final Map<String, PluginInfo> knownPlugins,
                          final boolean checkAvailability)
    {
        this.supportedFormats = supportedFormats;
        this.knownPlugins = knownPlugins != null ? knownPlugins : new HashMap<>();
        this.checkAvailability = checkAvailability;
    }
    
    @Override
    public List<ValidationResult> validate(final Device device)
    {
        final List<ValidationResult> results = new ArrayList<>();
        
        if (device == null)
        {
            results.add(ValidationResult.error(ErrorCode.PLUGIN_NOT_FOUND, 
                "Device is null", "device", null));
            return results;
        }
        
        // Validate device ID
        validateDeviceId(device, results);
        
        // Validate device name
        validateDeviceName(device, results);
        
        // Validate plugin format
        validatePluginFormat(device, results);
        
        // Validate plugin state
        validatePluginState(device, results);
        
        // Check plugin availability
        if (this.checkAvailability)
        {
            validatePluginAvailability(device, results);
        }
        
        // Check for known compatibility issues
        validateCompatibility(device, results);
        
        return results;
    }
    
    private void validateDeviceId(final Device device, final List<ValidationResult> results)
    {
        if (device.deviceID == null || device.deviceID.trim().isEmpty())
        {
            results.add(ValidationResult.error(ErrorCode.PLUGIN_NOT_FOUND, 
                "Device has no ID", "deviceID", null));
            return;
        }
        
        final String deviceId = device.deviceID.trim();
        final PluginFormat format = determinePluginFormat(device);
        
        // Validate ID format based on plugin type
        switch (format)
        {
            case VST2:
                if (!VST2_ID_PATTERN.matcher(deviceId).matches())
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Invalid VST2 plugin ID format: " + deviceId, 
                        "deviceID", deviceId,
                        "VST2 IDs should be 4-character strings"));
                }
                break;
                
            case VST3:
                if (!VST3_ID_PATTERN.matcher(deviceId).matches())
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Invalid VST3 plugin ID format: " + deviceId, 
                        "deviceID", deviceId,
                        "VST3 IDs should be UUIDs in standard format"));
                }
                break;
                
            case CLAP:
                if (!CLAP_ID_PATTERN.matcher(deviceId).matches())
                {
                    results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                        "Invalid CLAP plugin ID format: " + deviceId, 
                        "deviceID", deviceId,
                        "CLAP IDs should contain only alphanumeric characters, dots, underscores, and hyphens"));
                }
                break;
                
            case UNKNOWN:
                results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                    "Unknown plugin format for device: " + deviceId, 
                    "deviceID", deviceId,
                    "Plugin may not be supported in target format"));
                break;
        }
    }
    
    private void validateDeviceName(final Device device, final List<ValidationResult> results)
    {
        if (device.deviceName == null || device.deviceName.trim().isEmpty())
        {
            results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                "Device has no name", "deviceName", null,
                "Add device name for better identification"));
        }
        else
        {
            final String name = device.deviceName.trim();
            if (name.length() > 255)
            {
                results.add(ValidationResult.warning(ErrorCode.INVALID_PROJECT_FORMAT, 
                    "Device name is very long: " + name.length() + " characters", 
                    "deviceName", name,
                    "Consider shortening the device name"));
            }
        }
    }
    
    private void validatePluginFormat(final Device device, final List<ValidationResult> results)
    {
        final PluginFormat format = determinePluginFormat(device);
        final String formatName = format.name();
        
        if (format == PluginFormat.UNKNOWN)
        {
            results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                "Unknown plugin format", "format", formatName,
                "Plugin may not be supported"));
        }
        else if (!this.supportedFormats.contains(formatName))
        {
            results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                String.format("Plugin format '%s' may not be supported", formatName), 
                "format", formatName,
                "Supported formats: " + this.supportedFormats));
        }
    }
    
    private void validatePluginState(final Device device, final List<ValidationResult> results)
    {
        if (device.state != null)
        {
            if (device.state.path == null || device.state.path.trim().isEmpty())
            {
                results.add(ValidationResult.warning(ErrorCode.MISSING_REQUIRED_DATA, 
                    "Device state has no path", "state.path", null,
                    "Plugin state may not be preserved"));
            }
            
            // Check state size (if available)
            if (device.state.path != null)
            {
                final String path = device.state.path;
                if (path.length() > 10000) // Arbitrary large size check
                {
                    results.add(ValidationResult.info(ErrorCode.PERFORMANCE_WARNING, 
                        "Device state is very large", 
                        "Consider optimizing plugin presets"));
                }
            }
        }
        else
        {
            results.add(ValidationResult.info(ErrorCode.MISSING_REQUIRED_DATA, 
                "Device has no state information", 
                "Plugin will load with default settings"));
        }
    }
    
    private void validatePluginAvailability(final Device device, final List<ValidationResult> results)
    {
        if (device.deviceID == null)
            return;
            
        final String deviceId = device.deviceID.trim();
        final PluginInfo pluginInfo = this.knownPlugins.get(deviceId);
        
        if (pluginInfo == null)
        {
            results.add(ValidationResult.warning(ErrorCode.PLUGIN_NOT_FOUND, 
                "Plugin not found in known plugins database: " + deviceId, 
                "availability", false,
                "Plugin may not be available on target system"));
        }
        else
        {
            // Check if plugin is deprecated
            if (pluginInfo.isDeprecated())
            {
                results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                    String.format("Plugin '%s' is deprecated", pluginInfo.getName()), 
                    "deprecated", true,
                    "Consider updating to a newer version: " + pluginInfo.getReplacementSuggestion()));
            }
            
            // Check version compatibility
            if (pluginInfo.getMinVersion() != null && device.deviceVersion != null)
            {
                if (compareVersions(device.deviceVersion, pluginInfo.getMinVersion()) < 0)
                {
                    results.add(ValidationResult.warning(ErrorCode.UNSUPPORTED_VERSION, 
                        String.format("Plugin version '%s' may be too old (minimum: %s)", 
                                    device.deviceVersion, pluginInfo.getMinVersion()), 
                        "version", device.deviceVersion,
                        "Update plugin to version " + pluginInfo.getMinVersion() + " or later"));
                }
            }
        }
    }
    
    private void validateCompatibility(final Device device, final List<ValidationResult> results)
    {
        if (device.deviceID == null)
            return;
            
        final String deviceId = device.deviceID.trim();
        final PluginInfo pluginInfo = this.knownPlugins.get(deviceId);
        
        if (pluginInfo != null && pluginInfo.hasKnownIssues())
        {
            for (final String issue : pluginInfo.getKnownIssues())
            {
                results.add(ValidationResult.info(ErrorCode.UNSUPPORTED_FEATURE, 
                    String.format("Known issue with plugin '%s': %s", 
                                pluginInfo.getName(), issue), 
                    "Check plugin documentation for workarounds"));
            }
        }
        
        // Check for common problematic plugin patterns
        if (device.deviceName != null)
        {
            final String name = device.deviceName.toLowerCase();
            if (name.contains("demo") || name.contains("trial"))
            {
                results.add(ValidationResult.info(ErrorCode.UNSUPPORTED_FEATURE, 
                    "Demo/trial plugin detected", 
                    "Full version may be required for proper conversion"));
            }
        }
    }
    
    private PluginFormat determinePluginFormat(final Device device)
    {
        if (device.deviceID == null)
            return PluginFormat.UNKNOWN;
            
        final String deviceId = device.deviceID.trim();
        
        // Check for VST3 UUID format
        if (VST3_ID_PATTERN.matcher(deviceId).matches())
            return PluginFormat.VST3;
            
        // Check for VST2 4-character format
        if (VST2_ID_PATTERN.matcher(deviceId).matches())
            return PluginFormat.VST2;
            
        // Check for CLAP format
        if (CLAP_ID_PATTERN.matcher(deviceId).matches())
            return PluginFormat.CLAP;
            
        // Check device name for format hints
        if (device.deviceName != null)
        {
            final String name = device.deviceName.toLowerCase();
            if (name.contains("vst3"))
                return PluginFormat.VST3;
            if (name.contains("vst"))
                return PluginFormat.VST2;
            if (name.contains("clap"))
                return PluginFormat.CLAP;
        }
        
        return PluginFormat.UNKNOWN;
    }
    
    private int compareVersions(final String version1, final String version2)
    {
        if (version1 == null && version2 == null)
            return 0;
        if (version1 == null)
            return -1;
        if (version2 == null)
            return 1;
            
        final String[] parts1 = version1.split("\\.");
        final String[] parts2 = version2.split("\\.");
        final int maxLength = Math.max(parts1.length, parts2.length);
        
        for (int i = 0; i < maxLength; i++)
        {
            final int part1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
            final int part2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;
            
            if (part1 < part2)
                return -1;
            if (part1 > part2)
                return 1;
        }
        
        return 0;
    }
    
    private int parseVersionPart(final String part)
    {
        try
        {
            return Integer.parseInt(part.replaceAll("[^0-9]", ""));
        }
        catch (final NumberFormatException e)
        {
            return 0;
        }
    }
    
    @Override
    public String getName()
    {
        return "Plugin Validator";
    }
    
    @Override
    public String getDescription()
    {
        return "Validates plugin compatibility, availability, and format compliance";
    }
    
    /**
     * Plugin format enumeration.
     */
    public enum PluginFormat
    {
        VST2, VST3, CLAP, AU, UNKNOWN
    }
    
    /**
     * Information about a known plugin.
     */
    public static class PluginInfo
    {
        private final String name;
        private final String vendor;
        private final String minVersion;
        private final boolean deprecated;
        private final String replacementSuggestion;
        private final List<String> knownIssues;
        
        public PluginInfo(final String name, final String vendor, final String minVersion,
                         final boolean deprecated, final String replacementSuggestion,
                         final List<String> knownIssues)
        {
            this.name = name;
            this.vendor = vendor;
            this.minVersion = minVersion;
            this.deprecated = deprecated;
            this.replacementSuggestion = replacementSuggestion;
            this.knownIssues = knownIssues != null ? knownIssues : new ArrayList<>();
        }
        
        public String getName() { return this.name; }
        public String getVendor() { return this.vendor; }
        public String getMinVersion() { return this.minVersion; }
        public boolean isDeprecated() { return this.deprecated; }
        public String getReplacementSuggestion() { return this.replacementSuggestion; }
        public List<String> getKnownIssues() { return this.knownIssues; }
        public boolean hasKnownIssues() { return !this.knownIssues.isEmpty(); }
    }
    
    /**
     * Create a default plugin validator.
     * 
     * @return Default plugin validator
     */
    public static PluginValidator createDefault()
    {
        final Set<String> supportedFormats = Set.of("VST2", "VST3", "CLAP");
        return new PluginValidator(supportedFormats, new HashMap<>(), false);
    }
}
