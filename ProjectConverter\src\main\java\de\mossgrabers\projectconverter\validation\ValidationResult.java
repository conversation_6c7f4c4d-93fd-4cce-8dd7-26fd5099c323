package de.mossgrabers.projectconverter.validation;

import de.mossgrabers.projectconverter.core.ErrorCode;

/**
 * Result of a validation operation.
 * 
 * <AUTHOR> ProjectConverter
 */
public class ValidationResult
{
    private final ValidationSeverity severity;
    private final ErrorCode errorCode;
    private final String message;
    private final String fieldName;
    private final Object invalidValue;
    private final String suggestion;
    
    /**
     * Constructor for validation result.
     * 
     * @param severity The severity of the validation issue
     * @param errorCode The error code
     * @param message The validation message
     * @param fieldName The field that failed validation
     * @param invalidValue The invalid value
     * @param suggestion Suggestion for fixing the issue
     */
    public ValidationResult(final ValidationSeverity severity, final ErrorCode errorCode,
                          final String message, final String fieldName, 
                          final Object invalidValue, final String suggestion)
    {
        this.severity = severity;
        this.errorCode = errorCode;
        this.message = message;
        this.fieldName = fieldName;
        this.invalidValue = invalidValue;
        this.suggestion = suggestion;
    }
    
    /**
     * Create an error validation result.
     * 
     * @param errorCode The error code
     * @param message The error message
     * @param fieldName The field name
     * @param invalidValue The invalid value
     * @return The validation result
     */
    public static ValidationResult error(final ErrorCode errorCode, final String message,
                                       final String fieldName, final Object invalidValue)
    {
        return new ValidationResult(ValidationSeverity.ERROR, errorCode, message, 
                                  fieldName, invalidValue, null);
    }
    
    /**
     * Create a warning validation result.
     * 
     * @param errorCode The error code
     * @param message The warning message
     * @param fieldName The field name
     * @param invalidValue The invalid value
     * @param suggestion Suggestion for improvement
     * @return The validation result
     */
    public static ValidationResult warning(final ErrorCode errorCode, final String message,
                                         final String fieldName, final Object invalidValue,
                                         final String suggestion)
    {
        return new ValidationResult(ValidationSeverity.WARNING, errorCode, message, 
                                  fieldName, invalidValue, suggestion);
    }
    
    /**
     * Create an info validation result.
     * 
     * @param errorCode The error code
     * @param message The info message
     * @param suggestion Suggestion for improvement
     * @return The validation result
     */
    public static ValidationResult info(final ErrorCode errorCode, final String message,
                                      final String suggestion)
    {
        return new ValidationResult(ValidationSeverity.INFO, errorCode, message, 
                                  null, null, suggestion);
    }
    
    /**
     * Get the severity.
     * 
     * @return The severity
     */
    public ValidationSeverity getSeverity()
    {
        return this.severity;
    }
    
    /**
     * Get the error code.
     * 
     * @return The error code
     */
    public ErrorCode getErrorCode()
    {
        return this.errorCode;
    }
    
    /**
     * Get the message.
     * 
     * @return The message
     */
    public String getMessage()
    {
        return this.message;
    }
    
    /**
     * Get the field name.
     * 
     * @return The field name
     */
    public String getFieldName()
    {
        return this.fieldName;
    }
    
    /**
     * Get the invalid value.
     * 
     * @return The invalid value
     */
    public Object getInvalidValue()
    {
        return this.invalidValue;
    }
    
    /**
     * Get the suggestion.
     * 
     * @return The suggestion
     */
    public String getSuggestion()
    {
        return this.suggestion;
    }
    
    /**
     * Check if this is an error result.
     * 
     * @return True if error
     */
    public boolean isError()
    {
        return this.severity == ValidationSeverity.ERROR;
    }
    
    /**
     * Check if this is a warning result.
     * 
     * @return True if warning
     */
    public boolean isWarning()
    {
        return this.severity == ValidationSeverity.WARNING;
    }
    
    @Override
    public String toString()
    {
        final StringBuilder sb = new StringBuilder();
        sb.append("[").append(this.severity).append("] ");
        sb.append(this.message);
        if (this.fieldName != null)
        {
            sb.append(" (Field: ").append(this.fieldName).append(")");
        }
        if (this.suggestion != null)
        {
            sb.append(" - Suggestion: ").append(this.suggestion);
        }
        return sb.toString();
    }
}
