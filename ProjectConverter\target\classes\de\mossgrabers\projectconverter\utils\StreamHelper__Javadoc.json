{"doc": "\n Helper functions for input and output streams.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "readIntBigEndian", "paramTypes": ["java.io.InputStream"], "doc": "\n Reads a 4-byte integer in little endian format (LSB first).\r\n\r\n @param input The output stream\r\n @return The integer value\r\n @throws IOException Stream error\r\n"}, {"name": "readIntLittleEndian", "paramTypes": ["java.io.InputStream"], "doc": "\n Reads a 4-byte integer in little endian format (LSB first).\r\n\r\n @param input The output stream\r\n @return The integer value\r\n @throws IOException Stream error\r\n"}, {"name": "readLongLittleEndian", "paramTypes": ["java.io.InputStream"], "doc": "\n Reads a 4-byte integer in little endian format (LSB first).\r\n\r\n @param input The output stream\r\n @return The integer value\r\n @throws IOException Stream error\r\n"}, {"name": "writeIntBigEndian", "paramTypes": ["java.io.OutputStream", "int"], "doc": "\n Writes a 4-byte integer in big endian format (MSB first).\r\n\r\n @param output The output stream\r\n @param value The integer value\r\n @throws IOException Stream error\r\n"}, {"name": "writeIntLittleEndian", "paramTypes": ["java.io.OutputStream", "int"], "doc": "\n Writes a 4-byte integer in little endian format (LSB first).\r\n\r\n @param output The output stream\r\n @param value The integer value\r\n @throws IOException Stream error\r\n"}, {"name": "readString", "paramTypes": ["java.io.InputStream"], "doc": "\n Reads a null terminated ASCII text.\r\n\r\n @param input The input from which to read\r\n @return The text\r\n @throws IOException Could not read the text\r\n"}, {"name": "readString", "paramTypes": ["java.io.InputStream", "int"], "doc": "\n Reads a terminated ASCII text with a fixed number of characters.\r\n\r\n @param input The input from which to read\r\n @param length The number of characters (bytes) to read\r\n @return The text\r\n @throws IOException Could not read the text\r\n"}, {"name": "writeString", "paramTypes": ["java.io.OutputStream", "java.lang.String"], "doc": "\n Write an ASCII text to the output stream.\r\n\r\n @param output The output stream\r\n @param text The text to write\r\n @throws IOException Could not write\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Private due to helper class.\r\n"}]}