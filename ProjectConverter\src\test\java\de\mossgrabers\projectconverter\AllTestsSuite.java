package de.mossgrabers.projectconverter;

import de.mossgrabers.projectconverter.core.ErrorHandlerTest;
import de.mossgrabers.projectconverter.integration.ConversionIntegrationTest;
import de.mossgrabers.projectconverter.ui.ProjectConverterAppTest;
import de.mossgrabers.projectconverter.validation.FileValidatorTest;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * Test suite that runs all ProjectConverter tests.
 * Organizes tests into logical groups for comprehensive testing.
 * 
 * <AUTHOR> ProjectConverter
 */
@Suite
@SuiteDisplayName("ProjectConverter Test Suite")
@SelectClasses({
    // Unit Tests
    ErrorHandlerTest.class,
    FileValidatorTest.class,
    
    // Integration Tests
    ConversionIntegrationTest.class,
    
    // GUI Tests
    ProjectConverterAppTest.class
})
public class AllTestsSuite
{
    // This class serves as a test suite runner
    // Individual test classes are specified in the @SelectClasses annotation
}
