{"doc": " Represents a UI theme with styling information.\n Contains theme metadata and CSS resource paths.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getName", "paramTypes": [], "doc": " Get the theme name.\n \n @return The theme name\n"}, {"name": "getDescription", "paramTypes": [], "doc": " Get the theme description.\n \n @return The theme description\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>t<PERSON>ath", "paramTypes": [], "doc": " Get the stylesheet path.\n \n @return The path to the CSS stylesheet\n"}, {"name": "isDark", "paramTypes": [], "doc": " Check if this is a dark theme.\n \n @return True if this is a dark theme\n"}, {"name": "getIconSet", "paramTypes": [], "doc": " Get the icon set identifier.\n \n @return The icon set identifier\n"}, {"name": "getAccentColor", "paramTypes": [], "doc": " Get the accent color.\n \n @return The accent color as a CSS color string\n"}, {"name": "getPrimaryTextColor", "paramTypes": [], "doc": " Get the primary text color for this theme.\n \n @return The primary text color\n"}, {"name": "getSecondaryTextColor", "paramTypes": [], "doc": " Get the secondary text color for this theme.\n \n @return The secondary text color\n"}, {"name": "getBackgroundColor", "paramTypes": [], "doc": " Get the background color for this theme.\n \n @return The background color\n"}, {"name": "getSurfaceColor", "paramTypes": [], "doc": " Get the surface color for this theme.\n \n @return The surface color\n"}, {"name": "getBorderColor", "paramTypes": [], "doc": " Get the border color for this theme.\n \n @return The border color\n"}, {"name": "getHoverColor", "paramTypes": [], "doc": " Get the hover color for this theme.\n \n @return The hover color\n"}, {"name": "getSelectionColor", "paramTypes": [], "doc": " Get the selection color for this theme.\n \n @return The selection color\n"}, {"name": "getErrorColor", "paramTypes": [], "doc": " Get the error color for this theme.\n \n @return The error color\n"}, {"name": "getWarningColor", "paramTypes": [], "doc": " Get the warning color for this theme.\n \n @return The warning color\n"}, {"name": "getSuccessColor", "paramTypes": [], "doc": " Get the success color for this theme.\n \n @return The success color\n"}, {"name": "getInfoColor", "paramTypes": [], "doc": " Get the info color for this theme.\n \n @return The info color\n"}, {"name": "toCssVariables", "paramTypes": [], "doc": " Create a CSS variable declaration for this theme.\n \n @return CSS variable declarations\n"}, {"name": "createLightTheme", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create a light theme variant.\n \n @param name The theme name\n @param stylesheetPath The stylesheet path\n @return A light theme\n"}, {"name": "createDarkTheme", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " Create a dark theme variant.\n \n @param name The theme name\n @param stylesheetPath The stylesheet path\n @return A dark theme\n"}, {"name": "createHighContrastTheme", "paramTypes": ["java.lang.String", "java.lang.String", "boolean"], "doc": " Create a high contrast theme variant.\n \n @param name The theme name\n @param stylesheetPath The stylesheet path\n @param dark Whether this is a dark high contrast theme\n @return A high contrast theme\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "boolean"], "doc": " Constructor with basic theme information.\n \n @param name The theme name\n @param description The theme description\n @param stylesheetPath The path to the CSS stylesheet\n @param dark Whether this is a dark theme\n"}, {"name": "<init>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.lang.String", "java.lang.String"], "doc": " Constructor with full theme information.\n \n @param name The theme name\n @param description The theme description\n @param stylesheetPath The path to the CSS stylesheet\n @param dark Whether this is a dark theme\n @param iconSet The icon set identifier\n @param accentColor The accent color for the theme\n"}]}