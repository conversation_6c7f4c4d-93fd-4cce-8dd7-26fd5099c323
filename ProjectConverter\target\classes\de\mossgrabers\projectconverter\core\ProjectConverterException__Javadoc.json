{"doc": " Base exception class for all ProjectConverter-specific exceptions.\n Provides enhanced error handling with error codes and recovery suggestions.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getErrorCode", "paramTypes": [], "doc": " Get the error code.\n \n @return The error code\n"}, {"name": "getUserMessage", "paramTypes": [], "doc": " Get the user-friendly message.\n \n @return The user message\n"}, {"name": "getTechnicalDetails", "paramTypes": [], "doc": " Get the technical details.\n \n @return The technical details\n"}, {"name": "isRecoverable", "paramTypes": [], "doc": " Check if the error is recoverable.\n \n @return True if recoverable\n"}, {"name": "getFormattedMessage", "paramTypes": [], "doc": " Get a formatted error message for logging.\n \n @return Formatted error message\n"}], "constructors": [{"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "boolean"], "doc": " Constructor with error code.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param technicalDetails Technical details for debugging\n @param recoverable Whether the error is recoverable\n"}, {"name": "<init>", "paramTypes": ["de.mossgrabers.projectconverter.core.ErrorCode", "java.lang.String", "java.lang.String", "boolean", "java.lang.Throwable"], "doc": " Constructor with error code and cause.\n \n @param errorCode The error code\n @param userMessage User-friendly error message\n @param technicalDetails Technical details for debugging\n @param recoverable Whether the error is recoverable\n @param cause The underlying cause\n"}]}