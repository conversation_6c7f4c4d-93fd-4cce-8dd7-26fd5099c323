package de.mossgrabers.projectconverter.gui.themes;

import java.util.Objects;

/**
 * Represents a UI theme with styling information.
 * Contains theme metadata and CSS resource paths.
 * 
 * <AUTHOR> ProjectConverter
 */
public class Theme
{
    private final String name;
    private final String description;
    private final String stylesheetPath;
    private final boolean dark;
    private final String iconSet;
    private final String accentColor;
    
    /**
     * Constructor with basic theme information.
     * 
     * @param name The theme name
     * @param description The theme description
     * @param stylesheetPath The path to the CSS stylesheet
     * @param dark Whether this is a dark theme
     */
    public Theme(final String name, final String description, final String stylesheetPath, final boolean dark)
    {
        this(name, description, stylesheetPath, dark, null, null);
    }
    
    /**
     * Constructor with full theme information.
     * 
     * @param name The theme name
     * @param description The theme description
     * @param stylesheetPath The path to the CSS stylesheet
     * @param dark Whether this is a dark theme
     * @param iconSet The icon set identifier
     * @param accentColor The accent color for the theme
     */
    public Theme(final String name, final String description, final String stylesheetPath, 
                final boolean dark, final String iconSet, final String accentColor)
    {
        this.name = Objects.requireNonNull(name, "Theme name cannot be null");
        this.description = description != null ? description : "";
        this.stylesheetPath = Objects.requireNonNull(stylesheetPath, "Stylesheet path cannot be null");
        this.dark = dark;
        this.iconSet = iconSet != null ? iconSet : (dark ? "dark" : "light");
        this.accentColor = accentColor != null ? accentColor : (dark ? "#0078d4" : "#0066cc");
    }
    
    /**
     * Get the theme name.
     * 
     * @return The theme name
     */
    public String getName()
    {
        return this.name;
    }
    
    /**
     * Get the theme description.
     * 
     * @return The theme description
     */
    public String getDescription()
    {
        return this.description;
    }
    
    /**
     * Get the stylesheet path.
     * 
     * @return The path to the CSS stylesheet
     */
    public String getStylesheetPath()
    {
        return this.stylesheetPath;
    }
    
    /**
     * Check if this is a dark theme.
     * 
     * @return True if this is a dark theme
     */
    public boolean isDark()
    {
        return this.dark;
    }
    
    /**
     * Get the icon set identifier.
     * 
     * @return The icon set identifier
     */
    public String getIconSet()
    {
        return this.iconSet;
    }
    
    /**
     * Get the accent color.
     * 
     * @return The accent color as a CSS color string
     */
    public String getAccentColor()
    {
        return this.accentColor;
    }
    
    /**
     * Get the primary text color for this theme.
     * 
     * @return The primary text color
     */
    public String getPrimaryTextColor()
    {
        return this.dark ? "#ffffff" : "#000000";
    }
    
    /**
     * Get the secondary text color for this theme.
     * 
     * @return The secondary text color
     */
    public String getSecondaryTextColor()
    {
        return this.dark ? "#cccccc" : "#666666";
    }
    
    /**
     * Get the background color for this theme.
     * 
     * @return The background color
     */
    public String getBackgroundColor()
    {
        return this.dark ? "#2d2d30" : "#ffffff";
    }
    
    /**
     * Get the surface color for this theme.
     * 
     * @return The surface color
     */
    public String getSurfaceColor()
    {
        return this.dark ? "#3c3c3c" : "#f5f5f5";
    }
    
    /**
     * Get the border color for this theme.
     * 
     * @return The border color
     */
    public String getBorderColor()
    {
        return this.dark ? "#555555" : "#cccccc";
    }
    
    /**
     * Get the hover color for this theme.
     * 
     * @return The hover color
     */
    public String getHoverColor()
    {
        return this.dark ? "#404040" : "#e0e0e0";
    }
    
    /**
     * Get the selection color for this theme.
     * 
     * @return The selection color
     */
    public String getSelectionColor()
    {
        return this.accentColor;
    }
    
    /**
     * Get the error color for this theme.
     * 
     * @return The error color
     */
    public String getErrorColor()
    {
        return this.dark ? "#f48771" : "#d32f2f";
    }
    
    /**
     * Get the warning color for this theme.
     * 
     * @return The warning color
     */
    public String getWarningColor()
    {
        return this.dark ? "#ffb74d" : "#f57c00";
    }
    
    /**
     * Get the success color for this theme.
     * 
     * @return The success color
     */
    public String getSuccessColor()
    {
        return this.dark ? "#81c784" : "#388e3c";
    }
    
    /**
     * Get the info color for this theme.
     * 
     * @return The info color
     */
    public String getInfoColor()
    {
        return this.dark ? "#64b5f6" : "#1976d2";
    }
    
    /**
     * Create a CSS variable declaration for this theme.
     * 
     * @return CSS variable declarations
     */
    public String toCssVariables()
    {
        return String.format(
            ":root {\n" +
            "  --theme-name: '%s';\n" +
            "  --theme-dark: %s;\n" +
            "  --accent-color: %s;\n" +
            "  --primary-text-color: %s;\n" +
            "  --secondary-text-color: %s;\n" +
            "  --background-color: %s;\n" +
            "  --surface-color: %s;\n" +
            "  --border-color: %s;\n" +
            "  --hover-color: %s;\n" +
            "  --selection-color: %s;\n" +
            "  --error-color: %s;\n" +
            "  --warning-color: %s;\n" +
            "  --success-color: %s;\n" +
            "  --info-color: %s;\n" +
            "}",
            this.name,
            this.dark,
            this.accentColor,
            getPrimaryTextColor(),
            getSecondaryTextColor(),
            getBackgroundColor(),
            getSurfaceColor(),
            getBorderColor(),
            getHoverColor(),
            getSelectionColor(),
            getErrorColor(),
            getWarningColor(),
            getSuccessColor(),
            getInfoColor()
        );
    }
    
    @Override
    public boolean equals(final Object obj)
    {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        final Theme theme = (Theme) obj;
        return this.dark == theme.dark &&
               Objects.equals(this.name, theme.name) &&
               Objects.equals(this.stylesheetPath, theme.stylesheetPath);
    }
    
    @Override
    public int hashCode()
    {
        return Objects.hash(this.name, this.stylesheetPath, this.dark);
    }
    
    @Override
    public String toString()
    {
        return String.format("Theme{name='%s', dark=%s, stylesheet='%s'}", 
                           this.name, this.dark, this.stylesheetPath);
    }
    
    /**
     * Create a light theme variant.
     * 
     * @param name The theme name
     * @param stylesheetPath The stylesheet path
     * @return A light theme
     */
    public static Theme createLightTheme(final String name, final String stylesheetPath)
    {
        return new Theme(name, name + " light theme", stylesheetPath, false);
    }
    
    /**
     * Create a dark theme variant.
     * 
     * @param name The theme name
     * @param stylesheetPath The stylesheet path
     * @return A dark theme
     */
    public static Theme createDarkTheme(final String name, final String stylesheetPath)
    {
        return new Theme(name, name + " dark theme", stylesheetPath, true);
    }
    
    /**
     * Create a high contrast theme variant.
     * 
     * @param name The theme name
     * @param stylesheetPath The stylesheet path
     * @param dark Whether this is a dark high contrast theme
     * @return A high contrast theme
     */
    public static Theme createHighContrastTheme(final String name, final String stylesheetPath, final boolean dark)
    {
        return new Theme(name, name + " high contrast theme", stylesheetPath, dark);
    }
}
