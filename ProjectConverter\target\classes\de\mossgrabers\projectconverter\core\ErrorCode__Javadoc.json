{"doc": " Enumeration of error codes for the ProjectConverter application.\n Each error code includes a category, severity, and description.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [], "methods": [{"name": "getCode", "paramTypes": [], "doc": " Get the numeric error code.\n \n @return The error code\n"}, {"name": "getSeverity", "paramTypes": [], "doc": " Get the error severity.\n \n @return The severity\n"}, {"name": "getDescription", "paramTypes": [], "doc": " Get the error description.\n \n @return The description\n"}, {"name": "getCategory", "paramTypes": [], "doc": " Get the error category based on the code range.\n \n @return The error category\n"}], "constructors": [{"name": "<init>", "paramTypes": ["int", "de.mossgrabers.projectconverter.core.ErrorSeverity", "java.lang.String"], "doc": " Constructor.\n \n @param code The numeric error code\n @param severity The error severity\n @param description The error description\n"}]}