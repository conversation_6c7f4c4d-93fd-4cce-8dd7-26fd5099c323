{"doc": " Enumeration of error severity levels.\n \n <AUTHOR> ProjectConverter\n", "fields": [], "enumConstants": [{"name": "INFO", "doc": "Informational message - no action required "}, {"name": "WARNING", "doc": "Warning - operation can continue but user should be aware "}, {"name": "ERROR", "doc": "Error - operation cannot continue "}, {"name": "CRITICAL", "doc": "Critical error - application may be unstable "}], "methods": [], "constructors": []}